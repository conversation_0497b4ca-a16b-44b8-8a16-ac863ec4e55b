{"_id": "is-fullwidth-code-point", "_rev": "12-b41eb45da12abc2dbc2319095b3437dd", "name": "is-fullwidth-code-point", "description": "Check if the character represented by a given Unicode code point is fullwidth", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "is-fullwidth-code-point", "version": "1.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-fullwidth-code-point"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "char", "string", "str", "codepoint", "code", "point", "is", "detect", "check"], "dependencies": {"number-is-nan": "^1.0.0"}, "devDependencies": {"ava": "0.0.4", "code-point-at": "^1.0.0"}, "gitHead": "f2152d357f41f82785436d428e4f8ede143b7548", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point", "_id": "is-fullwidth-code-point@1.0.0", "_shasum": "ef9e31386f031a7f0d643af82fde50c457ef00cb", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ef9e31386f031a7f0d643af82fde50c457ef00cb", "tarball": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha512-1pqUqRjkhPJ9miNq9SwMfdvi6lBJcd6eFxvfaivQhaH3SgisfiuudvFntdKOmxuee/77l+FPjKrQjWvmPjWrRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBhb0b+tF5u+eLyOiDCJWf+9vivPAhAjfB63B7nxGGdPAiEA68Z6vda38SbQiDKsCXPOKotDbZ7+ZO60j6dUHZPegXo="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "is-fullwidth-code-point", "version": "2.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "char", "string", "str", "codepoint", "code", "point", "is", "detect", "check"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "e94a78056056c5546f2bf4c4cf812a2163a46dae", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "_id": "is-fullwidth-code-point@2.0.0", "_shasum": "a3b30a5c4f199183167aaab93beefae3ddfb654f", "_from": ".", "_npmVersion": "3.10.7", "_nodeVersion": "4.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a3b30a5c4f199183167aaab93beefae3ddfb654f", "tarball": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSW/cIb2AC9PUmuDvt3vURWZEjYEv+/Z+qXFcaI3He9wIhAJplGEI7JWtosgwWrXJjQD9IYNK5KNVtWFv31JFxRyHm"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/is-fullwidth-code-point-2.0.0.tgz_1474526567505_0.299921662081033"}, "directories": {}}, "3.0.0": {"name": "is-fullwidth-code-point", "version": "3.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check"], "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "gitHead": "80e5e314d86e5f76bd1b0573aa9d33e615a372db", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "_id": "is-fullwidth-code-point@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "shasum": "f116f8064fe90b3f7844a38997c0b75051269f1d", "tarball": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "fileCount": 5, "unpackedSize": 4994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcj1IhCRA9TVsSAnZWagAAXfQQAINQrkV7QJDGobc762SJ\nTQP45A1CPt+R4IrE6kzoZgbu9aVlK/1/ovrehQ3pkUYLdK+FLwkijHHNkP74\ntK/poxvNxDPUQbBUM/j73RmoTUye16j7bLIkoFQ5a9mgnjy35ZPUUARaw9Ad\nXLHa33xcp1v1dzg7fKrRicfqyIbv780zQRnrKCdE1Vw2G/wDO9R2xBbjhsFd\nni8GhNIHcmG9PDcg1B5vlTxrMPfhMPY4mw4j7V9IgDBbFU2r2L9hGVJYIa6s\nHDiQJYUeqyh/pKlvjI6O/GQPE+AB8IduBxMk+hRHaONTIX/F3RPFmkbwBpTW\nqF6yfoC7roALJrxeJwRooir4h7rChPXLf2UfzDMXYbo/srJ62Gw9ZYnYk/KQ\nA0Ve8GMXiPw3g9ow882INpTGRfql2szTuv8Lmz/vLW1XzcS+AewI72LKM+Kw\nRDwIVsjlORm836UKa8Or6JoGA1jBxIde+ETNXez9c8zAHr4O3zg+ySuAEN2S\nzswpng5xcjhPrgXp2OgkBG8LJBnvPNa2fAT5UPuEcMZhUxHNezSNDIih6weX\nb1YgjtrDOh5qIHaLbR9BJBYC6Vk+gvTK2m6/vDLqmH8P/cvvFkCX2qKgV3wC\n3vPowzeDopsNnA6YvIztI/uk/KV4B1i3Y5eFaylbzqUt23OPf1RrbzL9E0q3\ngPCe\r\n=DL3r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFqdK0e+RN4xM+7IrAyYmqvTaNg8MFEzwF42BtAoAgitAiEA/9YISFLSR/ut9zSkO4XG67As7nGNUWRBkhM21Q+8foU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-fullwidth-code-point_3.0.0_1552896544852_0.7785122753639435"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "is-fullwidth-code-point", "version": "4.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "27f5728857a28f9893de14be33e893d33da7f85b", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "_id": "is-fullwidth-code-point@4.0.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==", "shasum": "fae3167c729e7463f8461ce512b080a49268aa88", "tarball": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-4.0.0.tgz", "fileCount": 5, "unpackedSize": 5189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeSVjCRA9TVsSAnZWagAAfy0P/ir85rQxo4H+S/uoWB08\n/+OnsqS78NBu1JI12piqD7cO3uUfsgvnlNiLAJtth0QaMH39ePBGoPW4LY15\npMyqEZzRRWb6qrOI8RRPBMFfKbVWsvsOyQZaOqzRxLN9kim0zYIOQ4hC9Cz0\nfKTW0ADA2xAlW4WCf7dG0YVBLMIobL//aP6nu1OfJ1OoPEbZaLrtVvH2myVj\nAtTrBkQ1glEQZqBN8ZJ6DHwyCG+1FXZGwrBxLnRgnVzcgomeLLxM4LHIv4+4\nV7qFP2dTFDkEORjFmMAUvLyao2KPN+bcYVSTzh0Kr5uH/IPHl+bT1to97lWc\n8LemUx36SQL8/FF+WPnTGnD3fLR16uab1ToKFsu9xGTbQhdR1sYHJdsuGjB3\nDZ7fSf+uXlYz7fE/Uf9nJOF4lesg5PKvRSv0zOVtIACawqTdT4uNtlPL1R9l\n6w/GEKXf0A2dDMntU9MgWSvVBRuCKFGt6n8/1NcG7NE+vQViedcdIiQ3IQ11\nhI6xoh6wzxmedunG/i5TLLqD2SRtvKL0oWQxiggjHUBtU56XUR78D1FxKTDd\nIN+g1vJmdbv4NEMD+csIUkU+aYbYfwYIXKbTCzSnELM0YoG5GNLSLK3se5Gd\neQneAVqxzjXrvNh+DOMiLy391H9gTdvB4/+hkOTfi/WHe+HN+AVVTs9O8I+z\njU1o\r\n=bmuQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF613xPXqTxIgznlhMEhBWkAaDNGzRV0aTMYuaVLI8QKAiEA2E5CLITu0wyXxCeNnikhMA87fIyL5H2fF8B1T8g36Qc="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-fullwidth-code-point_4.0.0_1618552163389_0.6209131775447976"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "is-fullwidth-code-point", "version": "5.0.0", "description": "Check if the character represented by a given Unicode code point is fullwidth", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check", "east-asian-width"], "devDependencies": {"ava": "^5.3.1", "tsd": "^0.29.0", "xo": "^0.56.0"}, "dependencies": {"get-east-asian-width": "^1.0.0"}, "types": "./index.d.ts", "gitHead": "1f8fb1ffeab396915a22bf28420f13bf329963fe", "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "_id": "is-fullwidth-code-point@5.0.0", "_nodeVersion": "18.18.2", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-OVa3u9kkBbw7b8Xw5F9P+D/T9X+Z4+JruYVNapTjPYZYUznQ5YfWeFkOj606XYYW8yugTfC8Pj0hYqvi4ryAhA==", "shasum": "9609efced7c2f97da7b60145ef481c787c7ba704", "tarball": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-5.0.0.tgz", "fileCount": 5, "unpackedSize": 3455, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYAxiF1PI4Lsxh7Qine0172AVw/MCM3znOr5hKZpMHcwIhALE6H30F1HwwF4m9P9oc/tgyx6qf3MkQLQcAbPf++u6p"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-fullwidth-code-point_5.0.0_1698499215099_0.8423970946552544"}, "_hasShrinkwrap": false}}, "readme": "# is-fullwidth-code-point\n\n> Check if the character represented by a given [Unicode code point](https://en.wikipedia.org/wiki/Code_point) is [fullwidth](https://en.wikipedia.org/wiki/Halfwidth_and_fullwidth_forms)\n\n## Install\n\n```sh\nnpm install is-fullwidth-code-point\n```\n\n## Usage\n\n```js\nimport isFullwidthCodePoint from 'is-fullwidth-code-point';\n\nisFullwidthCodePoint('谢'.codePointAt(0));\n//=> true\n\nisFullwidthCodePoint('a'.codePointAt(0));\n//=> false\n```\n\n## API\n\n### isFullwidthCodePoint(codePoint)\n\n#### codePoint\n\nType: `number`\n\nThe [code point](https://en.wikipedia.org/wiki/Code_point) of a character.\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-10-28T13:20:15.556Z", "created": "2015-07-16T22:00:18.162Z", "1.0.0": "2015-07-16T22:00:18.162Z", "2.0.0": "2016-09-22T06:42:49.558Z", "3.0.0": "2019-03-18T08:09:05.035Z", "4.0.0": "2021-04-16T05:49:23.535Z", "5.0.0": "2023-10-28T13:20:15.377Z"}, "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "string", "codepoint", "code", "point", "is", "detect", "check", "east-asian-width"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "license": "MIT", "readmeFilename": "readme.md"}