{"_id": "@babel/helper-module-transforms", "_rev": "171-3071184ccf1e4463eedf0fb5859e576d", "name": "@babel/helper-module-transforms", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.3", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "87ee8ab17e83ccab43516a7adfb1d1da9581f148", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.4.tgz", "integrity": "sha512-F2DrQFI8t0EQ21Kbp9PCRCVGilTt7M/3sZZ4nRoBtnehN/DMtcy5tVQIeTBv2qLb8E6epaXUvwuI1LXU4pU7HA==", "signatures": [{"sig": "MEYCIQDnzFFbLMAdWp+2SeZ36892qMKj+oplKpgcSUiFNuNxgQIhANPxXbkngJJxWVOfpzSPCu6xmsu7BPfP/DDox3hhujV2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.4", "@babel/template": "7.0.0-beta.4", "@babel/helper-simple-access": "7.0.0-beta.4", "@babel/helper-module-imports": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.4.tgz_1509388551593_0.8361994007136673", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "9b56cf8b1c13f659b4d08e9e28f3a66783dfd728", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.5.tgz", "integrity": "sha512-dv4pwtOF+jgfS7x78fYu9uJF7RwVEnaQgBjzxSd52rYtYez+ekEdrnClYB+IybiAD4b5uw1Rn79Ptu+mahdufw==", "signatures": [{"sig": "MEYCIQDc5713Lkyg3c1Z9uJRL29631Y/3vfXICm9b4BnfFI1HAIhALQyxkDJEX28XMk2mIjOAh7yPMD9BoqyfiyWQ0BHSvMk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.5", "@babel/template": "7.0.0-beta.5", "@babel/helper-simple-access": "7.0.0-beta.5", "@babel/helper-module-imports": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.5.tgz_1509397050165_0.9182418631389737", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1d5dcb0afa018c727c5dbf6eb358c40998b08b6c", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.31.tgz", "integrity": "sha512-9CMeI+BKFZkOAhgbOPq+ocPC4Aanqfc/JoA7tWhPtmfFAcNcIAT/sg019DHaWmXL4FQcwaeDoi0Sg4ucur6SSQ==", "signatures": [{"sig": "MEUCIAstabC/mvugelVvrIjmZR5F7U1EFElc/FcCwOd+4HE8AiEAy7ZMVbaBRXYCQFHO4J8hQRGMcC7sJpBNy7lbD0oU2kM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.31", "@babel/template": "7.0.0-beta.31", "@babel/helper-simple-access": "7.0.0-beta.31", "@babel/helper-module-imports": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.31.tgz_1509739452846_0.7834649097640067", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6c3e978149f77aafbd05cc1eb687b1893e89d1f5", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.32.tgz", "integrity": "sha512-t1o1EiWVOgB463fplP4QBgDjthQqCWVtOpzp5P5I30FyCUH4XAyNRJyS8+cYxEbGpjM80FKf7IuRmjZhBC8trg==", "signatures": [{"sig": "MEUCICz8fwfbax3eI4p+B0Ew6HUr+OfG7vWM1vXKaVhm0JUwAiEAy9GA9KNhtpuCtASfo57DW+Wo0Lq8XlC93pnpxtTBc68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.32", "@babel/template": "7.0.0-beta.32", "@babel/helper-simple-access": "7.0.0-beta.32", "@babel/helper-module-imports": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.32.tgz_1510493633822_0.6845551105216146", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "515cc4d758dea17d17318c853e830d2ab3ffb60e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.33.tgz", "integrity": "sha512-tmH4NT5Yj6QWEgq9aAfchtusO5R4pkYcIwaWT9iI9R97PReej4/0mMn1j452f0cCnW/OAmpnt74r2qk/JkepcA==", "signatures": [{"sig": "MEYCIQCyxvAorWsIWkboyQaLr5ULjOLqMEt3uUMlu/omoMCksgIhAIRVA+8gLERwjkK1h4uqNhusqsAxx5GyxNTmNlGdbPO7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.33", "@babel/template": "7.0.0-beta.33", "@babel/helper-simple-access": "7.0.0-beta.33", "@babel/helper-module-imports": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.33.tgz_1512138550748_0.6088877227157354", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "69d8ea1adc6f7976ad61a61ef3f92c947bd07018", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.34.tgz", "integrity": "sha512-wkKYPBH8eTwKDBDm24SdBHNh4E+4epY74pKd/+jBaGy/1nxOgPtq7wNVd+8XCHVpxbilvRWFSdDoG/sR/5PDJg==", "signatures": [{"sig": "MEQCIDZRh6q9CII4c5H86yOVUQkTGKFcE2tOTYQukEAxRRi5AiAiowW2mIYxsleZRI+NzfUi1V9edW9q8JlNzt3NFkT6eQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.34", "@babel/template": "7.0.0-beta.34", "@babel/helper-simple-access": "7.0.0-beta.34", "@babel/helper-module-imports": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.34.tgz_1512225607716_0.11336015327833593", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2219dfb9e470704235bbb3477ac63a946a2b3812", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.35.tgz", "integrity": "sha512-2d2wk8WAPv98avaLLn8tUSNilgPDF1On+y50WKZV6rr6tKZwtmotrL7iyIUIYRU1FGfJ5lkh6ApOIghX17ZfGA==", "signatures": [{"sig": "MEUCIQDlZX8fUG5Eb2EpLenFkHTzOFToVTQkO5hjo1lBYyoFtQIgHc4l8LOt72yxzbyXU40/eO8vEPfj/1r5g+NXF1xybl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.35", "@babel/template": "7.0.0-beta.35", "@babel/helper-simple-access": "7.0.0-beta.35", "@babel/helper-module-imports": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.35.tgz_1513288101416_0.6744571314193308", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d88ac39b86d40065add150b71a09543b8daefe5f", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.36.tgz", "integrity": "sha512-M4lyGz8AM7WtCUxX54gB9HuUbY1TJswv0MexoytS2Pj/dMk9OIXN3Mt8rmkRIO+Akq02iTXOtKY8rh+ZfKI3QQ==", "signatures": [{"sig": "MEYCIQD7gt6VYw+5+JgGcumTfHQel9iSvDiPRIX6foDI/jHXwgIhANQWDSao7uhKMA8jIuguy1+WqzcxHiyn/UgygQvurw1l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.36", "@babel/template": "7.0.0-beta.36", "@babel/helper-simple-access": "7.0.0-beta.36", "@babel/helper-module-imports": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.36.tgz_1514228730286_0.4233464861754328", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "df92f844959d89e055f66afac44eb616edbe5173", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.37.tgz", "integrity": "sha512-a8geqXtNquZMMu05Z/H2K9iV4J5rXFQccvrsuTPwZOtj48Y/5Q8uOn7veKs3vZzMZealuLy79j1O9aXBlF7Rwg==", "signatures": [{"sig": "MEYCIQDn3UF6Op59oUamLoAZpehG79Sq2W3xFD8b3QeotHSbtgIhAJXUEM900mpftnuRoDRyI1uY7AX1LaKz/tsRYwbU/2gA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.37", "@babel/template": "7.0.0-beta.37", "@babel/helper-simple-access": "7.0.0-beta.37", "@babel/helper-module-imports": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.37.tgz_1515427416158_0.9816828337498009", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1e2bc59a5daa7a8dd79f65653c594ed5c0c650be", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.38.tgz", "integrity": "sha512-MnluW457L9EAykzbGYTx9mF1SUXO0Brh1UrNSSGu/C+VjMXf4EmQ9RCWOaGT3WCxLVp063xS8ejkdjGwAJqIDw==", "signatures": [{"sig": "MEQCIBqhzhZ0QE1N9BiwJlRU9RFBIJX0qE0yTOLBzTjUxyY1AiAbyIVo38whcRRCld+Ye7nAPkOVQPFQcCHnWyUePoy4AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.38", "@babel/template": "7.0.0-beta.38", "@babel/helper-simple-access": "7.0.0-beta.38", "@babel/helper-module-imports": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.38.tgz_1516206754407_0.788909648777917", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3ebf72bc2cb6453e9c5930a667496bdfa64bcf5e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.39.tgz", "integrity": "sha512-5TsPEI8iTE3VR9aZD4eww78SkqJPIDoItSDZ2p6C1uzLDxZlaUstWK91yRpnuNoI2w0YVGAE27T5soU25o+3Ww==", "signatures": [{"sig": "MEUCIAsbjDQIaT9iYkWbB3wmR3BR74NydHG/YwJ4ON4WvOiSAiEA/kSIK4PNwCtKhxLxrzPc01dbMjgV1GpZOahdCnMzGNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.39", "@babel/template": "7.0.0-beta.39", "@babel/helper-simple-access": "7.0.0-beta.39", "@babel/helper-module-imports": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms-7.0.0-beta.39.tgz_1517344120541_0.8293169280514121", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e5240afd47bd98f6ae65874b9ae508533abfee76", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.40.tgz", "fileCount": 6, "integrity": "sha512-1H7cBk7kUWJpTepPH77TIRGwKILRGpu1yXmz1OjOruR6y2z0qfbp7ZzzZ3/xg6NlLDENLArEyO2+J0mO+VyQsg==", "signatures": [{"sig": "MEUCIBe6eTtRQ6jVY0yhbOte+ejeke5NcqYvJVnGSeM0BswOAiEAowQzy4/l4ByR0xsg9ZDGi4cjKLg9ZhOqZHfIgt66Sw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40784}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.40", "@babel/template": "7.0.0-beta.40", "@babel/helper-simple-access": "7.0.0-beta.40", "@babel/helper-module-imports": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.40_1518453755429_0.5971269811832229", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ecb27cdf7b7ced08bc6d1aa2cf67056a2596650b", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.41.tgz", "fileCount": 6, "integrity": "sha512-MopWq4y+scaEtyMc60wSiVXaqg0UX/K1mzBAEM3ZWSRKreUsFA2xG1yd8elJ+7waolt8jT0+y5MfIxYjh4SmlA==", "signatures": [{"sig": "MEUCIQDgYOoisdRXVy6cW1lcEr8ZZIsZgC9qEjNuMoYNwiN9sQIgDvj0Rlvlexm/MZy6LnspSvxry67iv1LJuJ4BDniMWkM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39796}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.41", "@babel/template": "7.0.0-beta.41", "@babel/helper-simple-access": "7.0.0-beta.41", "@babel/helper-module-imports": "7.0.0-beta.41", "@babel/helper-split-export-declaration": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.41_1521044804273_0.8889412463390685", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "4d260cc786e712e8440bef58dae28040b77a6183", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.42.tgz", "fileCount": 6, "integrity": "sha512-XfCGsf6ijExiXw+oKL1Cp7VJttvgq8qalTGRqz4pviVNEjHU89Pfjsi1K/shdy5x4x+PiTSqn4zZ2PKfVp+vgg==", "signatures": [{"sig": "MEQCIFi/9B7zfayQfJg66w8mqF/qCcoq5RNysjpgUpMRz6ykAiAdgeB0FgqI3G5oKwhv4bogrUhQ8ZlEqGshaYjinlFtNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38753}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.42", "@babel/template": "7.0.0-beta.42", "@babel/helper-simple-access": "7.0.0-beta.42", "@babel/helper-module-imports": "7.0.0-beta.42", "@babel/helper-split-export-declaration": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.42_1521147121362_0.861977897347801", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b2ddc025124550605beb39e67fffce80300b48db", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.43.tgz", "fileCount": 6, "integrity": "sha512-3lneaBxnCTe+wuyQwpnIsvVt6/ENTH00eXN1AuE3EOTjBJJrpFYygU2gDsnf32LDjWQNYyvCodxPOlWLrGfcxw==", "signatures": [{"sig": "MEUCIQC2CJXghcxeQu3YSKVC8J98Z0JoKkm4Q1FC+mNFWQjkMgIgVuIBr5EEHHuIZUdIned3/Qf1qZ99o9UtXCs+TARslmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29562}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.43", "@babel/template": "7.0.0-beta.43", "@babel/helper-simple-access": "7.0.0-beta.43", "@babel/helper-module-imports": "7.0.0-beta.43", "@babel/helper-split-export-declaration": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.43_1522687732383_0.9485351266184039", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "185dc17b37c4b9cc3daee0f0f44e74f000e21bb7", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.44.tgz", "fileCount": 6, "integrity": "sha512-mwoQuzm1xY3L00Rf6vHO0tFKkBxarODf1f5l4wClTzvBmm7ReikPKyNwgS7wp2dzlorpIKPAAw+n3IEhnOjLJg==", "signatures": [{"sig": "MEYCIQCXyDfUSP30RUXCE54Unt2qZjoBsJS/FUv3E0VBH+60GQIhAIE8wgcAHPMAHtYJoQ6txWsPJNuk2237coFrLtsbeMPI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40274}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.44", "@babel/template": "7.0.0-beta.44", "@babel/helper-simple-access": "7.0.0-beta.44", "@babel/helper-module-imports": "7.0.0-beta.44", "@babel/helper-split-export-declaration": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.44_1522707631462_0.6773657619057345", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7114af5621ac4af3aa2e07209d5517f70c78575d", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.45.tgz", "fileCount": 6, "integrity": "sha512-yC3hhWm9uBWPIQ2p2THZ357kVxY9VLza0jFHqV+SVrfaRGA9PlKEmKGhoZOPcoIl+50oYoLoymoPRI6+HSAHcw==", "signatures": [{"sig": "MEYCIQCBcK+l3TdZcBMjuwj1pkGfKrIsMKLdG+vCmK3HIdskFwIhAJW3mRD7o0V4FtZ3SVhuepYHr7Ckac2PEqYQcboKbJOn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T23CRA9TVsSAnZWagAAah4P/3XQoSxS4O9QiLHcuNn6\nszGj5NjLwBGHSEmlq7txb1nc6UdLUW97b0DsfJ7j1YW3+lBijZRFtIRaXII7\nOZF/C0lGXn8VhIoA7SSu65OmlQ9N81RoskSsrk3UrN3OM2qUfD/Uv+BEAV+1\nx3jyq2AeZRYL3E6h9L0WRACmWR2V2oDPzeTi7Stiwqlbteh6py4e4POJkkb0\n/8e3Rt78w3+6/i9TOi41zvkyQKTQqz5Jg/ASGqhNCKIU1jcogMzwUTUtr++A\nEJWPZPLEhigvzG2E6nDYv7Rpl2M6KF84EHZsSBGspZfPbE8jeNToADdan0Ol\nIMk325pQFgnn5YwH9hzRHrPgViTFYn0JgkLE/oQAk+5Fm5fJEKHXRsNyMuVd\nrmQwecnriGmL6fU+46+69S6v0f+S3yxLLEXBxpN5EmgHwZQrgfLLKut7XC6Q\n+PfkGDY00LbLktyxbbVWzkoP54f1S6BJl5pbGV+fsMtefVrDSV9tPVh1Getn\nFQvddCDxMwTDcV8yEIEx+c17djRAy6vvLq/GMASOCFJtnML0d/STr1sIoYlx\nKtRY/IEAJ9rin1cishJPchG9aa/YiKMyYypAT3/d2YK8NDa4dmiofPhWp8wn\nJVqKSdGhELiwF1SDEdnY1jLeSIxQiUGMq47BEJ5RYg/4bhTYvm9kUzx+mMGg\nLZHl\r\n=rXWL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.45", "@babel/template": "7.0.0-beta.45", "@babel/helper-simple-access": "7.0.0-beta.45", "@babel/helper-module-imports": "7.0.0-beta.45", "@babel/helper-split-export-declaration": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.45_1524448694905_0.1825174854640963", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "90ad981f3a0020d9a8e526296555a5dd7e87cf5e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.46.tgz", "fileCount": 6, "integrity": "sha512-<PERSON><PERSON>oWSub3PHNvkWcUEWfKBe8pFUdMhsZMFDcaovcLb+gfxL/zZhQYwedKKKwbzVGIk9k44yjeMQ/OJd4yt4FGQ==", "signatures": [{"sig": "MEUCIAgHMBS3z1giOG6JaGONqewkrXX1EOgHkyPzNdLTCBJ9AiEA7xCavjqS17KK9O/FiPovLWfTemrQvLXIf/wLEaRmRWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHhCRA9TVsSAnZWagAAKOEP/Ra3FaX33Y5UMABaemGP\njmSV0HdREzKTYa1SdETAFWSav/kHVXwQwj5JDGtPqZ/1qqKNBemxmS/qe4fN\niWuaUSW8jiwRqYQkp9PFh7u+pxw2HQbC0LbuK5jEpy3aQPfRjEahc/yTe82w\nLpssLga/WWQvLG1iTuum7hrVVmwf4xC/KcwQ9nA16DCmMxnJTZjoQrykqezr\nKAHuADABpYkTl8sISOmQhpf1n3V4soysrzlUGa33sGngxOP1n8Wm+BahF/UB\n8tuTZ89zapcFvII+RaOWEqL+qWiKdc77JDLI0jcRmqe2ty5+17LZj9dtrfaZ\nQZYHNaBMybSYBHGlzIB3QPVjetlGFbxDc5s2avwiddt7wrTnCxjbiijLmcDX\nVInbrboH/Nv3Y2EHI4EilElnsYHkYwPDg+HvZFjl4VTFP+549rNmam/KxsaL\nDPgPGtvzPsJb/crPiPQjztZeCEpCMd1tBUN5KzXgi1qJ9d6EK8sSXY4P7REg\npEnQlSr7U4ATpnCsI0FSImG8yynnVFNT6+xmyC2cIMH1PHsAwqbt5DIKo0b8\nKmwwsP5Y45n+hb+k2ikXCt2fMPiBu3IkkA6tibyj6L1yVFGf04XRAMIndRCE\nbqlN7VSWR7C/PYC0seUzpmZiQZqlRLUd0fGGinA+eia9qDFlck8NXExyX2zi\nBBsF\r\n=hQS2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.46", "@babel/template": "7.0.0-beta.46", "@babel/helper-simple-access": "7.0.0-beta.46", "@babel/helper-module-imports": "7.0.0-beta.46", "@babel/helper-split-export-declaration": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.46_1524457952976_0.15341997400716578", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7eff91fc96873bd7b8d816698f1a69bbc01f3c38", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.47.tgz", "fileCount": 6, "integrity": "sha512-CziMe30ZunAhe6j05oNOFOg7im1lcv3dYuMxrwBYVe9YdP4NHPU7a1wrDBUhaPmyqTIZDwGnFne7k1KP79SeGQ==", "signatures": [{"sig": "MEUCIQCcxXRVWT5unvnPCY1nKjiUU33ts85WU7brkji2p85cpAIgAhphfBp3HryMvp8pT27FUTx2tIhsDfhb0eBWbiEyrGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+icfCRA9TVsSAnZWagAATb4QAIJGro8KyejWetbMTs91\nD8NNxLqCTcCHXq9wNrbhXTLQdqhcZPti9k5aEF6jF6Upki1Z3f17l83K0qvb\nv53uTaEZv4e8tFsMjLxnPY0gGO9RBO/ckaN6aE6PjUhdniHIsQlfZ+amIfCL\nBzVXVtszd+/cDoGpYS9NgVa+Z3T9vyAql90V+SEKUPQBxrpzs3Mppg6UyOBx\ncSarE3Zip/PJzFn5gZa6bYxn/MmbmFEdLY+27y0pzNSmAwuUAqG3+JfDRiNw\nffXBSiSUeSl4vHoULtcojS14g2lMsxkgqN8q/Zxl5724yZiCMzKLYCu2Fgqc\nB6QBl085ZLsms778KiaquzTb9cs4yH+CgjWUGfEq9hXzYjGe9j7fsSQJgR7G\nCKjDEiOwUaZl7C8amVC5X+rnoh1hhIWr0FJ+3oKMYWBemQcnwUvIF7txp+Pp\nH6ryOPvVTlTbO/12x1A4ToTMrIYDCqhBMB+8e8pA86xgRNWPirfV19V0mtmE\n24Sl0no+89mx/0pUzRPo6XV2kQhrOpEWjfmhWIxeT67hA+IoCGApTls3QEiS\nkRWDU9wsyTNNUxGLmQkS/Khoe8scEp2YgE4ENGp9NnITzeqNFr8AkFQXbPL+\nzXOAeh6YFZIQAO2uswh7+7ZFdHXH+t8Ztf4VTR8ibhpX8421fSGl8+UeuSAm\nRrjs\r\n=2OZ+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.47", "@babel/template": "7.0.0-beta.47", "@babel/helper-simple-access": "7.0.0-beta.47", "@babel/helper-module-imports": "7.0.0-beta.47", "@babel/helper-split-export-declaration": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.47_1526343454612_0.0167207488009955", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "edc9728b3d4137f90c0e6dd079e9456fb6f007b2", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.48.tgz", "fileCount": 6, "integrity": "sha512-PTAHN19m51RBc713bQdU7yd+McGkiOVV56gMtoC9x8iYZ7mfoxvyN723t7ZUkFdMlppTW2xCzWGZ0Ok9Aw1UNA==", "signatures": [{"sig": "MEQCIGEZbrTvuc9rzdroY+viOkK3r3WgRR9/n6vOMiZsuPsYAiAzAUqSHejonyRFp1Wp7t7nQr5X5Lqk7wDLoZG8NhEh8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFuCRA9TVsSAnZWagAAFcQP/At/FZK/EsqgcuAO9vtw\nFiHcB0aGLZ0lvKuSmRoD23oPTVC/Lkp0clKucrf/H7FXBT/ge8DQ2atJfJ+0\nGGvCEPULg/n+t64J78cIcEH9y9+Yz+SshHXh22YuHaG1pkfEGwODP2GEJeUv\n0SdG9B45iwZJQDEqzZySRdDVYKkSwT6bYM5HWKpMUP7uaUApnAkwNBiTm+0M\nZKKpAQLKbmvF0GhLtzOvbJq0pPlqqz9lCLJasbrMXZqBBbf4bV+b4B/h6F7c\nXUuFxr54y6GkDeE8bQtr/RD20eid9ZGosMFa7Um8rQNGj+sKaZ9dbCT495Ri\nRJolR52tvmz3IPcv2pIBaZjyhn0DSPXVopccJp6Kv/3MAQ65Efovgou8VNB4\nic0QIGhHk8IAy3x3wEXLBnh0yoYboljxHKm44mMFkagtnaZzNnwvuO0YLBoF\nXCgJ95kOmq3CS40BzPLWK/WSmujcHXGNDqW52gcVTWYy0rBo1aSb+DrZEyFf\n39dUW2xKQZeipEp8HmCmYxfmppjfR65KroRc9K3UD05vHtJc03qobEY3JIAE\nggPk/eKrUYRHAA7MTk6h9Y1Lwu2jJou/5uaIrZyx2jjNAbuBwyh4e7IPCEU9\ni6qn9B4YwzWqB+17CpW6wmCjBGx7oIVSaoB/qN8CRUTS5F+QQ7kvp8zI0hyR\ndpde\r\n=eJNp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.48", "@babel/template": "7.0.0-beta.48", "@babel/helper-simple-access": "7.0.0-beta.48", "@babel/helper-module-imports": "7.0.0-beta.48", "@babel/helper-split-export-declaration": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.48_1527189869846_0.8815418951901577", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "fc660bda9d6497412e18776a71aed9a9e2e5f7ad", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.49.tgz", "fileCount": 7, "integrity": "sha512-1cGrGfF2JHCSsjtkzHfjvMX1CfKdkwdP6D4/SkvHmH/o8D8QC6lfdPD4pkOvpA2qmCQGXFe5ufwMAjgK+s5HLw==", "signatures": [{"sig": "MEUCIQDmQLKYdHTy6acjVtf4juAdW/7X9fpklCU9CUeCJthOBQIgfBcxBSSVheUWWGN3SRO0e/1EleHANCdrKBCgua9lOIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDP5CRA9TVsSAnZWagAAeTAQAIGc7bQyk9DDArbbgIvk\n0/eYMVwypJPNwAHKCzmm6OUdd6Mf06hHj3DHaiiyBpxVQ31fg7M0ioxcs8Ng\ns/mK/xTNQtFXufh8hgnJGFCYQVQgmpdnpowMI7WHtGkpGjuoiaqW/ok/C233\nn+Zvy1V7ipFylje0Qbk7/RKdLWpuOWBxQJ+NIczGJPfxTc7JxH15yygnQNug\nJVwfhCtYx/QQNKTzzWDp3iaf4E4vXm7m5GvOfmdxFyXGoj+1Ry5NlqdNJG0P\nK8zfRbNC+CDffcHStes3GfuEXs6SUwM3n3NvMJg/CQNvJIt3urYBbrQHVlys\n44VpBoFH/naHZst7nRfMrqjMVeNYNug1nNWD6uXmdWUiwOhLfNpqI8vUyTo4\nMSa0h2tD5SGQvkDbZwUbmC0k5oCb8GI64eW6ZEj2cLt54TcDtKK1Tr8Fg6jh\n5U6KKlvnAqLKeakOHlMyuQkOBMRJcSyrNjsC6tBhTUnMK+yVT7IGSrnBZeph\nivTu94Mr0knIN3kgutKOY56xtLNm+CCwPcXgZV6H8wr86V6TgB17PaJU07A3\ntL03foWDJC4oj5VdnmZlm4mIK12RBTHvOtAhliFPGu5a3ogRbXhii9VZM3Fq\n0W4bNyMtADIM/kNzN/+nUysKv0RVdiAsMqdaSj9casKjqyeB92AFXKmNww+f\nTDKS\r\n=swAF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "fc660bda9d6497412e18776a71aed9a9e2e5f7ad", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "3.10.10", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.49", "@babel/template": "7.0.0-beta.49", "@babel/helper-simple-access": "7.0.0-beta.49", "@babel/helper-module-imports": "7.0.0-beta.49", "@babel/helper-split-export-declaration": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.49_1527264248361_0.6444724270259836", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "59111d476033b37c3b62c03781ef2f01f0f76551", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.50.tgz", "fileCount": 8, "integrity": "sha512-wCWQkRNvdBJh2jOl66VQDrJOHcuE6kR1F2oY8lvlmrtncG6KJx/lL68ho7D1OFxUqFD2HTziSl1HBdEPE5Ilzw==", "signatures": [{"sig": "MEUCIQDrL3Q6aQHshJCnh6SJUDWLViuskfzseq88b4ccViou5QIgeF2eb4ve0JSUgTwna4xE4x9Kn8UvgvvCVmMCtdHELTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29924}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.50", "@babel/template": "7.0.0-beta.50", "@babel/helper-simple-access": "7.0.0-beta.50", "@babel/helper-module-imports": "7.0.0-beta.50", "@babel/helper-split-export-declaration": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.50_1528832876306_0.37627442289242485", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "13af0c8ee41f277743c8fc43d444315db2326f73", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.51.tgz", "fileCount": 8, "integrity": "sha512-IFRl+Gzw6cZtGjAjLEQjgEWxEWetdGhdBJh/etlNR2BHzuQM9y2xAoCE1+EvSkJxndUZ7BFmf6hmgEJc1b11Sg==", "signatures": [{"sig": "MEYCIQCmCneWbBBbnIS8Sxdeksjo2pcLIWa/Q9VqN5PuaC4ELAIhAK+VS+LNz/GjUJ1NA6fpdqmbuU10byKaCfqu+XM/jl4V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29924}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.51", "@babel/template": "7.0.0-beta.51", "@babel/helper-simple-access": "7.0.0-beta.51", "@babel/helper-module-imports": "7.0.0-beta.51", "@babel/helper-split-export-declaration": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.51_1528838435829_0.5937322362781114", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "bc8444ead252a372c928996ae1733deaf3b08c90", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.52.tgz", "fileCount": 8, "integrity": "sha512-pU5k6C4TrzJ1ffYGSVDNZNrXDp+CgJrzeZWHCHyTUKOS4bh2/iWrWqtxYDLYWKQXKB1yctUyNY+rI+mDuZwCVQ==", "signatures": [{"sig": "MEQCIBQcXtcSws1KAMensQU3pD+zrN1XdyTq3fKDXnkmnHqbAiAeaj9Pgfp9iZHAxQiCp3z/8SSdG2wSRBIISXLoUJYpHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29923}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.52", "@babel/template": "7.0.0-beta.52", "@babel/helper-simple-access": "7.0.0-beta.52", "@babel/helper-module-imports": "7.0.0-beta.52", "@babel/helper-split-export-declaration": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.52_1530838783905_0.7496036906461019", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7ba214cdcc8f8623f2d1797deaff1ff349aace13", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.53.tgz", "fileCount": 8, "integrity": "sha512-FQrR3poCdkIxIl+QGkw9Fq3fYcEmcFloO/CSX26FYZuXcHZ5FbPLZajtdcQmPNWWqIicHzCXd0h+gkcRSP9siQ==", "signatures": [{"sig": "MEYCIQCye+1z7f83J82lVrr2/KQX5HChlOF6+aElULS0fYo+FwIhAJ0WNqe+EdswwayF8qIlJ1fHl8BW3drpYeDxyPLvWW74", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29923}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.53", "@babel/template": "7.0.0-beta.53", "@babel/helper-simple-access": "7.0.0-beta.53", "@babel/helper-module-imports": "7.0.0-beta.53", "@babel/helper-split-export-declaration": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.53_1531316443326_0.7742037682905123", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8cc57eb0db5f0945d866524d555abd084e30cc35", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.54.tgz", "fileCount": 8, "integrity": "sha512-tTCP/h2R3pFsdcI82+0mHEwWgm2i0XDHEJeWwaP6qPncKN83RNe0VZGOIJUwYUcW4LldWmfS5x7oAm2LqMB64w==", "signatures": [{"sig": "MEUCIQCKcSVvOgkKELtmZylfMIWP66UM0M2pCdy0oT4xgKzBlQIgaCJw0hZx2cLqjMMZrCKR5iRGD2GznWM0Czdev8Qvu9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29985}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.54", "@babel/template": "7.0.0-beta.54", "@babel/helper-simple-access": "7.0.0-beta.54", "@babel/helper-module-imports": "7.0.0-beta.54", "@babel/helper-split-export-declaration": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.54_1531764025007_0.37243905857138704", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2bd12f0e9187e5d69599ffa7c11fe9a3a67b03d2", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.55.tgz", "fileCount": 8, "integrity": "sha512-nk6D7o/GqcWFah8pF44CMl/kH/pZIvkR6bN3jO8EjXNUX/l6XyV3hzUdeBqq81H2sgXMl/idnlAEeHbX6Gq2Zw==", "signatures": [{"sig": "MEYCIQCEQ/XY5nMcJyAKIgk95wk3uDYRhhZ/Stk1a1e5SvVvBgIhAO63lc38nIzFNRHD4cqhrJ1808BolHyLSeLaa9sOtK68", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29986}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-beta.55", "@babel/template": "7.0.0-beta.55", "@babel/helper-simple-access": "7.0.0-beta.55", "@babel/helper-module-imports": "7.0.0-beta.55", "@babel/helper-split-export-declaration": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.55_1532815670165_0.29374409129466117", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/helper-module-transforms", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "327ba3bd62bcfa597b008f48c0826cdc690ba944", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-beta.56.tgz", "fileCount": 8, "integrity": "sha512-jC+blwjVeVx43WWOJHHXYBcHvYw0eHNgZUUXHKkDTLYc0zx8oev3LyciGFiWz29KgCS1K8YYd0t7z8fFXlCTog==", "signatures": [{"sig": "MEYCIQDuSeEWPylkZO1T/6zOQxpECUjtaeKfZX9aRowtQ1NpvQIhAKnw4HlWvwmJntgQ3sCxUX77IWO/ynwJDhhd+6de4rxs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPx+CRA9TVsSAnZWagAATAMP/R7IN/b7I4ACl58NfXNV\n30EAVNHShDU87TaSY0n4Lx4GT556opNdCHVwyHBpf1neWMc4PGlPYYs1j+ns\nejldKt3zGdinhwcD//9yIOUt2iSxqmwV58mWF/Z+8frRlmdcAsVRNWyAOkCW\n4Srx2J7DOrBMJKE9Dm7kBVOSzIAul8eP/ZqS2utDUXUxXRqiwVfG8wg3wLJI\nlV4Pvjf6lmyUWqJzCXqC3hAGxnriuEjdRK+pDl4ah5JTnnDOGHOspIBIDGlr\n0pesS+m0j9S1gxVHYCxq/mx+nX/GSX+Wt8Cm5KV1qx8Prg/MWYUOniVHcpuX\n1NmvGNQXMka4XG7o/rj0RtMbJOCZyXili4T10UNd4RysGmiVRhUPDQa1Gtlr\nHwimUnnqon/juPTn6kvl+lNSjFOlu/LRAn8uI5x7YtnQ1jopZSeOTSTqxXdu\n6cpRWaJ0ns6lz0vZ7zVoxFlltI0NVOFs42ODP2vdk01bo/QSVcJZyLIs4Jzs\n13rUS21sxeETjzgXdYlhsqaG6GKRAwhvhXcEGxNjTqPbDqfkK6kSmhY7Q34h\nn5ZYxVaqh6b8qCLqnaiCOXpCrAJDAyA/TOcjwXu+wnLtYb3MbVFdt260t/en\nJcTDnYcDgzUrTcgRlzB1T7P5ZVRj9xZ9OhnafqA+K1ut7rp/W2OQTpRz35AJ\nhUkL\r\n=azM4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-beta.56", "@babel/template": "7.0.0-beta.56", "@babel/helper-simple-access": "7.0.0-beta.56", "@babel/helper-module-imports": "7.0.0-beta.56", "@babel/helper-split-export-declaration": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-beta.56_1533344893700_0.8431609820700072", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/helper-module-transforms", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6d7ad5948183a284fb5960554a04894457b39639", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-rc.0.tgz", "fileCount": 8, "integrity": "sha512-ueo8629nhQAfcp3Bo2HTaVGZPFICJgq34SL6bJMTb1LbNdZkBqUZay0SXndoRLTpK6FnQP87ND8rnerSy7jlTQ==", "signatures": [{"sig": "MEUCIQDKC/EzuNvR0phJXU78iWJV56LOftP73dcbVkrfh+lTYQIgTwvxmP3g1H5bpPgx3BpUtrA8o+811uj9K/vJF3tyDhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTzCRA9TVsSAnZWagAAhTkP/00o8Xf9/ejIi0nSjHBn\nMT+/WvdyuHAKSdJWASGF6JlSVrObceKyWcoZWtCAIW39JverwT4c3lXzUmKb\nydTZqmbuD8iih5OeUkpo5f88PK/tx91qrfZTPsGBItWjgfEcxKP6rfLzwIpe\nLE6XY3w1PrhPpzfgtG/k0USdagbmTnGwhLqCEE2xOL/zgpPd+nD13Kesw9OK\n2ryTJAI4uPGC+tguUu5X69pJqIOGmngNlAWbJUJMFrseLi9Y0sKzxiLMkqkq\n4gCF5zn0tZi+olWFb8EecbrjCFk5EaFzeRc4Mh68iHVoH7aR2vTPHd80mmsr\nhLgbrhGHDpCpUfm8s3WucODON+vUOaGngyfPlrxgXpKQJ63LbBjcQeiCzGP8\nDp1wq8vkgydNUPny39F88rMWZ+KEoDUesJRcttvRTLHlQxG1/x8BJ4xtzMKY\nrd9uOcDGbL5zMJGh7hE4FaGEQVXpNd9y5bO1fATYEviWNx7J2VSjvjJnBORy\n7SkZ5hutwW/2eSqUKaRe3Bqhbl8KX8W0gL3Xg9kBpJUbh1nVjmRgc7vaD/9Z\nVmUJ45IIvnrI2WloEPW8hoLGW0oRpZ/yVbEGhI+l7kNpesR9eHfcsqXCZQGd\nuXktN0w3aaDrJu7ZoButr6FJevW5CXx2yLn6cyPQIgC3fsmQ8N97KDQe877p\nrq6o\r\n=jwee\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-rc.0", "@babel/template": "7.0.0-rc.0", "@babel/helper-simple-access": "7.0.0-rc.0", "@babel/helper-module-imports": "7.0.0-rc.0", "@babel/helper-split-export-declaration": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-rc.0_1533830386959_0.027551392860656554", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/helper-module-transforms", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "15aa371352a37d527b233bd22d25f709ae5feaba", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nz7FTFXlQ9UYp/dBjad4ZOu3Q4/1n86ysw9z9pjunqeKFNm+JHq7j5BeocFKIQAwul7QbIkSXiYm5EiteCHjiQ==", "signatures": [{"sig": "MEYCIQCHI1KjAPqzZUriKyKMzxgOsYjKKgm8jNd0nSYQ405GgwIhAIAXg7tp6OYihw5xicsAx43k0GGwPjN7+JTQ+Pm39OCP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ+ECRA9TVsSAnZWagAAsgIP/0bIFQJ/8/8mjv7soW4c\nZYMeBxnYdFnL2nYRk4iRWEDzjtEa45ZY4ortNx6i/cnyUofqE/0wtNg/hMy3\necKd5s2NAYAebDefZp9fiQOO4PKeFa83KGALdMTzhkajPj+Ad2C6OfKd06r3\nYyO6ZQGqcjvRI8+cfcflY6hqQHjAs9oyrS4ohQxYWypoBGE8VYa3Dd5pKz8p\nkQxTt4/1RLwy3Qewjqr9O29PecSwk7K/O4fO4v1Gm8pIrGUKZpX9YhZi7Pm/\naETiE8t3Bszon1mAs/dvPx8Y5k8324iOwmroD3RwT4qFx2w7noFuccanoqnd\nqZbC98TEY/wqoQ03gU19tVmrM8SfqXCoGMkC9wKZY/hN7o+u+dYNTBD1UXkm\n5L2llvK3pl+Y+9FjNK+Js03BhuFzwKTxDNjb49s1yQlv44NDwmuxTg1Y/LTV\nRLfW3FNKBhQc5nWGG7ZfsK59NeWOL1ybyvQ0HeGuDXPm0jT0RBIQYaZbLe6Z\nVC4NFVrrJn7VU+BrG2urPg21PEh84nEnW+YP31ok81WojAAAQbaC+CXzxJkX\nondYT/PWkb62EJZvBZk1JL+7bbMiVpaj+n+QuWWDrjp3XcZ48lHzeStZ95AX\n5HvbrM6ndsjqF5rG9mqhI6x7SjpaFOgubUWJediUZXD649Dz1oWI51IvvM7/\ng9ce\r\n=0f+3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-rc.1", "@babel/template": "7.0.0-rc.1", "@babel/helper-simple-access": "7.0.0-rc.1", "@babel/helper-module-imports": "7.0.0-rc.1", "@babel/helper-split-export-declaration": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-rc.1_1533845379454_0.2095248323055603", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/helper-module-transforms", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d9b2697790875a014282973ed74343bb3ad3c7c5", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-tMGTJiSPa3naZNYfhIBF6ma5TfhyWq8PZe5i9ZemreDx4ffToad0MqQ6OW7g0U9S6a3RlZO1639Wc3DQNyvJlw==", "signatures": [{"sig": "MEYCIQCX8uq1lOm5qxbNPMp2o8aHg6B+quJcnoay8h4LJXYZCwIhAIWGSiGnyaid4Wn7s594xXwBeWrh1zxWKPVZPmUasV2o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGc5CRA9TVsSAnZWagAALoAQAKO+vEmsv1nXHPNe4kXm\nKpYKZb59mwU0kCsrrSwwtcrDWBWLLGN/xvk+nuDuSymSptabjqCUHfhHfK5S\nAB8VJ9/DEpvMayZU6nWy06/E/JmUQfV9eWEFi1rNjDsvClZJ3XZ1iSzmG+el\nUC+xZ12hSedxsztkoXXmnUZJo9L7u1e7HafAQ0Irb1/PhhAeCHnIXdSL6alx\n9fV3nO9bnGueS6tw7SJ4p0t6fQsAE+4jBLVhuJmhduK3OgDqmLwkR+odeYE3\nDfj6OZSqCxganW6Aq7tx2wh1hS98pGCzVudXZ6SlpYCqXjj0e6mc8RWH2472\n/LjilNIiIcJCuk8KVoBup7Z4buanbKPRfweNGa0wsvNw6KmNQxuKp+BeXBY0\nx8hSPCPPaDdR6R3B98KhCYoSeFy24YDvdgHtzmpRs+7xnXjK2OIAlXHWwhlK\nxZ6o1QjLLd/m3YaAp6IJSlQUOsy0RvJqK5ovEBQSOpyu3V+LPTXSITvU4aFe\nVtEfHg0y21p9UjdpzKs19nOv1pwDG6yctGrLpCgYmGYQOdPZif63Q32sk9QL\nLz4BfWlWPg1LRDvVsGx0DtVDVSszFehFxovNJ3Sbt6YR2AxB2Y6mdqCL2qvu\nieV9UGZyhwkO1LSHsZywvARND8vZH88TWCZtBJMshlbAUH53zfG8D3xV7L2y\nkb7B\r\n=80UD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-rc.2", "@babel/template": "7.0.0-rc.2", "@babel/helper-simple-access": "7.0.0-rc.2", "@babel/helper-module-imports": "7.0.0-rc.2", "@babel/helper-split-export-declaration": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-rc.2_1534879544616_0.956664507244029", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/helper-module-transforms", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "dc92b603f42d3567b2d091241569bbf420221cd9", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-rc.3.tgz", "fileCount": 9, "integrity": "sha512-mfAEtgbZ4ziQQl/1nvO9hUjRmsok+VEUowR1FLEXeUxqOAYQszSFgVq+8WQANY97CLr0Dj1hGvs5FGdGsBdhLg==", "signatures": [{"sig": "MEYCIQCdP4zFEtEyEeyR94RzswZRsZ81j5J5iNgCK4hcfRFKhgIhAN9IAS0mvyNY2MSmfV6TeIHBVLjMsb+jeSBLaKjb8EnS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEnZCRA9TVsSAnZWagAA5OIP/0ts0rJ6fsQ21QXMthFN\n5iTV6JaUjDHlSO0SW2cDVoUduylwro4LH3br/7Y159hLfEcZ8p8logY3q1J4\nyUufdHXUUmIoolXfb/MNItVgfhYhEaDgWJhKW4cf7ppBb837nyFFh6DF+kqP\nMj8Zo6hZY3NMwtYCaX8GvzX6solfwLsehG+7/KZK+1yUPqdjgVBqUwJhz3HT\npQrc7nUtuzLgAN/BUZHrHeewctji3UUro+VHdquN2W769BX9VrqvdrAO80DD\n8lhLnC4BcfmLqz2gz0WQ1hq0YQ/olVaYlJrLFk/WIVtVDl8D74GHQ/Z6ApjN\nie+46xR06vikEKzZMmWy7F1NFDAggTFtViPLah7lULKyv1DLPpWoT5s3btYL\nyCJoHKhdTNaouWIJW/0+0xNmh5yeGJFvyWc8AjTPi37Z3zXnXAL8XIeGETVs\nsfhPZNwy2b4xtBirG+A6ur/xK++4WVlbx15cntD3FdtKGZYS/JZYAzSw9KtH\nOtquUqjCoMx+mOI1ic74LkvuGw21h1BROkPWMN1CYIuviilWaCqknAv00D7a\n3rwl4fHstUnegiDvq2cT9ZCJynk50byc6x3Fa1fmw/hdyLaGLXt0YQkQZTmE\n2GBj0bNlwGBR82XwivHs376rLpv2mzBLoEc4YkUhKA/1a8BH+kVQXCPah67q\n/W0K\r\n=6LoW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-rc.3", "@babel/template": "7.0.0-rc.3", "@babel/helper-simple-access": "7.0.0-rc.3", "@babel/helper-module-imports": "7.0.0-rc.3", "@babel/helper-split-export-declaration": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-rc.3_1535134169098_0.5979060529702194", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/helper-module-transforms", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1498a65a6dad00b5786cb4e96486215a588baef9", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0-rc.4.tgz", "fileCount": 9, "integrity": "sha512-aal+20iI7soCg0mWZt2D3vZR1h25Pn+dtVghP56z75swqaejyKfDozQuiqBCaW3tME9LnEHvjrhZVGDo30VtVw==", "signatures": [{"sig": "MEYCIQDyilUpYWRKluUObp6CEnVJNZnBTRz1mDaAER+AwRna6AIhAJkShnm+Olkl7BzNERxkeY3RsAL1QB/iIdzxdi5JjXRX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCrTCRA9TVsSAnZWagAAKjoP/R+rGgv8+OA3MLZJxSe2\nLDMFkWpdhxXEgYsLG6fr7ZAvHEv3W3zEY33kNzjqOkKsImsynrl6rwu2Hq02\ni6IziXrfZ7ErvNuDpDZQR51HtYKvIcDN/R6DZbdX7N6jHu0CixX2g9E21zOP\njfyk9GBeyU5xh3pOz90PPLAKYfGImTkYXqRW5PJ+h3p0/Ou/gTDvdKvdypav\nwmwDE0d7fB/njuHYYj/U4sQzbfUfxhybvTa8sjBXLNmDpfcY+e8XkL0Q3Geh\ngrF2iJMet/6gvy+45HQgSoN86Vb0gi8DJD/TONiYpBIY79fZpbe38w7wIzKZ\nSruw1V5KlD22Y4K98prAeuMRiIvnzpg6vD1KCnD0Vt2LnPJLohwug9vALsc9\nJVKPB8GWCCLtus00mYvvJW1DQc3xC9lXw6mXAa0Ip3oApVEkQxDx3L3RMTgO\nmZsm2JSI06NB3aq6ASI9wQGZpsu4n4+W81ZbIy0mTXjP5+fYwD8mj0cCIRnh\nxzXNbQXhpRvzCiUf8MTREZxE1aeceMw5iOw4Q3EYycf2KJxTowr8y2gVlvt+\nWnSWWJ6Nkb6Wzl7O6+K+ntIbKcGh6y4+NQ/zkhY0CacFj3zZAA5rPJG8bMXy\nUE3htYSiv91Zmuy1gh+vZNioMLA4q4+AfS/5yf9qhpAzuP1ZcKlaFvvNvqzh\n1nL2\r\n=PBzL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/types": "^7.0.0-rc.4", "@babel/template": "^7.0.0-rc.4", "@babel/helper-simple-access": "^7.0.0-rc.4", "@babel/helper-module-imports": "^7.0.0-rc.4", "@babel/helper-split-export-declaration": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0-rc.4_1535388371033_0.8725137615011422", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/helper-module-transforms", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b01ee7d543e81e8c3fc404b19c9f26acb6e4cf4c", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.0.0.tgz", "fileCount": 9, "integrity": "sha512-QdwmTTlPmT7TZcf30dnqm8pem+o48tVt991xXogE5CQCwqSpWKuzH2E9v8VWeccQ66a6/CmrLZ+bwp66JYeM5A==", "signatures": [{"sig": "MEYCIQCSzmZrJ8htiiOmGODo4ZF3HdiAtNx1kNnOZwAPwzimYQIhAN9apevmmdwSqNxP6IS1zNvL7YGWTPY6HgdtwpxcqF6z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHDICRA9TVsSAnZWagAA/nAQAKCWzC5FoNnwTVN38IXF\nXwtSCFkdJzL+PAeHZUYXTNIoY7yTfgWfbzF1McnlNLX8jB6a7FJIU2Rm/BtQ\nac5XUJtvNaj/GnkMt/3gog33uxOXrlp3rA53Z5LQ4hKYvdwf+Ad8ENLaAbU3\ntVZGSuntWgfx9q7tJoglhdvquZnr7/TV2N4nuzmwboIGupt1s0O/RtJSwxkp\neBJbz9zf9z3zF427sIeHtxEq1r/UokHJMSZgmRllOOqG4z+HkHgjKuMfZcm+\n/FF3ZikahYyMrcDaiYqleNFvEXKlSn8ueYEcnqKdWXsnjEriCKe+zsM3918b\nqGihq5kzigLOMRtzf1zYrklcW+RBUm7j9SdY16DK4hCiE7YFC31QszuhvKCs\np2mkfuHyuHyAjeTBH/yECVixMwi8FibHdcU/HOJSZZahd6ypyVmxKBlYN7TM\nG5mOHfTid5byF9JuC+w6+PKjnBJQbUVGEFFktjBLGSXr/Ic/xlL/Re52YMeE\npZ4iODu8Y/9vDZPL4ETxEVtycmE3pj0vukR0PKwU7l+3rHJIyWRD4hTMATId\nT+7L829dK+661JyZVCil4CUogUcRwX+pJKnhqKQT/cpzxRgWdYDq6Xq3gkdb\n37xwiAsppd6HWgNF2TeMEtfdkZjZ8vrCy4nle5fttzuepYJMBSw+iSIbuzWg\n59UY\r\n=kgi+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/types": "^7.0.0", "@babel/template": "^7.0.0", "@babel/helper-simple-access": "^7.0.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.0.0_1535406280360_0.15071922267569637", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@babel/helper-module-transforms", "version": "7.1.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.1.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "470d4f9676d9fad50b324cdcce5fbabbc3da5787", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.1.0.tgz", "fileCount": 9, "integrity": "sha512-0JZRd2yhawo79Rcm4w0LwSMILFmFXjugG3yqf+P/UsKsRS1mJCmMwwlHDlMg7Avr9LrvSpp4ZSULO9r8jpCzcw==", "signatures": [{"sig": "MEUCIAtYknppNR1xdov4sQwqOhXWSe08BPedf+MNjEvpQh7WAiEAkIABKbPOdTPNuo9FdXXkMIXHO4X6KUmV7mih5G7XJN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboADnCRA9TVsSAnZWagAASs0QAJ/x4y28jU+yQcDlvy8C\n+be/W8Wx7Z/q539hmoKrbRUJG/+upRV4HtjzE9ZAsaZuKhp2SxvLVphHRj1K\nZJYHiZZm1Oi8unlOW37nXs4cqdQgkRuGKAbuuT6EQTAMZXPXByKEfKd2dFXM\nXKP/7q3ofiYoMw5vi3Uin27hL+O9SjI085lLRyeYrXTZJamvtrlihH7IWv06\nURJPaeqcU1REFOG8SE0Le7ioCTfUDvjxJa+lBR4jREQQ5Gh46m9vBPjtd//w\nH9az06aw4Ix9uugTQ1/Wmv/aWtc4zGUzY/c0OPDbSm+2w/swc1vEEUzqGMJn\nUKhNLRRiXqnOQNZK+y4MSlOvZUD1E+9z2lDlHsy9sm6SIZ9c2sBGP5Y+bNIy\npbWcnrzK2gJ8kcJBzBHd+cq1461UZGOG73B+cqQD0b353kfask7sB8GMaQUM\nGoxTt3wO1jXC1INUdQZFPS2WwIFfUkKOF+6KdqHTtkJEk8XbZ8qRkmYc/2YA\naaO8xV0mZ6cvgj41SWCYuQU9ixctoq60rbM6mXQDLfty4nZK00TJHM2Hjw+L\nzw9rLBkr7t9SmlHyDBhhvLfJ8ZiwjJgICAnvxyZ0OobCVXz2aLSJ60JnM501\n21id+UIzRRYUeb5i/5XF+Wm2w8BZI1MqtwSZyLboUiYAvfX41QhR5ZJZCcos\nq5Ve\r\n=B9Fq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/types": "^7.0.0", "@babel/template": "^7.1.0", "@babel/helper-simple-access": "^7.1.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.1.0_1537212647047_0.10457452579295512", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "@babel/helper-module-transforms", "version": "7.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.2.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ab2f8e8d231409f8370c883d20c335190284b963", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.2.2.tgz", "fileCount": 9, "integrity": "sha512-YRD7I6Wsv+IHuTPkAmAS4HhY0dkPobgLftHp0cRGZSdrRvmZY8rFvae/GVu3bD00qscuvK3WPHB3YdNpBXUqrA==", "signatures": [{"sig": "MEQCICRDNDRen/RWN64JKrz/gC+p3V898KvvDEJBR74IOZ5CAiA9hfV78DYrlXdw0PirP86/AoK6iGhRQJO5Syo6FF+LnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNH4CRA9TVsSAnZWagAAPe8P/04zlEM63YaP0wpBVnAm\nHnnssI4FrncOqdegAkrcpcFLaWOdhJSE8u0G5pPzzItzbSar+LIGJgSjy/s+\nnfDDVuvJ5XP9ssIG4NXnj6FAfHx59a2x37tlOHdT3tjd2WdRvupcUS/bQuid\nxCDvCJfKzyO0oUFu32qKOoRvMDHdatdptytz65B9C/YywTDeitg4yEX8ARa5\nDf7xsx+FeORen+lhiTdMM072/LGTPPHxetY+BSE+Hz3DFau25iio/XN652a4\nz7dfrFPjAfYeLyp4ostKUO7faLGpg1HWCoaUiJpt20gLuOi7LYMk0PqYgcqM\n5ZKcVsnsnvjLrIrfg2VxHAGYouX3EZmlFJ3TYqqC8jRb+vlW3t9HWJ9JSgmP\ns8jKaN0lHd7V3FxOQ4IAdWmniYmOlpjVleQGa47clMXcw95M+pXaFeypkwmL\nz9Xb1xHhE60+/Cmb9UjW57SdS2rmqp/CgJLxyCEzBWZ9OtOW40uDaE1TuHSS\n1yPAWh/s/hkQ+Tk/fWX8Ov31bJCTOajRvQtULzBA7ZqimhSU3Io9IIzdWhF4\nQyRFRSEW26s91fxFjoUer6pEkUA+XhbRx0L/JSPw/DTT4MlOrx3/OFYRl2nG\nn1+NJ/F3gh0+3u9PRV5slThDKuPs2Y8XRLLtU+/4bqxJ3hyz4H154Z0eVh4M\n76d5\r\n=MBMe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "@babel/types": "^7.2.2", "@babel/template": "^7.2.2", "@babel/helper-simple-access": "^7.1.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.2.2_1544868343787_0.4173415735656467", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "@babel/helper-module-transforms", "version": "7.4.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.4.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b1e357a1c49e58a47211a6853abb8e2aaefeb064", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.4.3.tgz", "fileCount": 7, "integrity": "sha512-H88T9IySZW25anu5uqyaC1DaQre7ofM+joZtAaO2F8NBdFfupH0SZ4gKjgSFVcvtx/aAirqA9L9Clio2heYbZA==", "signatures": [{"sig": "MEQCIBh0Ru4LdalSfSZMgjN8f6w65bf8XVvoYxyzYux4gVXrAiAyGBEn7dqZEqUFevP+VeUII24UnET6wDQVD/xnD+Qmgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco742CRA9TVsSAnZWagAA6ZYP/jvVik9kkAbiUObb4Sr4\nWgvh0i7izcqnzQr+adE6od2Bb0NT+hDV7zLZWPn+mzE3CGedrrtrv7ag8p4o\n/vxLjekQDilE7vgBm6dekqEdmduhlreTqzv/t9L7UYTyyK8o8lHs2Qsaxy3F\nu1StqcJsEmscwx32D6eQJhIBzalBTidz1xx6KvjBH2beNi6n2Z2Ve2Dy3Vo7\nmKVSDD72fOcoZoLbfUM1d1oBwC0nUEhSCyeNeiqFXqMtvX14Nu+HQ62Le2BX\n7PVuj/5vSNpsnCd6Fqcq0x6M7pkRsi5DQEXjmajV9bOCol03bfndfbOX199F\nqC30hxdCdZwuR7siZwz+IDFYDoAXXb2ZlImfVf8bHz52RPvkRjVbx/3urpeI\n+oWvQPgQ3W7iSp0wWDp2J44wT5gx1i9L5wdCj1Ez5t6aNTQ8CUMiVyV4PiUF\ne5gM5Tn7zeuOcshGI8wrB/0fwYwnONGgv3XwKjItjeoXUdJ7JSPf+FGDKTT5\nacWDIfa8ABejAtKS/FOGZ5mHNLCbwy5//pfEvpVdMKYyRm6oMl4/TlTeIFfr\nub/DpVSelb5pYgsbp2Cuqzk3hjPRul0v+od12sGEZzsiCu4MoDX9DJORD3Tw\ntC4aauOra0v7PCYmReU1Quhg96LBr2NFzCY7kYgd/mV3GxitqHpdoJTLeRED\nZ9O8\r\n=nZLa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "@babel/types": "^7.2.2", "@babel/template": "^7.2.2", "@babel/helper-simple-access": "^7.1.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-split-export-declaration": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.4.3_1554234933399_0.5499603982433914", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/helper-module-transforms", "version": "7.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "96115ea42a2f139e619e98ed46df6019b94414b8", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.4.4.tgz", "fileCount": 7, "integrity": "sha512-3Z1yp8TVQf+B4ynN7WoHPKS8EkdTbgAEy0nU0rs/1Kw4pDgmvYH3rz3aI11KgxKCba2cn7N+tqzV1mY2HMN96w==", "signatures": [{"sig": "MEYCIQCmC3pDRR1vIRN92pEZ8TjOntYekxQjM5+QHffek3JrtwIhAOa55bFidR4AiA26/tOnY9nfHMxYqdkmAgblWyzBVcne", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JrCRA9TVsSAnZWagAALpMP/Rz63uI2eP8YRXzg/XJO\nrAIBm2krshnlvQsIasTNQKzlZdUlcMCl2es9tpB+/59m0PcKgsFDiA0L+fp+\nK26+tUjuLTnkl8axJMKmo3BgvZ1GzKAcYYf6X1PyXV99h2mOdFxz6Bi/VTWI\nn3Jn76zC2nK0M96D3iqpS5h+TcDYuIrF0BFpYNgqxLdyX0DVhjwSIO74l5cH\nwDr3Dnfp9VVZUAfywtkIjHg7mVMdKBmHXAUs1em0i5J7J2hq1uB+kIvthqKp\nOvC+a4FSE0yeYyU0oseH+jjFygznJkBwMjiHokzTuJ+EJzw1MV2YdmjSa7AX\nozocBJ1tnPWZDdDuy+qbCgfasCFZWuGKbiT4WX9RknvXbe0U+jiohHT2OXuC\nCuXggB8W7zHR9RX1Y8vtxmY/iZjjA9BzKMvMJMBIzba0kMJxVrzJ4CKOZVWu\nRtCIQXwqc7vdIlm8p2F6W6gUFJI3t0J8lFfmiKtLQMX06FwOZBmNfuksIHUW\nRnV3P6Ilm+Yu5Dv1G0D3PkKJkhtzKYItL4aCwzAJttO/Ygk4QgFax+NY33LB\nneYQrGouoeG1Oh8dSuGqi5gj3+sTCnX2i5QgEqYhbQYqscv/F1Qld5JteC8y\nfFbHPb+RwKR+hnATGnEfJ6nzrwh6EeGHVPt1qyMGZ9zn6IzsJriVw6G4SBNd\nHCrk\r\n=rUs0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "@babel/types": "^7.4.4", "@babel/template": "^7.4.4", "@babel/helper-simple-access": "^7.1.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.4.4_1556312682772_0.5931920160744002", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/helper-module-transforms", "version": "7.5.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f84ff8a09038dcbca1fd4355661a500937165b4a", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.5.5.tgz", "fileCount": 7, "integrity": "sha512-jBeCvETKuJqeiaCdyaheF40aXnnU1+wkSiUs/IQg3tB85up1LyL8x77ClY8qJpuRJUcXQo+ZtdNESmZl4j56Pw==", "signatures": [{"sig": "MEQCIFuz7y3j1GB10bJKmf5g8fkD948OLS3lCgD2YsPK3jNEAiBx+D+7iNbc5P/XOyrMLEDluDMCNRV+A2j6tGQoztvQDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FlCRA9TVsSAnZWagAAmgoP+wWuPqny3fvmKl6W2fTM\ntfDnhQHQ1gScmT7TXtcyg4vYQFakGwpfEJdYgqT2+e6BAgP7b/3FndnFTQIe\nuOdnUofpAEJbC29TgKVeLhGOpsFhmgZnamh1G+W6tIo/p8Qj59/m2qPwrqhM\nxFRileRWJ5HeWFF7+MQOkCg1+xS9jMWzvphR1HZSgV1TQ1V046rWAFXGE1YP\n2dl786pdaZ2zkZcehoXWRUnoFJk9jK8gYCzFy/QMFgWp4kxfPW7o7z+WoOmA\nM6b5NhnrCESPQMAdpd4RQ6K7id8jdJO0yUTEvVEmLcrqVCEW+Y7L4dRzgwS4\nqxPkLuYNzzVo/UMXzMkViTBSToFC17y864dYGHRQaz67L9dZgQkkjbxbc6eQ\nNhXcAyNSfGctc2FJL0enqc0KdCe341Av7au/2MpjWGEIbGfG9XtnqTZ34ao0\nli9hGkAPrHxujFv8O71imDj3xvKdIMkHG12NWwmqGZyDau38mAaSEFT/nBNL\nAa7bqHYVQgNaJsqEjx+MYkgE7AUjTtTbRZnVobf3clNJf95lGHS7Z9h6xuXd\nVD7Iw6derOVpmUmTPd3ID2TWBSZAj6OaEVJP4J3jcIevmTHTyW54UjsTIGrg\n21jxon6v66crnIx38YohUvdYTYuFu98v29gz1Dmew7JR+0bEwe8xkheNMokp\n5DC6\r\n=wGIO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.5.5", "@babel/template": "^7.4.4", "@babel/helper-simple-access": "^7.1.0", "@babel/helper-module-imports": "^7.0.0", "@babel/helper-split-export-declaration": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.5.5_1563398500423_0.651070919774823", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/helper-module-transforms", "version": "7.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "154a69f0c5b8fd4d39e49750ff7ac4faa3f36786", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.7.0.tgz", "fileCount": 7, "integrity": "sha512-rXEefBuheUYQyX4WjV19tuknrJFwyKw0HgzRwbkyTbB+Dshlq7eqkWbyjzToLrMZk/5wKVKdWFluiAsVkHXvuQ==", "signatures": [{"sig": "MEUCIQDNNQ7NCrE8XRjT831bQgxjP2xj3oeLgV237yIERCSwTgIgK4OWAAxdZpj/7cG/IGgEzb4NVLXTl+Z4WEKlQgao+rI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVS8CRA9TVsSAnZWagAAp9QQAI+HiOTg8wcsC4LHscID\nwn/n6Tlr9JSfYs8zzar0OnagWMHe34dn1x38UX0jWWOwKABs2K+c+TSL7F9J\nyYb/DT6zMPRV4/jKBME3gqK5/qZW7XW5NApowIGA7iyFqGRmb8limYX7FFT4\nAb8njfBGxW7cK48RvPleIKAAvEeQIluZJPGKr9t9FhBRp9BJ2QrW1qEqk95m\nMx5Z41+lsEn7YPgqmzlbDuzzaydyQDA/iZrXRaV9qz5gPRZIcPZGl120D6lI\nC4OROtyNOIFcv1wkSDfRA+uWI4p5mJ27DOP/3o3pWoFFH9Avnyqm28PjIHkG\ny2y1hGwqz2+sgiW/YdVDARIL9EDmKFV6bRtZ8QnXFtbaWHa6IPVr9qzSRyEU\ncXIo6OiXZOH8PlKcFSYtIECZcFipkB7eKJMPedVNjmiuOyvoWmaXgFTa8uR8\nK055SRQ36CgbnfQ5CvDrg84B4fUlLdViXGBWeqIBXI7/8ZdmQbhLcii5L0kO\n3s+tZCmSIBX3E2JUhkkHT/3nkWv62/86H866l+vJGYEj5ofZ1W0NRMVNkcOb\n02ir94uEMO0CHZ+GKlOYPYpSseU9ZN1jjYsCSRPRtC+CbXKQ2L/7CjXK01xP\nGqJprdMemX32WAjKhnNmfkqllHTxhwlR5Y7P8p729ElVNV+61rNHU8VUif2G\nIZDc\r\n=bra7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.7.0", "@babel/template": "^7.7.0", "@babel/helper-simple-access": "^7.7.0", "@babel/helper-module-imports": "^7.7.0", "@babel/helper-split-export-declaration": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.7.0_1572951228079_0.29037072505413764", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-module-transforms", "version": "7.7.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8d7cdb1e1f8ea3d8c38b067345924ac4f8e0879a", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.7.4.tgz", "fileCount": 7, "integrity": "sha512-ehGBu4mXrhs0FxAqN8tWkzF8GSIGAiEumu4ONZ/hD9M88uHcD+Yu2ttKfOCgwzoesJOJrtQh7trI5YPbRtMmnA==", "signatures": [{"sig": "MEQCIFvxkUZIvoLm8n7vO1XBuTWXaEbmDlo5gBHG2ZC+n+LXAiBIY2rqOVKDo1QL5/Nr7xBSyKIcB+IsNV+IQRg1u0Gccg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBWCRA9TVsSAnZWagAAgmcP/iwN7g8K8bq15Hc7Qcxq\nXnZ78la3q/lebidais9icEysihCEHxIc15v822awwndi3sJTxcrGqeWRSE85\nHYMDf1J+LPUJxAbSSMOg6Llm4vFyyo81EOWxampp59ha8/HegSiwVgzgr2y0\nQ4CozruK40rxinXTvkmZxYSTjEkswCXfi2KqmGhb0wPY9P7WMMwA/zJfvkLi\nJa5WK3j8q/6Vce5YUPmHzbbbKeaB3araJpUQhCJq4cuZZTZ5hXen3LeaqeLq\nhpRLewvqfRctE0s1XSG8rzG3i8D4K447j0HqO6VjT/L8lIa9AAjbkmGxu+os\n5RLq00O6cCvaWuK8XMLGMmvAQ+CNJwHOjTUKSotcJmgDRThAeKUI7XFKUkD3\nh9vi17tV/uTj8X8fCmGoDmHLVdA4eslUQWEoh9E7FbbfwZReLrvT0mhdTrqb\nI2qJ6JOlmVV0Gsf5pz8SNZ7UwytJt4LaUmBn70JCsXsy1LBH9wwRrC0PmZcG\nCWsLMabg6xX8eD4oc03IIGbPV+h3vePQLUwCZjSgSIZm3WKttP395TRq83E6\nDGb6O4mGQ98wCkb8mZLxa3Tki0abwLxGorbo60X+OnziUbePpyAtvMD3jsVV\nfDHVJryH0S9/GvbIi87vcYOU1dYEzRI2aLkifBbOUomUjxb6Zp+G/f8/CPGT\nYNYv\r\n=Yymg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.7.4", "@babel/template": "^7.7.4", "@babel/helper-simple-access": "^7.7.4", "@babel/helper-module-imports": "^7.7.4", "@babel/helper-split-export-declaration": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.7.4_1574465622002_0.5019206610723839", "host": "s3://npm-registry-packages"}}, "7.7.5": {"name": "@babel/helper-module-transforms", "version": "7.7.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.7.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d044da7ffd91ec967db25cd6748f704b6b244835", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.7.5.tgz", "fileCount": 7, "integrity": "sha512-A7pSxyJf1gN5qXVcidwLWydjftUN878VkalhXX5iQDuGyiGK3sOrrKKHF4/A4fwHtnsotv/NipwAeLzY4KQPvw==", "signatures": [{"sig": "MEUCICVadWKPzk8Iqsvf/TT+LhzDIA9FYYjxPvyZgUzEd0GJAiEAtFVPClB4zw0Pu+1m1De/aSESHWQamdoRtzaP1zH903g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6lT9CRA9TVsSAnZWagAAZkcP/3xwxoBCPuLyvlBoGA6H\nPR+FrU1fZ1dNFhMaFrpFiL1lOL2GV0IOj4zcFCx8rjW22zroduX6gF0ns9UK\nXM2pNOU0JgkEp9tsyQ7//mBrEmKhXNjMWmY8gIi+2yXXdRYybXAKpCnMe2Uw\nO9CWemZRka1Cr+WU5ffFLl7SieFZNQRfYH7EqA0m6Wx3rc+dzwRBGkYdIaHx\niU+OfkEJzvrnyd/ia1NOjz9Ab/w0Vot1B3O1I+zXPzdx86aPskPgySwtAMUs\nZbMaS4FaXgDFpOsh/Nk7KRXhSIxp6MyChQZNhqh2sxws6a3L59nvB8xBSiFY\n0KVC1vKWXTg924Z17RickeBsa3bMM0eoZvzlkNGIgcUonKpEvyJjs3/p8CES\nEJWbLLe0OtxLFJ3tr4+FmFSmwU0TVxzm9MWYey6WBlSgstWA4X2YSxZwQBZ2\n87gOe0hk4AYG0YyZ5U/CYYR/BixtePKNp84zddAdapLRuR/03BIvxo9Qvn4B\neNK+JRXZu56/an37UQqrRrah/M46aSEP9MPPFGIld+0RPlOFTaxkZ0J+oGYY\nXuPUZXG/7nIjfWMZmFrdEznN4+egV19NIts7zgYikc59e72jhCOHuo0OmDyi\n4s4KoVWTHFUhmZmoyxo0eGNVkEaEUtAvGpWxXKii2Z1IqcLg2IYUALzsbbVM\nZMkN\r\n=9Ia3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d04508e510abc624b3e423ff334eff47f297502a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.7.4", "@babel/template": "^7.7.4", "@babel/helper-simple-access": "^7.7.4", "@babel/helper-module-imports": "^7.7.4", "@babel/helper-split-export-declaration": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.7.5_1575638269347_0.18709959092309014", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-module-transforms", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a3cbe4ac91b101c4b6db278af0c868fe7091ebae", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.8.0.tgz", "fileCount": 7, "integrity": "sha512-fvGhX4FY7YwRdWW/zfddNaKpYl8TaA8hvwONIYhv1/a1ZbgxbTrjsmH6IGWUgUNki7QzbpZ27OEh88sZdft3YA==", "signatures": [{"sig": "MEUCIQChifOI4XuD8eKW5RxrUKtG6Cwshell0bllp+ZkFYSGNwIgYKx9s38cV5VVglPzgZ48whj4c0yijPL7gFMFGOr2cEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWTCRA9TVsSAnZWagAA/DQP/jIGh2UpQG1E/8RHtPbE\nDjEu8A9sA+9hbaxgZgcrSUr7UknKK/3OB4gl25ExVSJmHs7St7TkO8eBQSH3\n9N6B1uUtruBieuSug9/FEDd2hxVkDQ9pbajIjehXQzh12XLsWlp7znflSCnW\nBnk39ORr6RDshK0vF9e2bjO+zCWRhCGebRcDTJuamRgNzRzl5RSONCtldnu6\noq/uqgKOZDHiak4iKoLWy+7Gy62aNBjBvi4fCrB6gCe6deyj6BuVolp5lwmT\nHKm8drZHDjPIlfKYKbFOfEr0lKo4Ou/23ud7AkKiWKfPm6usKZqkmX5n74zM\nP18+DeiqSRIxZjRwSOwzFX9OZx7iOrS1lhurgUpo+VoeGWNbFkgQPQYBMuY3\nY1ufxWgToHpSKgJzLkdw9hDTdJ9AYI21aDgJts1rlxgPFeBW2KZBTbvTYqb1\nlfe9WtCI6lDgSH69T1lUktOVG8blKcEbsPZ5vvdT15cpMamcSbxaS5+Bx5rh\nBgGBm2p+QykS+PHFvF1/v1qALI23PF0MvJntC/8bjq4897D5nlc1ZAfMawwD\nDI0DoFL+tEtJ/cjvHVfjSEfPyFw95M4R0A0M6iMaERwnlaJ9xyAYhCfewgq2\n1Veb5TtIpvv5AE/bDa6b7SxOhmsBj33jYw/2QULWAJYg+lQpQGYHYY48j06c\nTUHk\r\n=We4P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.8.0", "@babel/template": "^7.8.0", "@babel/helper-simple-access": "^7.8.0", "@babel/helper-module-imports": "^7.8.0", "@babel/helper-split-export-declaration": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.8.0_1578788243462_0.9173000401279692", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-module-transforms", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d305e35d02bee720fbc2c3c3623aa0c316c01590", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.8.3.tgz", "fileCount": 7, "integrity": "sha512-C7NG6B7vfBa/pwCOshpMbOYUmrYQDfCpVL/JCRu0ek8B5p8kue1+BCXpg2vOYs7w5ACB9GTOBYQ5U6NwrMg+3Q==", "signatures": [{"sig": "MEYCIQDq1ZDK9Z8/zfTjT/DhqYnW1lZJrV4VGUcC+g0lzQTn3QIhAPZpIGOyIzSq3x4BEqHPnmJoEWC6bChHxTrcgkOk1hNA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQ6CRA9TVsSAnZWagAA/SkP+QDGfTwTYKVfpl11pUuL\nDX1Jo2/eEqKXsdP0zDw4sgwOyUUFVhoA80mi/Qr77jYHFOoKe+mn6d393K+A\njRlltFc6Iyp9G6ssnx8ljnY+Z/V07jpgjYwrFPy7qwrHArWBjpqgUJBKuNd9\n5LSGQiGeDYulmtOWPBP7UIEyy+toLPvJ3DJiKwfZBXHY6Rff89k2ONxDxV5E\nXHC5NvHRV6v73jcQVW2x9ivCf7oYiaeXqhPe5dsAbuxDc6/l+PF21SVITjlQ\n3xYYmTK8DAIMHds14BZZQFdeDlQJdzrwAccnQxiFQxbYCnKSla8sAAsm6qSv\nP5qU9yFT7xwwySZEYW9tmvyWuOa+Fs3BuEcNQDXS295g1gnmVK3WxRRMpO0I\npNpwRKVv95HAOJND31f0N1W/Z+TWk+nocl6klc6+gSIbX/N/+762RgE4bATQ\nQuHAk3hhSYrRkCRi+kIJwpjPaFp0Ni9H+SE7Dkj+r1vcTLslf5GZDGRUWHge\nlYxh39Yzl04Z2S6cfWP10Wp+XZQrGG1wONfw30F8QUa3RDMaUMP51b3D6+L5\nr+H+ltxKT1EYdS/4P1fLdWAdeIBET+E+VqjWQ4kT1T2blmzQuyrex8eeVLw/\n5BwcvrESxfBSJPsdmg9hk47CrakNvtA7pjeq8+JbxLycDzm1Z2+kae/7gybi\nQGXA\r\n=9xcN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.8.3", "@babel/template": "^7.8.3", "@babel/helper-simple-access": "^7.8.3", "@babel/helper-module-imports": "^7.8.3", "@babel/helper-split-export-declaration": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.8.3_1578951738424_0.6341717706760139", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/helper-module-transforms", "version": "7.8.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6a13b5eecadc35692047073a64e42977b97654a4", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.8.6.tgz", "fileCount": 7, "integrity": "sha512-RDnGJSR5EFBJjG3deY0NiL0K9TO8SXxS9n/MPsbPK/s9LbQymuLNtlzvDiNS7IpecuL45cMeLVkA+HfmlrnkRg==", "signatures": [{"sig": "MEQCIG3Eq4+zneYeQVyrXrDDoYwxI3l1b4ehzj7E7Gr5CRp9AiATm8bYAk0TloHNo1B+z7tsifT1ewcILu7zyjD2LC6zRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RaCRA9TVsSAnZWagAATH0P/R8YUYtrPnryJ6ge/uda\nnPZ/TY3y+yZ4pk3Cs8t4b9bJI4l+aFHvzBIbjM1GeYcRUl+X5rEj5PSxkgv5\nlbKkM+/qo6oOvCev7+or5dqku4hr5wlklz/mdmAHBzcdySYmJswnKsNrYM9p\n4jNJIz5BZC0vLVVqWWqAB3UOUbNulCm6JU2Uxi06f/jmwLkN6Oiv5km6pqhW\nHoTu3yzBHvX5L0Y79r2LZZpcWYGYxrHNHG/pKTssWm2cU8st8FP2FPfGvw8E\nEMryQYaCUfR+j81FedGn2HdhVWgsekLo5l9lUnvBCU0LnMiF43p3PLqxWuav\n/7tJvm1TxEeEz/l5yIPm5rrtemco289Rf8UiloAnPotuQ0L7l4GhP73t3Fsb\nZhSyLBiemn+4Gj6cqDyvoZkbrNGAauwbs9e/fSPEtplt56eTWDq7vUSNqK3Z\nzPSkNANR8rcLgaFkRHANHQAgvYo8GHH5WVI/fkVE6cznbqiQyjkSLdxfB6HF\n1wRK7TSVdNLhFlDPl5wg3b8NadUir1UIRuluTRIccejCm4ASOqpeTFtoYNUS\n/DxFizA3eGWp1GJk/RoFMCWI3nBw4I/00VN9g+OHNwhVpNtmO1QMoUEffL3C\nj7h6wVTcNh+btxhNI8ncnNEwGtnKn/pZUVpmp4SjsOixN+PkQpIsLRz1n13Y\nyD8U\r\n=d0K+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.8.6", "@babel/template": "^7.8.6", "@babel/helper-simple-access": "^7.8.3", "@babel/helper-module-imports": "^7.8.3", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-split-export-declaration": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.8.6_1582806105732_0.9635019035700987", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/helper-module-transforms", "version": "7.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "43b34dfe15961918707d247327431388e9fe96e5", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.9.0.tgz", "fileCount": 8, "integrity": "sha512-0FvKyu0gpPfIQ8EkxlrAydOWROdHpBmiCiRwLkUiBGhCUPRRbVD2/tm3sFr/c/GWFrQ/ffutGUAnx7V0FzT2wA==", "signatures": [{"sig": "MEUCIQC3f489ZY7WxtGtzUgiqdXkOf2xpcfUD2WK4OB7YyAAngIgcu63YkYHi4Gzv0gO8z2TlZziqGPfH1g+kBweGTdiZzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOO8CRA9TVsSAnZWagAAKuIQAIb/3F3uxoycKfp8T+Gt\nYcY7dh2yZFf58LjHKCQq95LzOxCO8xnrfDyok0YF7HIP6GSZn2yolYJ/TgOs\nBdMGKLaTsMVmUQ72jFtZMxzxQjd1OBZT8SWD9hXODKNiCTGDRukXSM1vNWSt\noMyMu8K67CasvgNATLlPRNoVrqKlMBjjq3Q12IDAxLj9+oOBdMZgzT5m1ZdX\n42KOXkNMDfmRf9/GBVJhByILn1fFKsld7ll4p+RLTHJWcpAvh/wOcFXLsNwN\n2G8waVCGaj2vEikwnjCTQuM3Q+orOOxtDPdQR1SFRx5hSxRKNCV1q3pPgcXD\nrFtmfQyjz0nr4h2zVixz9vDvO5waORRm2DhJ3G4YcIqts3yQy8ms0LPMV9LP\nIQM0IBnFmuNN5h70osGbLbBIuQ3vS092J0vlmnw3Oz/l3+zliMKfxTRme19H\nsxp4hd71LKpQaAl7/PBiBObt0BFABSBqZQQUaFvXirrQAJlaMEzD9aL1uDVg\neaA/b68lekogluZQE0awm3m08fgO/fqxpzqIvjmqGQ6tMV02XkS4cO5SFJNt\nqz8K8QH0hhS9lbzDVsUwhqLxC9WcYxbuoisxmA4/qvsA5rA4PKizAEbRssec\nr3Mtvm1SXwMS+4+lVDDMUDFQ6yRpGI/vvrQFcjWANfI7gyIM2GCENRATLDsS\n1j3S\r\n=ulMn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-transforms", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.9.0", "@babel/template": "^7.8.6", "@babel/helper-simple-access": "^7.8.3", "@babel/helper-module-imports": "^7.8.3", "@babel/helper-replace-supers": "^7.8.6", "@babel/helper-split-export-declaration": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.9.0_1584718780091_0.023471694862844616", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-module-transforms", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "24e2f08ee6832c60b157bb0936c86bef7210c622", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.10.1.tgz", "fileCount": 8, "integrity": "sha512-RLHRCAzyJe7Q7sF4oy2cB+kRnU4wDZY/H2xJFGof+M+SJEGhZsb+GFj5j1AD8NiSaVBJ+Pf0/WObiXu/zxWpFg==", "signatures": [{"sig": "MEQCIFq0agsjgioQ6ka3lXI0PYufkzBSF2DMCet2PbyOrhtyAiBVjMR1ExH5g4HtX0KrBhjFp3L/u59hFkRz3tfHG6m+0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuTWCRA9TVsSAnZWagAAMv0P/iDgLtLBxXgu/pC3zwqV\nIVBLkmNwNwDEza5xy5IuWVBx2bXbTj0On2vX0a2xpnbGypN+ynLt8vMTCuJO\nF66BCmp9EOOUlPKozFrD+RrPtGFHzggq7Hulw81OGWoPp8/DeL6CkZo3pPWU\nJYVMXe/TCK5HnPXcwCIkvvyM6m9Tdt2dGYHonPaTqEqAut2NaNPCVNSizNFs\nBfisUdtGMFTDthMsKMmVpq5nRPUMkJe9BOeYnDIydl9TGR6WpMRalgI7I+Nb\nmPqT3q2rZUhs4uTRCqMB/BSUvayvTjvWILkAx4FQQLBEBBs/uozO04CeFnaK\ns0iOrFiJydxZcVkW8CchXNSNbkM6nfqg+zoZiz1Keeo1LNrwiasoso1NX7pf\nJrynS+AxVi9kfQrFe5haCLLTSuuMegYIWm6UXhYUYLOTuy6t+m87mj0DxBfA\nTgpxFMTHFHWni/g7XDxYVjGU8zMDb3/JtbM5Pt7BEVjmL3DmO86uKpk7uo2F\nNdxIsKUgzVrrckuiCBb2cOWYKE3KPpha/Y2HgWm+1PL67Z3f9WuI4Iz7ASV1\nLy/B5cTqpAV5oSVWTfIPbdJyZCisfbf6TEuKbP86xmOp8m1nqrZWXLLEJ9Sg\n2e7V6UzszLncfE4OXl1TzQAJZH5449mYq8JcR8mT14zdnTo+12E+gC157sjB\ncaGg\r\n=TQpS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.10.1", "@babel/template": "^7.10.1", "@babel/helper-simple-access": "^7.10.1", "@babel/helper-module-imports": "^7.10.1", "@babel/helper-replace-supers": "^7.10.1", "@babel/helper-split-export-declaration": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.10.1_1590617302228_0.14348494792780042", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-module-transforms", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "ca1f01fdb84e48c24d7506bb818c961f1da8805d", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.10.4.tgz", "fileCount": 8, "integrity": "sha512-Er2FQX0oa3nV7eM1o0tNCTx7izmQtwAQsIiaLRWtavAAEcskb0XJ5OjJbVrYXWOTr8om921Scabn4/tzlx7j1Q==", "signatures": [{"sig": "MEYCIQDaPtqYHIPDvnEkSONF1wkHh06KRXYoEoLSTsGy+91KiwIhANFRmHq0pjnh+23jztZPsB44/+SMZWZwDr2X1xXk9fzC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zpzCRA9TVsSAnZWagAAU0MQAKGSusGkmHQ5N5lLzrKM\nBGFkCAY4eiKr/K1MkSILJ8qOEQzQQrbWfMyQaxohceN1DTSffyavsYSncBgu\nTZnV8eBKxL7ZYGuVZcei8mMf/H7e0ueXa2MrESPRgJ5FqKFFI0VNezhuHApt\nEBTRCPthHrSGV4OWrWS6q5W35vi9mmGL95A8bcNWI+/rSCfUF0ItGXRn6IFq\nREZrbLWtXR54cb35ml91kLgYVY3olKcVT7MXXu3yyx12rN9bBDh618tyDZPJ\ngxMmvhgaweBFnOQvxWDKivLHvaOvJgOQbkre1kIizQskfs60QSdi1nFawElm\nLxf3h8lnBVS45dSnsnOWHHbedptc+Y+QGbcHD3H8y9Y5XBztVydr4JCk+LHE\nhIk2eSUkcPMXjWV4H1841ouQ8MhtNERpc89WeuD1ZqE1W24S9Bl3rrvjvJhj\nwMBxlFtIevfIfBkgTHagpqaZ3CAuM9cqTUqVEi3QBczzGfQV5CCSt7h4dEiZ\nnhcyyi5pPglvt5UjptbPzPPLmLrdMnV8yTHNxGwEPj9uUxRlz78cMdzt/skI\n0kGAJSgxfvVHnJBPaWIIqETTbRwh/ij6DZmZcf+gEVd/4s79XXBOqEoB7tLT\noYy/iRhZflJ5nUlQNd4G1FbxHZaJ7Z+HT3qdAi7o4MoDb8qirdbochC3LTJG\nhXfp\r\n=L79c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"lodash": "^4.17.13", "@babel/types": "^7.10.4", "@babel/template": "^7.10.4", "@babel/helper-simple-access": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.10.4_1593522803176_0.8175775749993117", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/helper-module-transforms", "version": "7.10.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "120c271c0b3353673fcdfd8c053db3c544a260d6", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.10.5.tgz", "fileCount": 8, "integrity": "sha512-4P+CWMJ6/j1W915ITJaUkadLObmCRRSC234uctJfn/vHrsLNxsR8dwlcXv9ZhJWzl77awf+mWXSZEKt5t0OnlA==", "signatures": [{"sig": "MEQCIBrMx2nOKmKn4vrMcGHuIiSZ527DjYZ4JHZ910J4asaxAiAQe0VgMD0s7RI7BTKx7s3dYnOlJrEIZgcOXFp7vY3Org==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbcCRA9TVsSAnZWagAAregP/0iLtZYbJzHX2jHCCfWT\n/q/Szee8RQjwoRL+q601IvSoD08x6/2A8fy9BgsLu/V7WbV+AWNV7V6Cjg21\nDz+x4KuNXkHQf12zdEJm1LcmylBudGnZL092pMpwyc9jiGwLu1Va1K6bGXG+\npexr5OmUkYyh75VXICnzqwtpX2D3rZrevwdULPqBPugVr9Fvtove6AEOJTio\nAQGJhgBHsSj7Quxks+qtCjh95J6sSpQdaYZEcd26nDfaewQFFnFx5HNfEAZy\n00f5quHJ0YCRVZgbh30PHtsvwvlcmzo9LJ+ySH7r8DUPVDrn7MOJZrG5NtAZ\nkKsCLG7O1PTi3RpLqmx9yKP4pnuhLCPft3wFqrSVWCP2EjGCH8nJLtjhN+jr\noHfsHnPmR9TUVUdIZPrQ6mFnH9W1nryOUqms7kwjMzpRhiqRM7JR5dIGUecT\nXCNc1EXP718Ufip/QrCG0oN1USBcWDcSKtQclMGatxHZxhk64UIhSnyv98Ed\nSkaOAcZcYtvuW4fvQWQz3mHXLZ7PzP4sw85UgZQrpJHM9yNZmmh9b4Q94CB3\nStLkbIJfkknvjphBdS/NeKM2uzxhJ2AtY1AnbgC2G/1mA30CxxYsxl20tlyd\n2zNKQ6a9qxSj1pRPm4U2K7LddkMGw+cmU7LS/KRuSzIT98SiYvWgtpEfn11F\nR7nP\r\n=whVM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"lodash": "^4.17.19", "@babel/types": "^7.10.5", "@babel/template": "^7.10.4", "@babel/helper-simple-access": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-split-export-declaration": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.10.5_1594750684372_0.1222462199634291", "host": "s3://npm-registry-packages"}}, "7.11.0": {"name": "@babel/helper-module-transforms", "version": "7.11.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-transforms@7.11.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "b16f250229e47211abdd84b34b64737c2ab2d359", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.11.0.tgz", "fileCount": 8, "integrity": "sha512-02EVu8COMuTRO1TAzdMtpBPbe6aQ1w/8fePD2YgQmxZU4gpNWaL9gK3Jp7dxlkUlUCJOTaSeA+Hrm1BRQwqIhg==", "signatures": [{"sig": "MEUCIQCcl60LQpUkBkvMgKOgyiLBBCj4gFI4hvjcyTgr3/xnzgIgPP3f9DZ+9jeDTEFaxfAXzOZSe2G6lgTQYtjRgINSlPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIzsyCRA9TVsSAnZWagAAF2gP/1EJRNM56UI2w+UMmU3J\nNSKVRA7JC8fpYS56i0m/9WBbTFZdlpNprBtTDZ2Z2iaCVIyyfX5xAm95owCJ\nSxSo5cwCcr7lKo3cLkTho73XPdY5hqDEEc4lFZUyRO1wLbwBVG+6OwThjZtj\nmWTWJL7Tdd5Bx/8zHV0TDjGF8AGjfCS0yTPiuWRgKakRaC+OCU646i6ZA4sw\nyWN4Gbm5yBl+kBPVtis4ICVbgFgBijPy1F2zqsF2bQK090fD9XQWNil8Cc+1\nPNW1S3AQjN7yt1tGhAXPaZT8sHUXsLoqjH2Bsl0fVIC1jOc9qKLM/9os46TA\nXziDOaA31Wcbabj/Ch89Wv8akU9uhWJ50ZByHGZnTT/gN+2l++zQl1Piu8jQ\nT0C/1Fl6DcdGxwLf+DBhM6nLHoSj1qt+XnCN4psN4QoQT9PF0goRaAKhLQ3u\nCIICb0BThHrJpR7Z/JE/UJMUeNDgxLruRSoSkRs+6kOjgSG2Jsg++jGdQN08\n+8nm2l+n0E9jpEjV5S0H9BDGvcXN2Z1h0rCr6V0iBKwbGMGoSfgWjX3qkl2P\nUknHweC9S8bPb/wYdorNCWBMWOWRyurHgEx6sqOmm1EXBDEI7y1bJsoZ1WWM\n762JAmV08QLfYKmDDrgigY/mEjuujmoO5nIXSRw8iNMWgNZ7UseCYtPTu33j\nsdyj\r\n=b5zS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "_npmVersion": "6.14.7", "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {"lodash": "^4.17.19", "@babel/types": "^7.11.0", "@babel/template": "^7.10.4", "@babel/helper-simple-access": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-replace-supers": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.11.0_1596144433516_0.9402683856971066", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/helper-module-transforms", "version": "7.12.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8ac7d9e8716f94549a42e577c5429391950e33f3", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.12.0.tgz", "fileCount": 8, "integrity": "sha512-1ZTMoCiLSzTJLbq7mSaTHki4oIrBIf/dUbzdhwTrvtMU3ZNVKwQmGae3gSiqppo7G8HAgnXmc43rfEaD8yYLLQ==", "signatures": [{"sig": "MEQCIA29nrnDl0FmEr6lNgTFlHXj8Fk85jJyiioeqIyZsuLvAiAfCyYEPWEGWmap6iepSYF7Y7fufbVvfHGHnFKRZKoevQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1mQCRA9TVsSAnZWagAAhn0P/2epVeEU+Aj44WqDQfNA\nbLs5gc344QRNuJqJy5z8hLafKPiAXofKkeNipXWZWaaM3cRxp8D3MZrP/FqQ\n9cDc7+dt4+2Y18DeD2jaWPWkvilbEIz2aee/Pb9RjCVF0wqhMJ0eNXi83NoW\nEFlQe0oY2OSMXpZydMoNtGHQdeIVG4SH1LEbtSis+d3qaTW6HckTWbuTCzdU\nzC+HiO4V9hEAidXRkCTbl6JtFd26VO/YNqNykTbF9QzqBpbZ1irxNgXehtoT\n3JYLAdUp8Qa3NSdsbI6Bx9A8bmKpp9/RJBJuzHgq+btRGlV9Dh6IqVSr7BaJ\nvDR15+SEbjqLUsMK+jcFxtw/tiDaQeRN0vtH+wW9Q0ciKyJTLowrDZtB/kA6\n+ty7PY7e8KaEHjsiP/UTqOtTkx00d0GJqgqp5gQkdp7MJ2qRsgp8ze9SVp+A\n89tGEOEWQFJ3hb0qa9KGzsmwXR7jQuqYd67tQ4M8J3+IOvZyHesQ5xiFGAXf\nchpqSV1b1d3xAbRcM710TyhnI1Dhg1qIW2s+cz+oVmNHlpJB4O1Dq7GEdkoM\nUv+LG60ztrRDF9vLs2nmTxnLE+9TJyTHVkiSPQrDaKPyUeNBuaKFIDYcCifQ\nzi30+gRv/RsRem3BogXREP1VVBh7purteMlAwF9nK62XIB7VHTsf+n07jJw4\nRgyR\r\n=0GF7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.19", "@babel/types": "^7.12.0", "@babel/template": "^7.10.4", "@babel/traverse": "^7.12.0", "@babel/helper-simple-access": "^7.10.4", "@babel/helper-module-imports": "^7.10.4", "@babel/helper-replace-supers": "^7.12.0", "@babel/helper-validator-identifier": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.12.0_1602705808148_0.22986284838149817", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-module-transforms", "version": "7.12.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7954fec71f5b32c48e4b303b437c34453fd7247c", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.12.1.tgz", "fileCount": 8, "integrity": "sha512-QQzehgFAZ2bbISiCpmVGfiGux8YVFXQ0abBic2Envhej22DVXV9nCFaS5hIQbkyo1AdGb+gNME2TSh3hYJVV/w==", "signatures": [{"sig": "MEYCIQDMWAKck0EWApbBoUjYRyjJqdi05SfYLWEMCh+n+PRcOAIhAOyw/jA7KiKHPh989Bp32M27+ia1CjGGt5NEsaMrqEZ6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiNAzCRA9TVsSAnZWagAA5J4P+QDfIQT6eQXtFDmY1Cxr\nx73p04A0p6jQ0lKvtG9SIQb4YXZBn7c9Am1D0OqqXwlCBQC+7CuN9C+5wIXn\nA6Jo3vO9L7zTbLkQ32vKGChWKnP93ctZQk1eNZSdB/tPa7ACMJU4bGJlx1aX\nL+JVhWFUQkuEduwSgYoF3h4KnIcv7Biy4FQSMg9BaozdhVeGiLSzSoDCtQ8o\n3h7h0nkMJ3ARqMOrTbbi8iKAFV2zmuVVR7WmebRlIxbj0EStDjlJp9JJTVQH\n9DKkt++7/kqFTrmz3WmsM1DQYfOhZhTXe3Jg57rnB9FRD+tS1XMwG4kZc4Vg\nf/w0k12oaZ38PkLIulZmZL264ojk7BxQeCtZ1Dnqh5zKDDnt+Bbw7lZmf4UI\n1TtfssUa4BPUPeZ83mOC4EcDfGkzhUp7kWojub/wZBLm5sp6G3USZOyGmobt\nlqFjASGKFmHsuPNE+OZ3VM1xYt4KFB67VzeAbLCRvRXUJqxhGtX+/W1SWt09\nWG4dIx6gy/rshDmSwyy9uq8tFOuG99VTGkCoGSVb+v8cJqbcHp2KfDjX27tv\nblD9QctytFNy78PpsHNeJeO360hdg6WBLl9OlzOIsox/74EVCudOgYkd/FwY\n3gRSE2L1LYZIM4uaNH7yLH2w2YRUeEMpy8Z9XyFMymcA3HrlSCEmMht4eyGf\nwNrb\r\n=sawO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.19", "@babel/types": "^7.12.1", "@babel/template": "^7.10.4", "@babel/traverse": "^7.12.1", "@babel/helper-simple-access": "^7.12.1", "@babel/helper-module-imports": "^7.12.1", "@babel/helper-replace-supers": "^7.12.1", "@babel/helper-validator-identifier": "^7.10.4", "@babel/helper-split-export-declaration": "^7.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.12.1_1602801714584_0.5680680650661563", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-module-transforms", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "01afb052dcad2044289b7b20beb3fa8bd0265bea", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.12.13.tgz", "fileCount": 8, "integrity": "sha512-acKF7EjqOR67ASIlDTupwkKM1eUisNAjaSduo5Cz+793ikfnpe7p4Q7B7EWU2PCoSTPWsQkR7hRUWEIZPiVLGA==", "signatures": [{"sig": "MEUCIQCCWPBjEkdYz/SJJLpSIvEq5guvqnaYuvzEyTs7IbTEhAIgTHkC8t1JiYyGMsThwgrePIFJM2mv1rg6Lf/WuqRzKI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfhdCRA9TVsSAnZWagAAVxMP/jE6LdwrT+RsM+eX9W2Y\nbYReC2b8Yl1KgwqHu+pWoNX+hWCyvjUtJJHavdjTpInHTJiMW26T1Tua/8DB\n6v6zHMdol4jRVNj29wJ1HLL44Mgbl8fLbIcy5Ws1kuqycV42n++TGuUNH7/R\nUUtX0/tUqJUNgx+YR+OiYklmtpKqOYtrBdUYauuTXzjcFM1GjGDKuBiS3zHj\n6FHyPP60iJg9dQ2kOs/3MHe440kNwj1IDVOhf3JyUC4Tu1hfA4B8PeoPMFe1\nJmQ0Uh/djgahTwX9hAEyoDmekVUz3MSz+kERosz0x7ztmUM9uXDhXnl46g40\n95Dcu4ZZe0ZCeIXVf/qywrCyHaoIjreX386F//jKOzvRxCij2GT2acNcTN9w\nexF+olTT/0RDnCNUTCDar1anjvb81rHRaZDUluyFLYBw9ZzllSVWaJ38RQCS\nZ9dCFFADsh1MHzhX2nUR4dypf6lITK1GHLLzttlt6hULfPYZnBqTcz0w2mS0\nfJk3BxZ4qzO03xXGlRxhEpjbzInX0B8jUL/NaE5ScMUDbZtj/benE1TAAmjH\nlKjDtdnqdBewH0FQGz/VG2cbJZHWSbf5DTLjiJ4b2KFxlCFC49nlA9H6x5TJ\nMEIMoCvbd0sg54KQ1y7GskBNxC7BTaQXEgzw6MwB1kXAqyiK7RYQ7iRb+mfS\nXi+f\r\n=mBvd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.19", "@babel/types": "^7.12.13", "@babel/template": "^7.12.13", "@babel/traverse": "^7.12.13", "@babel/helper-simple-access": "^7.12.13", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-replace-supers": "^7.12.13", "@babel/helper-validator-identifier": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.12.13_1612314717551_0.7896344081487021", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/helper-module-transforms", "version": "7.12.17", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "7c75b987d6dfd5b48e575648f81eaac891539509", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.12.17.tgz", "fileCount": 8, "integrity": "sha512-sFL+p6zOCQMm9vilo06M4VHuTxUAwa6IxgL56Tq1DVtA0ziAGTH1ThmJq7xwPqdQlgAbKX3fb0oZNbtRIyA5KQ==", "signatures": [{"sig": "MEQCIGYmQkydQy+9lGLlgmyiZm6TFZ6A8mZT4WEVg6rH5XjjAiAFkg5rk8pJhOhqg7uZ0AQr8+MZRf0B3oiFl2BSl4N9BQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoQiCRA9TVsSAnZWagAAYmAP/1LgSt+zWxLl2zaH1LGc\nYxfkdZXlchGSNpwXguyzljnPg4SWlcQxofri5hJWIiUGd841pmnbL24od8LN\ns4l0A6Iko2N6yJHQm+L454StxaNOlI1B1RxtshKegnnub9kFmi4NioA6kev2\nEJqphmW6cgAYYw8Q/hb2oZLec715+ghyKCRCJWNvpT9QItCNtnFOW/9PN+7r\nOgkRH/4NL94IYYyNm+MFFSOrZpkM+46fDY7SCzAqXzYFLcHCG2k+MVemjPX0\nUb8SSeleNdFMFoZzC8IRvzjii6I3z1POmY7scB/mwGt8VAzxYWtxbwj2OuVV\nvYbKTzq1Wx0EuK1SwULmDRiJ0IfNWYIkjOInH+v7XqqVQxwTEcRSKQkskM5O\nJo4F+LB2GhU+ZjRei3TpquW0kPHwbVBSRHULgr59JGakbumDj4JLCA2wAT/z\nlnmeVbgtZkjel/Q31v+xkjZXwfD+xdZSuxRFPLbvkPLI47whwzTvQUvHvBVa\nG57coEL36rD/nLJw2XhSsxIoIzDljcmCrOq2KRIm3SAPSaji3GGAwK9BUhoV\ntfOFcAqHWqDQDnZdK4RTRxTp5+bU0RjfuKOxdNTrYhpNcztuylmxX6B/pHfw\nNR1DL6ljXQtKL9vjvBiR1pkNn/1q0dL3RAukXY7vF326n97qRXsznqs0J4U5\nlWXz\r\n=5I9C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.19", "@babel/types": "^7.12.17", "@babel/template": "^7.12.13", "@babel/traverse": "^7.12.17", "@babel/helper-simple-access": "^7.12.13", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-replace-supers": "^7.12.13", "@babel/helper-validator-identifier": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.12.17_1613661217739_0.8437080688785537", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/helper-module-transforms", "version": "7.13.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "42eb4bd8eea68bab46751212c357bfed8b40f6f1", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.13.0.tgz", "fileCount": 8, "integrity": "sha512-Ls8/VBwH577+pw7Ku1QkUWIyRRNHpYlts7+qSqBBFCW3I8QteB9DxfcZ5YJpOwH6Ihe/wn8ch7fMGOP1OhEIvw==", "signatures": [{"sig": "MEYCIQDn6QdAgPKrJe/r9O2iaX+3NisJZ6sjp8xsX12pfZSXKAIhAJykcMiu82R+PFbMs7J8FWtdXBVh8S6SxCRMWSV5OIm0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUqCRA9TVsSAnZWagAAGbkP/jTRn6VeTO0Osv5DmgJU\nzPBwuXyaGtQPbQZN+TxCh17tYku/Qhx4EuJyG1JA12IBUB5tDe65C/faB0a1\n+xDCRNARwjs4euBvgwAZiMB7S5/n2F5KjmSEawsJSuoFbV3i9hLh7Vppafyz\nrG4cG+VzL4jU/1hmq/T8soJ7Gmj/Jn+4tg3KSWGxosqx01DIaQ3F9D3RUSq1\nsq/yO+x9D3Uab1iKlsGSCkX+jSVqdoATsU92fiyWQIzklTwkIzIPGkfYRxhI\nyM0hm8yefW5H1LAsrSfHMyV6xReiHbRZEL2ayP8hbi81WXzRM65mXKbPiczs\nhGuJw4UUJEL/JqnwpGtfSgJvGFxu2lhm/fIfvIe3lJpNPdo4ajgvuEw2kRtt\n1Mg8vZ98DEFRv8VLD45VyUpPvOFopcg0DpUwDcV2GI8sDe89u8lwtpuAxUyx\nVl+9c0g5m3x7+K+8i9oGUT83GGZwtmwiceyKcQnYKzkA4PSZHHxKma1g94R6\n5hmHq4jX1a2fqHlkPBJ1yK+mPJIXYybeFMthFpp+V6YMquKxefA8Gtu9YdRA\nylcg5vpDexNQOT1pfsU+A7LgDXXYfasOYZH77HtTJcjcjI1/FKmlkM8NOzPE\n2aJN/5TCJIaGFEOhTGYjPt3xNX79pfTPcsVSB4JDLZ6BmLetI4/g+hGW6S/7\nj8fV\r\n=K2Iu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"lodash": "^4.17.19", "@babel/types": "^7.13.0", "@babel/template": "^7.12.13", "@babel/traverse": "^7.13.0", "@babel/helper-simple-access": "^7.12.13", "@babel/helper-module-imports": "^7.12.13", "@babel/helper-replace-supers": "^7.13.0", "@babel/helper-validator-identifier": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.13.0_1614034218587_0.542230228586337", "host": "s3://npm-registry-packages"}}, "7.13.12": {"name": "@babel/helper-module-transforms", "version": "7.13.12", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.13.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "600e58350490828d82282631a1422268e982ba96", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.13.12.tgz", "fileCount": 8, "integrity": "sha512-7zVQqMO3V+K4JOOj40kxiCrMf6xlQAkewBB0eu2b03OO/Q21ZutOzjpfD79A5gtE/2OWi1nv625MrDlGlkbknQ==", "signatures": [{"sig": "MEQCIGIrjUbCPVG4z8Sij8S2SonNRybPyYr9wQOqvd4REsQ9AiBmk8C9NPwBwQ7wcRiM9u/4DfYP0Q9TlGEqWDamY60g/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWLwOCRA9TVsSAnZWagAAaP8QAJUIapOYPSEFKj22RrK/\nOwW/dF/fygPO38aukELVlgud9udisi0EbTyhZkhGJoElc19T4Zrdgg+W+DfG\n81Ab7HTCOj/LjzAw0J+s3M7YBUif3QfBA5WZpHFPaQLLI2eolMB4mmC3TuWA\nn1xApN0Esx+OK14siXaUSJbgoqdy3Jr3UaJk433EYJ4QmGD8XZlGc1dSOldA\nNVCplGXM1h3jEFd2sExAEl5Led/qdqMMY0pRXC92I8Hi3TeWoKh6Bu/Ynemy\nygG4B0WE/pdWD1qLPTgxi3FZ2VhRbQm8Hw1frCPqHua6I0A456zp5SwxFIge\nT4LUxonbQe2GtwdgQdd3U6Oj4BKd/HRPu4duwglNBB3pfE+ez2ldAAWHI6AP\n3iswa7z5yvqViuRUt8f19XFdRe2UHnvIW+GIeaOXeoKRMPr0GedP+aZ4r3qh\ntYIOuLhKEQHjGsVOSN2uO6YxsU0QSqN7HTLH9qjPl6MSDDJVU+lasrdlU56h\ndngp0i1qaVQ7aTT60DRlIWcL5iZLA9uxWh+iBGU1ZI1/93ymrecFj+sR8zt1\nyRJRrFa4zlXhfqTfJFH4YGhPsCmDu0GQK0AmEoa20A3p6MTZhCIVbYIoD17C\ncmkyCmIfFruUldilpxZ+F36BYJx0vvKDGZJ/Jy3IRTpMwUGxO1Ad4gAWOkoj\nXI7E\r\n=kaTm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.13.12", "@babel/template": "^7.12.13", "@babel/traverse": "^7.13.0", "@babel/helper-simple-access": "^7.13.12", "@babel/helper-module-imports": "^7.13.12", "@babel/helper-replace-supers": "^7.13.12", "@babel/helper-validator-identifier": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.13.12_1616428046061_0.5274718536522685", "host": "s3://npm-registry-packages"}}, "7.13.14": {"name": "@babel/helper-module-transforms", "version": "7.13.14", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.13.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "e600652ba48ccb1641775413cb32cfa4e8b495ef", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.13.14.tgz", "fileCount": 8, "integrity": "sha512-QuU/OJ0iAOSIatyVZmfqB0lbkVP0kDRiKj34xy+QNsnVZi/PA6BoSoreeqnxxa9EHFAIL0R9XOaAR/G9WlIy5g==", "signatures": [{"sig": "MEQCIEV3BAA5Fbs/cCgV0W1HcvM5pf2PEQSYpjFSKZtDFqr8AiA2DTZH5D64rJVbH60nZkRk7hRq/G8t0yShwbz3sc7EZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYeEPCRA9TVsSAnZWagAAPK0P/1KzwHtYxG+xCTMSEKD9\n+evfVmY/Lon1YzwII7BlZyg3hKMUQPwTyssAWq4TBFVKN7mtf3lWOPB/CsSJ\n6iubH2BTEBQ7TQp22AJC3cI3PZ4Qjj4pBBea4Xm7BIY8hr6Wna3rVDr7lqkb\nMmNByaEjAfmU3JUoWrC3uXC21QxYLAcv/jHtlGkkwo3LmZmO00ik4zuPC9EY\nJDk2jediWxw2ebY4vfxn4cdsrB7ED1x4ZjX30gxv7CsYJ5gVyO2crCqsq4ID\n6ARQswCrKiYZ+qYWDoYB9anir5tqRoolzgvFiNU901bQ6hn+nXkGS1EA2/an\n4YQ40b66GkgZmmog6lgUCzJnH8Xi7cl6iM8/verh9Q19snk0hSVfrPe0ccLs\nKVONswUaW7QQHsGVt6BKkM+4D+Nc2d/HkpGXWshHK4+2LX9Nz41GzbXC6t+s\nQQVSAtBZagL9v9B6MSAGmwXVoOQAx5Kixd/u+aT40EptLV9Ai43bnqkZpWpI\nkWO/oQLg+/ErJJRyYlr3Xjn5446kkbsHacK5H3W3pPlYGgrn5o1Z/1sSfYWM\n7O0yeWNTCwf9azErMCJt0VKQx8Y3/7HXnQRaRa6jw09w6+UiGyDmKjo5P5UF\nf97psb0Bxb84N2uLkDLpyLaMAYvco9h4CPCyN9LCaU/me6PKfH2Pm5MYCRwl\nXwtK\r\n=U6LR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.13.14", "@babel/template": "^7.12.13", "@babel/traverse": "^7.13.13", "@babel/helper-simple-access": "^7.13.12", "@babel/helper-module-imports": "^7.13.12", "@babel/helper-replace-supers": "^7.13.12", "@babel/helper-validator-identifier": "^7.12.11", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.13.14_1617027342535_0.28213119897033456", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "@babel/helper-module-transforms", "version": "7.14.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.14.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "8fcf78be220156f22633ee204ea81f73f826a8ad", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.14.0.tgz", "fileCount": 8, "integrity": "sha512-L40t9bxIuGOfpIGA3HNkJhU9qYrf4y5A5LUSw7rGMSn+pcG8dfJ0g6Zval6YJGd2nEjI7oP00fRdnhLKndx6bw==", "signatures": [{"sig": "MEYCIQDm7Qxxxr+jDmjp7iEH/iGYn7wvI0tc5kjdrCnPSOu9wQIhALNOeak9/yXVBKP+79Q98Znbmt4+kNyJ+pu6eWpd4grE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKnCRA9TVsSAnZWagAA1UQP/3JlJosO+FAJ6bbxOM8M\njbqRElzKWUrLTH2hOXhMkwHpFz3jXr0yc4zeStncuS6PjX1e/F/o+3WHoUoU\nSrcw+cVAZMsnPTAvnet4tU9dOgYvSaGnSaXhh4FS/iEtNxN75q3blQQfbWX2\n1oN8oBxnTTlLQaaLY+uciXOeGk4s5mMTXU87XroerFpqw5MqWfeck7r23myh\n8pvD2slbgx5CqXl1uL1HZkM1sPqrJpku6nCkYRhJ8WDg2h8oIq4gmpHSLJop\nz3vVCZDKXHCxhvluzBoImHpRaQKxBaatf3v1ApfY6dk/t9sFpq9k2+nBlVXU\nO0wbcz1v7fdopJbo/z8Lgn7qPxT+VheGja9MLd7OrI1rTOAgPsBeC5464Jk4\nYMNT6mgqKrg07+dfj57u+jS6QAu065ktEywAZXEBTEs3+q6bQ0iNNIXtn9OD\nhk3JcP+VGr3MLNWlbGHwTE79dg2kSbMDhNC0E49Z0cHHqHVmxDEw5bRgeebm\n0lngeJOH9EY8InrMjzby3IbyoXRR+9cMG7r2zUS43g07QzQQNuRmeMgyV2vc\nvt/xgWnFDncztfwInARh5VDt4QSWWArLi1IxksGLvKZ5IioP8JToVvmd/z8K\nxUGU4LaBRUl+5BLXVgngYljxrk+cx4PtZH/Ue4TETlSSsIEEuDugGG32p4N4\nMIz6\r\n=XK9y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.14.0", "@babel/template": "^7.12.13", "@babel/traverse": "^7.14.0", "@babel/helper-simple-access": "^7.13.12", "@babel/helper-module-imports": "^7.13.12", "@babel/helper-replace-supers": "^7.13.12", "@babel/helper-validator-identifier": "^7.14.0", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.14.0_1619727015533_0.7434325165718447", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/helper-module-transforms", "version": "7.14.2", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-transforms@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "ac1cc30ee47b945e3e0c4db12fa0c5389509dfe5", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.14.2.tgz", "fileCount": 8, "integrity": "sha512-OznJUda/soKXv0XhpvzGWDnml4Qnwp16GN+D/kZIdLsWoHj05kyu8Rm5kXmMef+rVJZ0+4pSGLkeixdqNUATDA==", "signatures": [{"sig": "MEUCIA9PIaNpSURzUfzLFSrA/mIfyyLR0Z96csboxmDEbwXOAiEA11/aZRCVuuM80LjuHdrfwM8d1EzqSfi9Qtw0rq/TSrE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvUCRA9TVsSAnZWagAAZdYP/1Bpjs53SQEBeQQXAxXk\nglHY7iGM0kkC7Ty3H6K6VLiP4u6UdKRYToiq4kx3YsTg15G77nZHlxVSvxjR\naXPpoQBIme+2jSxDLOu7ye71+ZZTpqNKAxx/eh/LUZHViZM/DgVeLi/RT0ub\nL27jHYxQ7l80KMtB3uyUpyBIBwSbMZegs37bluEoHIHsOAoHa64lrL2sJBXJ\n6ogIK9uAQ34mhJUyVJEbr8K6As7w++WTl/pFRJE867YlozyGYUycHQZVA7aZ\nxpD94D+jUoYk2y8zFNCPYV7Ly9KzfnV1HZaWHEHPW0+K4YraSJBGS6dPrVH1\nJ0XpcCXgoAn3qPN054tEa5imMpbX8jP+89PHYjVL1dHg2aHgoB6IbaTRTX0I\nwdIk6T9E3iy9FZHr1yajkLznzkc4K5frCiNyQzNBvKPB8eZS9qktu3U6NBdS\nyNWuCje5nahNDcwhS6c3w/RLnVKKWVHEAQlRDxf6n2frgWSdLX5wW0/QBBgI\nrrok+qw23cQR7PIsW3P6QSgvGBf3771Jt/7FEMcKOEIcytEAuHrIdZpRvuYH\nE7UPTqNG5lcBdZA0tglI+ED/olnDJ8IIX77PbKMyQUsYmQNIFIHYjjoKvJ73\nU+l8wkQayUX7YsfeRJfOoPknwh3cnW2Ej6EpKAy2puHEgKMyyUMHOEP3YCb9\nEEsv\r\n=LLl0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.14.2", "@babel/template": "^7.12.13", "@babel/traverse": "^7.14.2", "@babel/helper-simple-access": "^7.13.12", "@babel/helper-module-imports": "^7.13.12", "@babel/helper-replace-supers": "^7.13.12", "@babel/helper-validator-identifier": "^7.14.0", "@babel/helper-split-export-declaration": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.14.2_1620839379639_0.36798915669049714", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-module-transforms", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "7de42f10d789b423eb902ebd24031ca77cb1e10e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.14.5.tgz", "fileCount": 8, "integrity": "sha512-iXpX4KW8LVODuAieD7MzhNjmM6dzYY5tfRqT+R9HDXWl0jPn/djKmA+G9s/2C2T9zggw5tK1QNqZ70USfedOwA==", "signatures": [{"sig": "MEUCIE5AYCAsfNsiW5aS9SIfXLf7oXBeh2smssAN94Ky2wz0AiEA6SKGFVD4+btkOSrOxy3nj/qsgSGTR0AyoeMHk4n6+AI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUsFCRA9TVsSAnZWagAAFZIQAIQOcq058rdXFgNbOAW8\nemKilznM77iiSF7pTydMdtpdpPeMIh/4DcpTCUmtNKLAsL50gRjq2e6MVq6T\nHxtyMMZpntbjrzb67t+JbZYUX4WjkSeb96LSsQYjUiPSeuJEKeW2ihD8BGH6\nFZEekLiONEuPFE8vnygVpS1ozOjQ+EvHTkQVBXfeMCtY7PxbxtfQzZnt1iYA\nIA7NGGRoFkaoxlwKbbtJTOPcJI1tSDty/Wr87dW1PEkQSaRvycvCh0+gisP7\nlqnnt8RJv/Sl0iLsF67smPL/0De1uvaKfBuXE+WBlwq6hBpvfYNpa5bpi1Lj\ngNU5WhYjC8GUxUjUk8sS3v9862wWk1z/QZ2xFqdgZmI7/NRq+GWe17oDFA3i\nBCdDbQWNgxS9T5XlyiRCbGpEsohuIbATx+XO4TmXLl5IB8mEa93Iz1O8c2bZ\ne7rVBqsuLPEgJTLc4YT6+N3kqLJ98mNe4St1ZHSc7IPLca1gFVFVIDNEa2Pn\nfVl0t1c823r/GvsNwwbAC8Qkf5LNnyWClgLprOJFeGwdJg+BmYuXW4/T1NVZ\nJLuAgzKGeqjoUBlWJ+5n4YyhRUj25Nmtqpo+fmFnKOw9BekcAEnVPQoYt0DU\n80L6FKbHO6NB7V0+OplfCP0FBgng+lJkxwYc+Up8DCgKVcmxkdWAuAe7H0at\n2B0w\r\n=Isa3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.14.5", "@babel/template": "^7.14.5", "@babel/traverse": "^7.14.5", "@babel/helper-simple-access": "^7.14.5", "@babel/helper-module-imports": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-validator-identifier": "^7.14.5", "@babel/helper-split-export-declaration": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.14.5_1623280389580_0.15496312454296501", "host": "s3://npm-registry-packages"}}, "7.14.8": {"name": "@babel/helper-module-transforms", "version": "7.14.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.14.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "d4279f7e3fd5f4d5d342d833af36d4dd87d7dc49", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.14.8.tgz", "fileCount": 8, "integrity": "sha512-RyE+NFOjXn5A9YU1dkpeBaduagTlZ0+fccnIcAGbv1KGUlReBj7utF7oEth8IdIBQPcux0DDgW5MFBH2xu9KcA==", "signatures": [{"sig": "MEUCIQCwmzCZ+hQX2FytxdqjKa1kA8HCMenSZwTbZzCZqIygNgIgQ/NO1Rz24Bo0X93xGcO7AbPQb+8Xp4PIQsd1Ruh03KE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9w/LCRA9TVsSAnZWagAAVCYP+wRtZPLhkugUBuCRuC3M\naeBjYDzz01dzFYgxdscvCRuWe6ecK1OhqYtcUHXXQxcW1C7jppBDXO3+Owjw\nGWetgNl2/ravNyGf6FLDnOvAxv7f8fDj2eD7u+7mEsh64t9kci0V2TL5I2md\ngZW2CtQ06ENtMqUwhy2+nr1EzdIup04ZQrv8nsLF+nX+wyXCuCpAJQwPu8b6\n4fK2rvVghd9WOBISRVmeQw+d+B+IAziC/C5nEm4cWjOD7B8+WOSBPe7YoehX\n7ctQPdjbOOSLYVGR7EEJT6GLCQA6Pn1vFXxSrteqqxId1bfolpv34sNUqBUb\nBURVoFN04gmRSeyFKevRs5mnJHsAiI9eW0rai19vysjpZaKO1Oul5RMC632g\nB3YiM70Mr7Wx4mfdC/sjzy9gV7qZTL06NChZoNJ0BaXmjqcoLGVfISRSaLTB\nmYvKBNco4MSD1ZQdZACzwiJPzqUA1ndNM34cOoyi+Bio6EEqmsXzvBmlp70p\nFn7jE9WZFMIJn5z32AMwbQw273X+vHGUemfosckVYLypQV+zIpq/2FTUKlFN\nSOSa+4NpYQO3thmAYrQv/IKa1aG6KlvoIneji5fu7aeE6OLYSqXVDUPN6JyY\nCMIOXbmqNapLtG/iHx2qXVHqdS3euIJjIajSzrM7F5WPZrddclfh8bzJdEN+\nasPg\r\n=EqE2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.14.8", "@babel/template": "^7.14.5", "@babel/traverse": "^7.14.8", "@babel/helper-simple-access": "^7.14.8", "@babel/helper-module-imports": "^7.14.5", "@babel/helper-replace-supers": "^7.14.5", "@babel/helper-validator-identifier": "^7.14.8", "@babel/helper-split-export-declaration": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.14.8_1626804171307_0.5253097987037785", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/helper-module-transforms", "version": "7.15.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "679275581ea056373eddbe360e1419ef23783b08", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.15.0.tgz", "fileCount": 8, "integrity": "sha512-RkGiW5Rer7fpXv9m1B3iHIFDZdItnO2/BLfWVW/9q7+KqQSDY5kUfQEbzdXM1MVhJGcugKV7kRrNVzNxmk7NBg==", "signatures": [{"sig": "MEUCIQDZaSWwMGu0ZoVZ/BsUVjmlwMhuXvISPEhaV5D3tS2bXgIgactI+9Wv84rEAKU56UkCTtownCmjOrgVuK9I8WiyyJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLoCRA9TVsSAnZWagAAJI4P/1UfcOUOK9rkuvgyglEN\nzjL8BQX2Q9q/AwRGaX1FxylrKe/YmL4Sm5vD1ZhPwLsfGItPaUE44prLB5wJ\nnv54+km+s/uYae1wFYTXfT0LLBQE82ny0mPHtVNdz8CS3XVi2vcuk5q04Nsj\nGWbFo4KhwJIkALF1IfDSHjvespQXS/o57XsuDJpPHv7E9vnq06TsCVDRP2Sh\nBXqKhB5BXcbZp60q0NamG6i43mtnNmES7/y/W6g3S+0ghqSfWcKxPHRQ7CF0\nFthZMb1HhyWCg40D7mPXtuCW7Z5/+paezdeWBoKJxMELLqyvmf4OwHC30+Y+\nKx96pjHnyNPS0hnsmINbNewx8GT+1JwVkklar7ZkKlDfqllDpF+xF1Eu0ocF\nez5qy5G5rttni11KViH8obBcPdZp65OXGNysT5G5z25MEmskOub7Y9q+W9Yc\n61xQwLml1XOyEIa4or5nkOXh90S2btIwSHOJKt9vkqonnVV3rSRuE6pg6ZHN\nuzyv8VZIWkRLKYAV7zoF6v4jrwsLWZQCMiuh8wh6q4o1h59aEXvbG/dwBD2R\n7/VC83AW+ifQ8oRMaYb2nGBR3bg1CGdVZ8RMB+bwJ5MB+brsW6mpZ6YTSzGi\nG7q4zP3FT57o7wuAN7gIo8ehOlXS+r29VPAA3w54NzEfynkjEfXPXEr+9vNE\n6fuR\r\n=Io+h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.15.0", "@babel/template": "^7.14.5", "@babel/traverse": "^7.15.0", "@babel/helper-simple-access": "^7.14.8", "@babel/helper-module-imports": "^7.14.5", "@babel/helper-replace-supers": "^7.15.0", "@babel/helper-validator-identifier": "^7.14.9", "@babel/helper-split-export-declaration": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.15.0_1628111592300_0.0022685838937646086", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-module-transforms", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "962cc629a7f7f9a082dd62d0307fa75fe8788d7c", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.15.4.tgz", "fileCount": 8, "integrity": "sha512-9fHHSGE9zTC++KuXLZcB5FKgvlV83Ox+NLUmQTawovwlJ85+QMhk1CnVk406CQVj97LaWod6KVjl2Sfgw9Aktw==", "signatures": [{"sig": "MEUCIQDKO+xhatRRSehxmc3TrbeqPtRqB7WfZPCjP38M1L2QqgIgeRWk6VhVUgan5f24aEeEbjAJDdLKcSAe73efXJpn6g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSnCRA9TVsSAnZWagAAwXEP/3eDbmZaUlPS9/4Rxjtc\n34+BKpccNe7Klwl+49j/SzsF6khHtPQXw5uKzjzx38+MSXFPoBIZpoNISURT\nPq1i4Ult0OZYL+7o5HyhjQoSkelPIgiGK7s2T4St/s5/V+396CjYcz/7WORu\nfL2+QdtAYJVdiRIGc42jjY0KmwbXXyYX7t9MDUXgDGnApnmHFDKFGoeon3S0\nUxXBEVQRvx6YbpThASaGAQNYHKE7k3p3fvhsgiI2JBHdeMGGTsfkTb6Nm2om\n057k7Cc1NkqNcOgj4E6fwd0gFpm1POl9C6xKnn150ZFv/zcTVUPd6Ny8zOs+\n+vMJNgsweGmx5pzdGSV2lll2+dO+56/PBG0Qy2DaoMGr/lzYRGyUyUmGBQTr\nbTGWsCaRZzx4gGc1FKVQGFC4ny4rVu3LbpvCC5qyQj2C1e+1Q1rBx4XSYWJM\n/f7sOiUa7saRn295xqf7o/BEBlA1NjPo9fb9jWwTI3fMSK9O5JkK5vqy8s0z\nXNU3bTHV6J2UQQkYGFcNadTDozvdiQCRIfIugNPDYeY1MvRHqOMCz6ievaJx\nH7uIFLyf+mF2sfFvpWaJcd7dOAPahhKGSaWulEY19qjgUWwkxXRQ48YL9zB1\n+4w7qFMCNoW2in1vfEPR3esVftxPz11+egQwuW/Dd3EjVZ3ZRd+tRF6zsEjr\n/k1e\r\n=+9PT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.15.4", "@babel/template": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/helper-simple-access": "^7.15.4", "@babel/helper-module-imports": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-validator-identifier": "^7.14.9", "@babel/helper-split-export-declaration": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.15.4_1630618791841_0.44620979659239746", "host": "s3://npm-registry-packages"}}, "7.15.7": {"name": "@babel/helper-module-transforms", "version": "7.15.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.15.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "7da80c8cbc1f02655d83f8b79d25866afe50d226", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.15.7.tgz", "fileCount": 8, "integrity": "sha512-ZNqjjQG/AuFfekFTY+7nY4RgBSklgTu970c7Rj3m/JOhIu5KPBUuTA9AY6zaKcUvk4g6EbDXdBnhi35FAssdSw==", "signatures": [{"sig": "MEUCID0FYUaZhr5l9PLutPzS0C95suwkxkSmcBC/FXUd+eFCAiEAv480lw1/Cq65eqsXOb4l/6eaZmPn/9UjCrLKZtrgSoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38797}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.15.6", "@babel/template": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/helper-simple-access": "^7.15.4", "@babel/helper-module-imports": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-validator-identifier": "^7.15.7", "@babel/helper-split-export-declaration": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.15.7_1631919978646_0.7013151671702664", "host": "s3://npm-registry-packages"}}, "7.15.8": {"name": "@babel/helper-module-transforms", "version": "7.15.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.15.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "d8c0e75a87a52e374a8f25f855174786a09498b2", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.15.8.tgz", "fileCount": 8, "integrity": "sha512-DfAfA6PfpG8t4S6npwzLvTUpp0sS7JrcuaMiy1Y5645laRJIp/LiLGIBbQKaXSInK8tiGNI7FL7L8UvB8gdUZg==", "signatures": [{"sig": "MEQCIEBGEzM2II0j8ToIiojfYq41KmL0AM+Q+zpQgKKbrrYCAiA2eBNd2fI/FzoLo9nAUfEKuarvw2hdQj5qgdAfCIGrHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39837}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.15.6", "@babel/template": "^7.15.4", "@babel/traverse": "^7.15.4", "@babel/helper-simple-access": "^7.15.4", "@babel/helper-module-imports": "^7.15.4", "@babel/helper-replace-supers": "^7.15.4", "@babel/helper-validator-identifier": "^7.15.7", "@babel/helper-split-export-declaration": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.15.8_1633553695017_0.35265618804598775", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-module-transforms", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "1c82a8dd4cb34577502ebd2909699b194c3e9bb5", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.16.0.tgz", "fileCount": 8, "integrity": "sha512-My4cr9ATcaBbmaEa8M0dZNA74cfI6gitvUAskgDtAFmAqyFKDSHQo5YstxPbN+lzHl2D9l/YOEFqb2mtUh4gfA==", "signatures": [{"sig": "MEQCIC8cyPvrFeuz/JibrS/oX5SOAxNvCClZdIX/N2B1wB3DAiB6p6eO8rav9ArCpQAhxBE8NGnoBUlbymM7ypcZ2XsRcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39837}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/template": "^7.16.0", "@babel/traverse": "^7.16.0", "@babel/helper-simple-access": "^7.16.0", "@babel/helper-module-imports": "^7.16.0", "@babel/helper-replace-supers": "^7.16.0", "@babel/helper-validator-identifier": "^7.15.7", "@babel/helper-split-export-declaration": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.16.0_1635551275336_0.6081689820231957", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/helper-module-transforms", "version": "7.16.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "530ebf6ea87b500f60840578515adda2af470a29", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.16.5.tgz", "fileCount": 8, "integrity": "sha512-CkvMxgV4ZyyioElFwcuWnDCcNIeyqTkCm9BxXZi73RR1ozqlpboqsbGUNvRTflgZtFbbJ1v5Emvm+lkjMYY/LQ==", "signatures": [{"sig": "MEUCIQDytKZUSs6OEJNB2smM58yqxW2mbzZj8oGC1nQcJcmLFwIgRaoaO939ANxn9dFsQ30S5yVVG4cEzusZnv9EXvbkMTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kXCRA9TVsSAnZWagAAPMIP/iN5/jpA9HYnhw1ysMAH\n4MRjg1Mqh5cC2QT8qRLTBUP2XbtiK7lHjLrRXHet9XXkAh8kUJghGwGdF13u\nkNVW8zVTGpsXpmHMfJyGIoDv1ysrWM9GhIMr0HKQ0U0uhJr79rD65cAjklIi\nKGzRoVX75yjOp86VlqcvBjAPZo3pprQMooPwk8z0+QjiAGBkSaa3arF/UcUm\n2MNtw1bKdIvAFECg5rdaYiM17w7z6lzQmd1xL2S0j3L7V3j2PJoTcqbvbS8H\nc2cHTuZRzgeUkuu5+lsumhn5FNI4E/2yRyyvWB0FvYKp73o4JsQVdzalMmNy\nq0+GR85j1bTGs9EfTvqFLg4QvC5a9lFv0lie6cgpcpKTO7p0zjo6h8EQ14UW\n0PD7LcF6Kk0bBcnGyqA+EWtY0Qmg1M/vV+/N/fBBUNK1tX6kY008a15PyJdO\nxFff4ZipLa4/Dqy9R+/aECqNpSAlIXuPqDhMJL/awK0vTA4H+PmwZ4u6QWzr\nB8HVwDTebxDCw6RxB6BkEEhgCwaQs3Cu7yJzRDSYObyXLdH/hXM2bpCJLADr\ncNRrFyEeRW09RPRPGFVf3WyuGmZ/+gICW9tJA9pFNnmCHHz7+HDqHGePUDgT\nKX8doQ8ailQF0fnlQ2hvv9RWxT4ffHdGvrU7h4QVjf+QRJeaHIAmSUflwKqx\nvZ9R\r\n=aoN2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.16.0", "@babel/template": "^7.16.0", "@babel/traverse": "^7.16.5", "@babel/helper-simple-access": "^7.16.0", "@babel/helper-module-imports": "^7.16.0", "@babel/helper-environment-visitor": "^7.16.5", "@babel/helper-validator-identifier": "^7.15.7", "@babel/helper-split-export-declaration": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.16.5_1639434519146_0.293740580343528", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-module-transforms", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "7665faeb721a01ca5327ddc6bba15a5cb34b6a41", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.16.7.tgz", "fileCount": 8, "integrity": "sha512-gaqtLDxJEFCeQbYp9aLAefjhkKdjKcdh6DB7jniIGU3Pz52WAmP268zK0VgPz9hUNkMSYeH976K2/Y6yPadpng==", "signatures": [{"sig": "MEYCIQCpSSTjRecZl+oUdPmdtaUohBxnutHmhkou7KZN9qorwAIhANuX/Ga4rH4Pnsj9ZcFjrpc3lbxgWT7RwQRatRh5+drK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk1hCRA9TVsSAnZWagAApBcP/0uyB0AfnL9m9flc9/lm\njUO9iFhcA77fR/u90ma0y71G2Q7lhwZedQW63IRUTfyGr67O8j9XWZZqBEB+\n/qYdtTFKJE3nHeza33ImsvHNF9L4Nd+QdrcMUSAmSDF/3v1wcZVRyUrRvyNf\nsaW22hHOsg/O18rUIJUn0xhdMWdFWz+4AnewWI9pV2xTU7nTC3PjlAL60Oks\nX3MZ+uKR5G/3cxXaIK0jFRqiuFDP8nzu0M/BisQ7pf0vUSHCAIvb66/Kwwm0\nJ8ahkuUL7jviudMSzXkXFQFDZ7HKVP0LohUWSiyhIMNuz2ekjgpy7bJB3VSP\n9leqVrT7SSfNkZJN1S6aXX60D1GsUVJCEBI8QuMQobSa2EcuiP0N9P+qHuBU\nEA8xU4RYuelVf/GDegBR4lxRjffcp98gP2nbkYO87tkCl+Z0DEVuYYtD7LGU\nbK/lXE0K0mER+z8rf6FEagF2NNkc/5cYa39ksLVSSQptA6D02/KFHrbe//vs\nOdherISfodjNNxtX2P+1UQbpflDTM6DegP17+4PbiboNIR0HoOc8Zkn9p/Hr\nZOClIbGQutH01NajNhUwWRXGqexN6OoFR42Nt2JGqyDC6F+QGAmW1jX/htmX\nsac9vY0lrmg7N+VCOZJVaRKk0OPL8KG3BwFBgRS8kils7Vx4CFU67FS7JFGE\nt6J6\r\n=oVrD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.16.7", "@babel/template": "^7.16.7", "@babel/traverse": "^7.16.7", "@babel/helper-simple-access": "^7.16.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.16.7_1640910176851_0.7534620515870849", "host": "s3://npm-registry-packages"}}, "7.17.6": {"name": "@babel/helper-module-transforms", "version": "7.17.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.17.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "3c3b03cc6617e33d68ef5a27a67419ac5199ccd0", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.17.6.tgz", "fileCount": 8, "integrity": "sha512-2ULmRdqoOMpdvkbT8jONrZML/XALfzxlb052bldftkicAUy8AxSCkD5trDPQcwHNmolcl7wP6ehNqMlyUw6AaA==", "signatures": [{"sig": "MEYCIQCJeMfxra+gwqj+kSwf3wSoDE46vMtDJXmY5FLRFjQSWAIhAPDYl4Zd7NLlcnhzBOLUSdBDxK1riTHygeKj9oRTPmIY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFBkPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3jg/+P7xSJmBCCdtIb4vkFlmIH4/5yPCEhhp0nd7mbVhcx0xrV4JE\r\nOQkqbzrAj26j+p/sDuSH6jVUdXxC+5w+MnPRKgufpqRbJAjHU/gEm+cpDfza\r\ncHOEoDkL+vr8ZaeC64PiHCZQE+CeyGK+IzD9FWgswOg8VYR2BhC6nx27LwCq\r\npxyMGbm509vI7Ms4xTOuU1u+SCmmA4KC2ZOs481jCL2ZDXRdFBVPm76Tb+KI\r\nKQPFJuktT48JCMPjU88MkivDhbQYhYVnUcpc9GVojk87u7W2/OPxVKvzD3gY\r\nlYMwh0YjUqZXcvDgSUuliZ1GrYEXjOwi768ya4GdpS1Ql1bRx7YIX4DrkQp8\r\nxPBtkASC52c1VbYrIqZIORNz/Jr+OU5sMKe7Jg1Rh81Jl2A7xDZbc0vmXL2J\r\nahQlhETipA43u0nQV61yL3tA1Ck3cJlNfgaeDoewMWT0Wm9/DVLmqiqTHBZW\r\nweNYKr6B9rqQQVeZTS8Y+X0O3aAwU3gBW7XuI/ncF5NxWbofgWBAza38i7oY\r\nldHSX6I0NNPGoRJ9r7gB2ikO3Am0u8EoZRZVZ8kg88T7SCGpQw+hdG/9oFKD\r\nkCJF2CfqcLm/Ho9eAWJIAoQPowZMj16SVeLVOcLVNklGIoiaGFphlbIu5ytk\r\n5hOqheHOHES2zsBxdAvEzVfwUxWwKTsRx0I=\r\n=7Y1g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.17.0", "@babel/template": "^7.16.7", "@babel/traverse": "^7.17.3", "@babel/helper-simple-access": "^7.16.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.17.6_1645484302897_0.41730826211758876", "host": "s3://npm-registry-packages"}}, "7.17.7": {"name": "@babel/helper-module-transforms", "version": "7.17.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.17.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "3943c7f777139e7954a5355c815263741a9c1cbd", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.17.7.tgz", "fileCount": 8, "integrity": "sha512-VmZD99F3gNTYB7fJRDTi+u6l/zxY0BE6OIxPSU7a50s6ZUQkHwSDmV92FfM+oCG0pZRVojGYhkR8I0OGeCVREw==", "signatures": [{"sig": "MEUCIA+N8MHeN4frqivmPCQ7Iu6iMfEXO2VyTosAY4Cii5hwAiEAzyIjWFkG7J663PXX/TG8NjDTbcnLO3iuLLrRKida3ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL3ZAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4nQ/5ASJrG5QGPb4Cgl5l3XmN5d1nFjurX1TBYVj4A1HP7uu7fbvs\r\nMSnFTtA8r8zW88YJFkJdG/wg05s6ltc/nEL10679QiV2SdeYrtyxHqlNw+39\r\nsDp0/KobzX8Uq+BGKGZupBA/dznNx3AvJ3YyjDbPk1wUS5m6PBoLotRcoPa9\r\nhl3nYJ/R1z+6SWAIo/yE+pKzRCZi0RbFu7dI1TkL98/pyjHgp9V5u9XI7PyI\r\njFGHG8X1tECr2JUw/Aw9bR5WBDANl06oiAglCk/mlsGceVzN5xSTzyQkkZK9\r\nH7l7uv/AGzz+ziiusnmHLbgOLshJB6BYSJfmvo6Bsh7YHmgsoHYFjzBwrLjh\r\nn2z0gfd94Kl0DNRSL6OVpqwjIjtp6KzfmmsUm4qdzWSqPMsp+W3MUKMU1EtU\r\n92OXN3SgcCRlOcxiK9JKGqSg5n3Mu6lbIIgMsCh2lAYdofSwlAndTAG+1xF3\r\nSoNihJnW5iv2yAZqm69XMame/x19FA7UoQg9kSgkDrQRqTVCTDJZWLABOiTJ\r\nU8tQPQS08FUiSAif0mysKBpu0IK7hGr5qvSTKRaRNHXZ+Xb3brTfQkJe1fMe\r\nDcEopSxHdmWigC7zVqtCyI8jCe6HKqIftrAQ4c1kstu7MA+8ypfESHkTunG4\r\nZtf2pnLuHzjv1xWH83s4tEOtj4xjZ3LHpvI=\r\n=BkiE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.17.0", "@babel/template": "^7.16.7", "@babel/traverse": "^7.17.3", "@babel/helper-simple-access": "^7.17.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.17.7_1647277632509_0.5354913004227393", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/helper-module-transforms", "version": "7.17.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "bec00139520cb3feb078ef7a4578562480efb77e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.17.12.tgz", "fileCount": 8, "integrity": "sha512-t5s2BeSWIghhFRPh9XMn6EIGmvn8Lmw5RVASJzkIx1mSemubQQBNIZiQD7WzaFmaHIrjAec4x8z9Yx8SjJ1/LA==", "signatures": [{"sig": "MEYCIQD/sEaUFHfLc/aokB9KYQWD5+TXEAJlUPzhF9o9kE8TkwIhANK/mDF7EouQ37ZC5LxXmAO7dsMetaKYme/8XZxfPBnc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqb6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmon9w/+Lj9cwpnevtHES2kD2TsOVbdyuUmn8QqhNpYmXkZ8JaUDwgvG\r\n9Kub0GQr++Slp7p3P/wmDHqP5C546x8mGqhX15BnBSdW3JH7qaijdF0Twuq9\r\nVybPjR69P+AFlYk4u3JV5KLLo/WtLV9fG6QFDdYWNZLn/5dasA2+K5f06aVT\r\nsCclWC0sOaotfZznr4vybU2FqKCzywfANo0T5OFN9txfEiejz2rqn6oMLKIk\r\nPx/0RtBqqoJf4hLN5pyPqjS3uczAaQnQdtCBZ6NPhV0rPuxg9aw/ap7XDRZa\r\nKEgDDLmZJyL+hvbLj7vi4rp+bCV8BQJJKDULSxgBki4pMfZNNoFMsK5pSdtO\r\n18YSMYJx8Hi26aQaMPnqRJHcrVitdv4pDYoLLm1VYYhLdwmcD0hOJviR/qiX\r\n/HuNs+MZ2lXdohp6xNNj3PaVoL5jVex4p9jhWZernJtAebRjzDZVtY1Q+EXs\r\npIU/MbPmgi3ifg253P7Pe7T2gRG3+rctKhaiB7krfd26sCGk1jIWwZA8M/vC\r\nch9if07cDAwydDLInL1I8q1Uf+kSNBaI/HCA2IAFjVqH6bopk4NhRYRJ6wzB\r\nfKw7blc/swencqA4fLecgQu9S56exMoA2DkZaNv1uF+13L5UrNMIDZuk6T00\r\nOtQEig31ulwlSYlKvDH16JuUFEEhkyMDNuM=\r\n=gT4w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.17.12", "@babel/template": "^7.16.7", "@babel/traverse": "^7.17.12", "@babel/helper-simple-access": "^7.17.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.17.12_1652729594251_0.9540627734449849", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/helper-module-transforms", "version": "7.18.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "baf05dec7a5875fb9235bd34ca18bad4e21221cd", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.18.0.tgz", "fileCount": 8, "integrity": "sha512-kclUYSUBIjlvnzN2++K9f2qzYKFgjmnmjwL4zlmU5f8ZtzgWe8s0rUPSTGy2HmK4P8T52MQsS+HTQAgZd3dMEA==", "signatures": [{"sig": "MEQCIHfnbNGULI6dHqqbPABB9bCx30Srpbs4utAmUCbkAYtQAiBtc1FYG6vptAJ5ZEZw423LRvTe5lXl/afZPvjKe7N5jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzBw//STTcoBFZgNjsCaxUj/fQQw3tZKxprnCplYW60s9A2wtYnFYG\r\nW+F4mPSvgOdEvPlt2vMIWLLhzPurXcRpKkgy5IYjXmXNMYTITtN9MNpx8wvR\r\nv5zssPnroh82MgHuDgiuVchxF1G4WfJ3RmJseCnQzNZPJcexd953BSnksftW\r\nvLUf5A2DAZhnSAPvLVCeJx3OR5fqYjpIW4yIozStD5g/XXY4httOa400BrN7\r\n0MfbXVk1tmRc7ZxbgFhL2WvXMldduBR9F/vrMsnjnDyph5sF6qLHNu4cMH33\r\nocCbG9n4fsGsqUlKJMjOt5FGQD+bUTVA6QbVfdngSGk4nMV/gs2bKUBceW0n\r\ntDfii4iz76gSxOmGgwA7SJglNO73Gw2pYgT56+2A2t5s3EyVfw0AmFdrwRcz\r\n2O2GAzYlsR0gqGNIwnFijU7focPmwWEo3WET/wNM3qaOPEeBwYfli84kjSIJ\r\n80lVZRbRah4SH8FV6+Gm33XNkoWq4cMPHr1UBcXWzQZ93eaqmCaxI4ceh3u5\r\na6DhdfMh4DvLEYA2JUL73oqbToAbgxm/ZP+RmkTt+HOHtGD/oNMFc7FFH1HL\r\nWriXXO1DRzGclBtf8AG74kjpJSjIM9zQJJ3fzVscmvev3wITBe65kztSP59z\r\n/B8b2uCdTT7OUWFS5KCmpBd3aiYLsP9YYGw=\r\n=bGr3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.18.0", "@babel/template": "^7.16.7", "@babel/traverse": "^7.18.0", "@babel/helper-simple-access": "^7.17.7", "@babel/helper-module-imports": "^7.16.7", "@babel/helper-environment-visitor": "^7.16.7", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-split-export-declaration": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.18.0_1652984199587_0.24697609375782847", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-module-transforms", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "57e3ca669e273d55c3cda55e6ebf552f37f483c8", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.18.6.tgz", "fileCount": 8, "integrity": "sha512-L//phhB4al5uucwzlimruukHB3jRd5JGClwRMD/ROrVjXfLqovYnvQrK/JK36WYyVwGGO7OD3kMyVTjx+WVPhw==", "signatures": [{"sig": "MEQCIHaTOXjPLKzqWAXvalNvm60vWlUIq6arSfrX5knN4uKxAiBrq8DyjbmGC6zx1Z/b+9WBM8H03C1QkfBUfnmNtvTG4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugoJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqirw/+Ln2lLrff94V56B8ORtrk+NWQ9K6oMGzB5TsDBEU/50NyTCH4\r\n42cKWtFkEx6p+ppKr9C6bipCm9MrfIHePpQ9LVVgbS4cnUvE0RPsdZBv9izI\r\nhkXtRVDUjzJGsiILKCViL6tHjoXIyKMiHdsyplE1XjHrQ7V7QhDDT1ITLtCj\r\nmhCkgS5dxxRNcuBK9JpJJ0fvP/DbNNFWVoZqPrRc2Q+fjnNptDbg/JP5tHZc\r\nM1qLtmNCOl8mx/jzKYjrBQtSZdqNVot+ZbQIscYdAznRnhjc8RaFg3aC/qLN\r\naok1MWHIigYScmcxeAtdSjDLprXSKLu1HFzxW9VPAEbUjcPWSTr+yEby1dGF\r\n77sOnVCZXaMlh2jopEiplr9cUNpmQtW69rtluwPWNxll4HNJmnOHngBP75Vx\r\ntBSgnIla4+UesE82TmNXkBR7lDH3jBfvVD6g1fqcx915kKBDjPTqQNpcaFzG\r\njPb9L11FR/l7rt9kCQv3woY4FQGTbVo2B0ZP7/kJ7oQVTDd2k1AB2dA1N1Pf\r\nVN3F4tKJqzGZFqFDEHnn5AG7C5pO2XaooqlcqOIYmcW8nyzxyytY/AZI3i7b\r\n4vDdp2lFc1MzktUlU9MlcZDPxvIj+KAR7VfMeSkGlEUBXf2DZqY2Bw46xaAz\r\n1NaPtf8zqaBMPyavUirPLM6VbQH41pMfnkw=\r\n=cDQq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.18.6", "@babel/template": "^7.18.6", "@babel/traverse": "^7.18.6", "@babel/helper-simple-access": "^7.18.6", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.18.6_1656359433472_0.8986980010932479", "host": "s3://npm-registry-packages"}}, "7.18.8": {"name": "@babel/helper-module-transforms", "version": "7.18.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.18.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "4f8408afead0188cfa48672f9d0e5787b61778c8", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.18.8.tgz", "fileCount": 8, "integrity": "sha512-che3jvZwIcZxrwh63VfnFTUzcAM9v/lznYkkRxIBGMPt1SudOKHAEec0SIRCfiuIzTcF7VGj/CaTT6gY4eWxvA==", "signatures": [{"sig": "MEYCIQCyKGCM7JJp+57eIvBmqq3HZeFrfQCEmZo2hCp2ZNf8WQIhAIZf5EI6gAAjgOGiFlEQjsCLMgvRDRgVWB1az3M1Bt32", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/m2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCQw//UgPsfL85pHEb9I6bZJLGjNRLzAMoacFV+7OZEFXwQHzyJD7k\r\nsxsc/pUWAmkjx33IrF4Z8CYIdE5NOT9VXz5R2y/5rkY4Ol8kiGbSLgG3v+Y+\r\nv5iWZoH2JThk40JbqBq13iKLSk6fJNaSLXH9QAlxpxekESbzNrkiQ1fMaXCX\r\nNjR6kNMzsr/QybTjyKTfo7/j9xdAhRIe5MbbVxbpUqHhfHwvHEAAxzCb8ulN\r\nAr+SN1Cz2izhgpWpo8L44KF3HOI5OX4Yksaqxy3/BYXbmIq2QKiQBeNnMv+b\r\nqG/ca0hW3iNRwCpvpNDb3AIWekYyFf/6thRaXgRAtjgIQKCHmBz6rygZWXJm\r\nxMBBPVo3dlptMzKteRHfWi2ok7+Gp+Ox74HkfCgfniLyRHvX+vbUoFuYSk8J\r\nlj17riFuLTHxqlEP0/ZSjC0aUqJrOsQF3czuDPaQRDc05o+IcxRR15C74jU5\r\nn8TEWvyPsFmJbRIhaYOLh+H4NgB3WCP5SCBb7TARxxAAzzJCnmUxTn/iMkOC\r\nYNAH+R86NxTC29eWTwbvawMoXKuqntGH3mMRiXlyW1Yp29W8o8DEwRcaqobp\r\n5AHkdpE4+BiGVMdTyCRAHHtxdSLx42ROEHZvwe1Q6icBz3QZjnrP4Ii3Z6oK\r\nLe/RcOAMyJ/FWpHac/AuBCzZ1CiVZnuCoK4=\r\n=fTK7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.18.8", "@babel/template": "^7.18.6", "@babel/traverse": "^7.18.8", "@babel/helper-simple-access": "^7.18.6", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.6", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.18.8_1657272758690_0.9642389972389589", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/helper-module-transforms", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "5a1079c005135ed627442df31a42887e80fcb712", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.18.9.tgz", "fileCount": 8, "integrity": "sha512-KYNqY0ICwfv19b31XzvmI/mfcylOzbLtowkw+mfvGPAQ3kfCnMLYbED3YecL5tPd8nAYFQFAd6JHp2LxZk/J1g==", "signatures": [{"sig": "MEUCIQDJhpUVoNBwxzcGxvfEjQSjV8yLWWhldBSERcISu+GaoQIgSlNNiL62tkaqlielXhVaPo2LhJL4NitsKNkjum0I5EY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SU0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr35Q//a6nO13PlqW6WcZlFgWBEPTjSY16m0tyYlQlmIwRT0kiwFAFB\r\nSV+NLkNt6WBqglyPUYiF8HLcVMGPGGdrjZB1JqHZfqmIPkexFGUjNC64catN\r\n3P+dyH6bgcEKLsTsq//fHXcetlVNrW4g6EXaeIlWLw4KJCT/mgCg3usMoA/n\r\noGK6JZDWb/2FbsSfDtaXCsHSePxe8Op89c7yuj+EtRtXbgmA+nBbn7MAoCba\r\nZ3uLbJWVPku8cdaHfiwh966I66u4GRslqMl3xlHvv6emozrA+MIo0gaaOM0S\r\nPfi5flZLfaFy6xl907+VQQitvKoL4/VfOUkEcbIAIZHh3jZ2H7dxOVfKmBOv\r\nIcH2FrMyaRWDlaWTneiq82N2JQm4ixeealoKX+YpbfOYOZ+/neOFBjxXM5kV\r\n6SKgTGZQL8/O5C/4tE5orJn4rMvsgiTYt4zv24DR+cMh4itwP1+7sTOy3SqD\r\ncRJwOkjc0qCcib58Ht52ZI/9CfMx+CEekL73Uz835z7t7Xtm3b9VwCJNMDI/\r\n8tKwVHMJxmLTHXXCocKp1aVw7GeZRa/mQ0bLVhXPG55xBpavaaIXy2+98cx0\r\nrBfeTxoDPTDhrCTsD99LuQO9SCGVWYuqxZN++bJ85CQ7BfnCCjHk05uzhZu4\r\nwrWXWsqS1AaZoILVX9H8HQYadQM9o5opXac=\r\n=Sp8+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.18.9", "@babel/template": "^7.18.6", "@babel/traverse": "^7.18.9", "@babel/helper-simple-access": "^7.18.6", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.18.9_1658135860192_0.042805690952458564", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/helper-module-transforms", "version": "7.19.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "309b230f04e22c58c6a2c0c0c7e50b216d350c30", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.19.0.tgz", "fileCount": 13, "integrity": "sha512-3HBZ377Fe14RbLIA+ac3sY4PTgpxHVkFrESaWhoI5PuyXPBBX8+C34qblV9G89ZtycGJCmCI/Ut+VUDK4bltNQ==", "signatures": [{"sig": "MEQCIEecbvenRNQF8+oxNsA7L4f+Mq5N94GhPqqw7mAv/wQ+AiAhgNcsIQSulYbNSaYZwmVGLK8NS7oPObIOFFsdsCUbSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzDQ/+KlI3PkoEjK25IZVMnssgjTa4HHikut2YrQBkzcHlXByhtCrA\r\njmqU/4/yu5cXC7vaeYyMVKJFz373TvC3lzt9D59gEwkxvZfsuarCMQILakeb\r\nUKZRMyjxVCNYqPzkL5loZKOep+Vd9VB6zySz5oIEIgSGwh7irNd4s/gH9cAJ\r\nc0NE6o7B9oJskN2gKt0XwibJX3lPq48TxAiZ/Cxztw/fc/n/c1OY3jcuLx/4\r\nGJtrqUpQpXUgMb2qGpy/GLIllw4/sXZ19/sKhJh/5EO2gsyvqz9q2TD6gay2\r\njrF9QjYpOW2K59xIqRe5DK+cIHNiRA7OBcRR+QNG2sVGtU2tB9cbUXxdaoXw\r\nZVaaRlpZ1oMd6pKrbOo3fEDZAWHqe+V3unTO3+SLmxN36yRZr+ZhYFIgcBYv\r\nFEP7CGU5s4x42JruL/OblHnLKos3wHb4Nnmvu3vfxHUqgIpkHBAaUCKTspne\r\nzBRWEGuSCRwDapObUXJGK90KXx+f4v/f/QKXJy42AhCYV1AqdURKBf3E8iN+\r\nd54q55pZdtjMfH074RDWbD3MRoIg3X4RJDmfC3iQ8J4FA7ht/6MJ7FUxqKNl\r\nnKSqkPc60wAl0IcAa1sjPgvHpZ1rG7M+QwR4IiRmhX+quavs6JhNm+Eoz2BK\r\nUUNd5Dd7174F2NDm2G7ozTtBlPkXmi51U5o=\r\n=0SOm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.19.0", "@babel/template": "^7.18.10", "@babel/traverse": "^7.19.0", "@babel/helper-simple-access": "^7.18.6", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.19.0_1662404541073_0.26812247965031144", "host": "s3://npm-registry-packages"}}, "7.19.6": {"name": "@babel/helper-module-transforms", "version": "7.19.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.19.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "6c52cc3ac63b70952d33ee987cbee1c9368b533f", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.19.6.tgz", "fileCount": 15, "integrity": "sha512-fCmcfQo/KYr/VXXDIyd3CBGZ6AFhPFy1TfSEJ+PilGVlQT6jcbqtHAM4C1EciRqMza7/TpOUZliuSH+U6HAhJw==", "signatures": [{"sig": "MEYCIQCNuUW/iujS/ZpHnLzOM24h8txz3ZeOiBETFTwenzANkAIhANXZU1L+6we8JkQufjPz3UOCGYoFxHNHcLnC0AidYiaq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUQ7mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIRA/9H7IpkyI1yzuREJbh3KBchakI+O0Qhk6rARImuGmHHCjF0CcY\r\nVl7xclAJWaIhY1AhI82PCEhIDLW63gCR4Zj77Akm0+7qh48d7rvshk8PRyS6\r\nPm9xyXtoWy8O/7JAaUH4tcu29qU6jjOvmHUSmZXsKnYU6o+zqHDHURYa9N1M\r\nE/Qf9WCanZtVsLLQSt3c+RK/Kvn/4nJDekh4zxiaOZAwL7+SOIsrUR/ijISu\r\nPIZAfHnHy49DIY8c+nKqVnm9Ff79n6aTPp+GIzVlfBwHhk0KOqzs/b9W9OPF\r\nTKTx3h4Aw37BinI8RwiD4rx8Saj9z+qFMX+Fu2yqHOlIiWiO01Thuv6hauhc\r\niD89+4xv8ztI1wqTJR+Q9z4MLQuv03NcSKEnBg2H/o6yVkn9+44jU8pgT0cq\r\n/uudGE081K6iyNPzGRCjZzLnhVBdiqilSP2XenuNn7sqkGCPvr/nQgNxP/Us\r\nNetTP3xId8vOkGlzBSPBjLFPHiVnQ+Q28p02uVMUH3Wb8mA/jHfZey4VqEj6\r\nbXdlLFuN0mWCioTuSgbNFcq+yQfc+QCVuP+eXobT8nXZbcCczhlc+OlOicTi\r\nR6EgTLULjkX/OfXT5DUIpTaheCiH8NBfbj2TzC8/cUpDRI/JzOJ00Xq5nNzY\r\n6HrOtPXIRdZ87LA4HmGQ3Gm2hPvAk26Bllk=\r\n=s1mt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.19.4", "@babel/template": "^7.18.10", "@babel/traverse": "^7.19.6", "@babel/helper-simple-access": "^7.19.4", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.19.6_1666256614643_0.573745813776773", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/helper-module-transforms", "version": "7.20.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "ac53da669501edd37e658602a21ba14c08748712", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.20.2.tgz", "fileCount": 15, "integrity": "sha512-zvBKyJXRbmK07XhMuujYoJ48B5yvvmM6+wcpv6Ivj4Yg6qO7NOZOSnvZN9CRl1zz1Z4cKf8YejmCMh8clOoOeA==", "signatures": [{"sig": "MEUCIQCsAFASktzrlZN8gkYjGpw1CuE8YcyNzTvsVvm3aRujWQIgbtwsyr6rnJj4wa/ombAgCtenBkNzAFeExAhJfH4aJ0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYNw//VX950JsVwUt8na10cKzNFrDdbtlhchwr9wUw6BTf1CO1dolP\r\n31+uljQMGwH9uqqh2BjeS4XStnI7RKdPxLquAGds2VcQBEpbFEFwiERZWkWJ\r\nCBsWo1qmyPb3FXljjHrw+2ki8InYRmuQyaqIOGMsdb9Qz8UhcDmDthsLpA4h\r\ntehHZpom3Z2mdlrsMYl5LN9JLqU9LVNzdleIvt66lyfd+54iDsu49rJfvCr9\r\ny5otMVpAK8vdU1MhHMFw55lMTbU3d+xEO+GfIx5nS7iIurAJR9V4j1aC25A2\r\n1pQNI9AziYXadhoT1HywsRj3XjVi/+sSR3e8A9wikVv2VtvPcQzXZG9/I6h8\r\nzyJlErwz91WLK8qg+CICGQF9mgKLNZ11/8vxlkDAt19ZM2miZPK9mdNgm/ds\r\nQ7KlwxeO+3YCF/3yXkkejnnQw64tjhK8qo8IL3KaJnB+M1bin6Hlx1Q3E4sB\r\nScJt9iYyZwyWQ2uhQgZ8aYgEBI6Kp5o+yv3Fjpl0SJUpRPH6j5x0pdNpsVY3\r\nzF9G1pXSYWeO6B7MW+QRLdcW45fsltKuHEKvCwtdiTSt9o8Iwl57jevVHm+g\r\npm4kpCqJ9hmxgYECrE+4y5G8zy0oIwXQWs5+8VU9GsatELsKdfrziYBG+YzD\r\nA+330N4PRQhwJAuKZuu3JG8rNaRhSwrmlM4=\r\n=q1Rn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.20.2", "@babel/template": "^7.18.10", "@babel/traverse": "^7.20.1", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.20.2_1667587869766_0.04803931362107772", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/helper-module-transforms", "version": "7.20.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "7a6c9a1155bef55e914af574153069c9d9470c43", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.20.7.tgz", "fileCount": 15, "integrity": "sha512-FNdu7r67fqMUSVuQpFQGE6BPdhJIhitoxhGzDbAXNcA07uoVG37fOiMk3OSV8rEICuyG6t8LGkd9EE64qIEoIA==", "signatures": [{"sig": "MEUCIHgXMQ2us2frL4LdULJkhOj2WRiDBM3SzbIvri5szZN8AiEA7PpAX15037ak953DNAnBlACVL+ThfcI4Fglb4AV7us0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCdAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+Vg/9GDXZnCcYXe73NouuCSxg6REkHkRR/HgTVoSmdt9w+ez+j445\r\n5hy1QMoDxl0IpsxqdwQX7QX96oQznbtgwTBVddCeT9KRvOVZNbXEltacjCJD\r\nRuJ89DuiBciMUdce8RI0S72oRgt5vnkeEyt+1cp+LXo9GYehTQL1Ct481mcj\r\niYqrTNhAPa6WBhOawvc9IkvRDJgTsAYfsWefLCDrp69tQjUxNBf+wrbTeE9B\r\nsufPTvARp+cf3fH9EtYugMWTg69nSRZ59augo6W5j3CklB1fYr+Bkutb6lD+\r\nXQIdpvWhmC02YYvqsG23c/0FypdQd190qSCaAHap02bfM5S2iOYA9nG4Rk6p\r\nj21jlZNd0PUDQofn5jZ65Z6EA40X1OKEepMTvdrtM7NbKj/5b9GXP+DbIx0D\r\nAf8XUT83bvOhhP5BdStcB03doU8czCWG2ldRVwglGH0uKIUdULm534ZZXi7P\r\nmZl0Y9AvE73aZgR2+5TJ4cvNw1/OS6D1YEQSsvkBe2mUokcZpLvpqtPlafx8\r\nWzx2uEx9UeLlxep3q33q8I+ucvJ0sSLIK5mS4DcaoTEw+nE5zCsWEbqnDzJi\r\nfmVRcOcIau/q0uFlh5V1m7evdtVjErUyUFu0gnaOyfBfxJpmS0Yda4tGl4aM\r\nFh0vlvq9m8zHsDpadxmOrIMZofn6ElAbpvE=\r\n=en2z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/template": "^7.20.7", "@babel/traverse": "^7.20.7", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.20.7_1671702336204_0.32054163495558496", "host": "s3://npm-registry-packages"}}, "7.20.11": {"name": "@babel/helper-module-transforms", "version": "7.20.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.20.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "df4c7af713c557938c50ea3ad0117a7944b2f1b0", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.20.11.tgz", "fileCount": 15, "integrity": "sha512-uRy78kN4psmji1s2QtbtcCSaj/LILFDp0f/ymhpQH5QY3nljUZCaNWz9X1dEj/8MBdBEFECs7yRhKn8i7NjZgg==", "signatures": [{"sig": "MEQCIDZusCZvXKcoSTO+jSDnB0mhrqrkuyFYcbKeeqIcvILRAiAx4QnoIthqXnfpwyZjQkturJhFJULLZ/++FISawinJAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjphi1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpthA/9EZQdg82LCpt5wkGgYf8lrwOle+poMbo3XuJuKRVwpK2SCuyB\r\nwUIiUEwAz5VlkYVQV6FNaRPIt3g1ifL+se9regT/DbcALTQ2pOl/JnhT3vQQ\r\nVMBgL1XQjddQpawtLuVWfeusH8E+9MqnrORat4/QVnc5r8ULD+cboVyN6OZp\r\ncnuqU/gohtYfMoQ+1kE+oS5HDkH1hAOpJ7RQqAkBI2L0I/MNpxP7DgYzeiIA\r\neS6ttW09ubw5HSTZNXzj037nPO/AcXOxC0a/CzgnGStgI2HQqnOgg0wGwBOC\r\nBUm5tC7wLEM9W86SwXYWc2JgCk7dTnp2jlSU1e9hKSPxkVKVGTMBCShW5cpQ\r\n0yKh2btiiMdFT5jrRIdWUWAa6Wn5JLHI2Kg/cDF1Z/1sL/c4OYOQOeMWZQBr\r\nVNrEMyif0l0vDyEmhjyGuhBFxjAAmf8GvLYGxDaw5ald/fwkMe2992kqh/0d\r\nU5UGL5F0tTgdA8baInFyMYtTro9S9PpIeIn9sAvfoVhaioGsSII6bJsfLUOb\r\nBbmz/8La9nx7GqQDPDKxYGOKQt8ALOW9Gq1rIQ6MWOwIQ8vDnq0aTdH0rb2z\r\nNgHmMCY8zSEpSBAfDFK1Kbg1fvJ/t0noCPQGRtI96iaddSNRur/zGgTeUHW+\r\nxFT5ArwE+vN/6qwM+cxC8epq2lwfsyRwXRE=\r\n=e0Jf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/template": "^7.20.7", "@babel/traverse": "^7.20.10", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.20.11_1671829685726_0.49144474245390346", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/helper-module-transforms", "version": "7.21.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "89a8f86ad748870e3d024e470b2e8405e869db67", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.0.tgz", "fileCount": 15, "integrity": "sha512-eD/JQ21IG2i1FraJnTMbUarAUkA7G988ofehG5MDCRXaUU91rEBJuCeSoou2Sk1y4RbLYXzqEg1QLwEmRU4qcQ==", "signatures": [{"sig": "MEUCIQCArbBfrU+aW+8wjUPp05OUWaBkmqxIGLQDES79QPxFYwIgFlliib+tUe5WNsRyQuNCgFrpnVVxIw1VoOnpija8Ol4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85JFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmprqg//e851NeVEPFvZqDLcUEmhU0KlT3VZeOWRe1qgGQLR9EHdD1Yi\r\nBTB553AhOXz5PWrCWTUzBEnApH+aiwtNLcz0FUEP1Nm38XL1dYYXfSyhZIDq\r\nUgQbkhwYLG0dgq9Ke00fawThLgk+w3NUuhZjFP+KayXo5sADJMjOrxE7+k6H\r\nqCb0BqZd4Qzv2UGQ3+00wUIKxbjJPktJVUkKJJOOgo3hDBMX13foh7I+5G9B\r\ni/BE/28wrbiEZj3GJnFqKSU0S2oOr0hhGN5r6gJMWs+lcUfDsemneXlnOere\r\nvHhDh9RW8ykW2zhdOBgBa3C7YB6RmZDWDy7MVvGU1dhjWHzx/7Lc1ana4we5\r\nFojf57XAc62o/MaJVGeBQzQOES0dFHnapPhWx8K0R6Vr8OV8+pbg9/Upt7wl\r\nOl+udDrK9AibJyu/4UjeU02aSSeXKMEkd23U7X4R05qCiNzFMku6D9WikM7K\r\nsFUCJL1AgmgpXwk+ED5Z1Zpa7SZ2Y7vToJNdN+lclDqoOMEjvJ0WN/JiScdX\r\nPZDa8vXo7yPFHtCyJoGh4carv5SbzEc3YpWtOBp3g+/X0T3mBTqWDp2xtHey\r\nDODLYCc9E38fRWLM1UkP4/afFoJSrKYHzck9uUDJ8sweoE9LmArNxFczjhLY\r\n3j9GrZtHN1GlSuXoarSFBJq8BTNf4sVhdao=\r\n=rMJ/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.21.0", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.0", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.21.0_1676907076988_0.7195449574030097", "host": "s3://npm-registry-packages"}}, "7.21.2": {"name": "@babel/helper-module-transforms", "version": "7.21.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.21.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "160caafa4978ac8c00ac66636cb0fa37b024e2d2", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.2.tgz", "fileCount": 15, "integrity": "sha512-79yj2AR4U/Oqq/WOV7Lx6hUjau1Zfo4cI+JLAVYeMV5XIlbOhmjEk5ulbTc9fMpmlojzZHkUUxAiK+UKn+hNQQ==", "signatures": [{"sig": "MEYCIQDh+nSRzolG3RZ1QzALtsAWy+I5L18796LtwKQMq8KAcAIhAKQGcoXh6E1XfIuQ4sV6BmA+1PwmUjTZhehY6FO9Gc4o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9zJ6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvPg//RYAS4JFB0jfOICC067eA/sBtQo1XE7uJUnXWdttwICgzLAsu\r\nevZuSWgoi1TjGC6nIRKe+eUj9CHzW4QnlUgOVVl9obBXnfRtxVtPdWYamhVR\r\nVLqawX7wPuU89P1RP3iyusMXsXdtlLDfPNVyAiiP/8HKaJUQzoJZlh52D4Z4\r\nK8L2UBMHIys3nX5+DIgv3qd2xyJKrcv5khO6pg9cNALwBR/C03bC4fxDqEv+\r\n1kDIH5+rInIGK3OIECL9ZB/nkQweXfEIGe5kYdneAKVU8FNyMC/9L1304z2p\r\nggzCKSfN40CXe6X36OF7qYWwOsq25RUC+UBEcRHzjAjMcpnQdToopwvUJZow\r\nYKz87C4HdikDDtabWFIQq//XVRy2RlOcMn4lLev0oSLjP8EHAyuaucS/2BR9\r\nLCurxkFPtMcFS0nR22Y/Xt3ZioVPMmdF61fakHzeKoxx7ZCg7TfjubaWBtTn\r\nn+siGnzH9wIpcEtkhDdBcidXXIXkOrWFeJ/SxrV0wpCzI7IFOhnvh2go2Sde\r\nSX2ZG/dxp4nCSZ9+N/+3TvwITTL9ezoJ7L1D8VkYaxW9Ds7KHV3ALJNmfH1w\r\nqiIF1C+5fOtb6pJ08ufgpOoV0DHCXAQxlePhMvnp+UKrIr1d//YWPTfiwiaB\r\nsvWwJ2IeyhcseXlEaJpA5KBBARhpu66AaNE=\r\n=Jpv3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.21.2", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.2", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.21.2_1677144698152_0.9858314382095839", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-module-transforms", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "fc2d12907edc62270a086b5c1867f8eecebe0f7f", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.4-esm.tgz", "fileCount": 16, "integrity": "sha512-Od+XrpcGEtegesa144mafZjAxLsf9F8PApExmV/YxPkQxs/AHqgmDofgo/sf+y/bOx7ow3rREv9Ey8FfeFgRkA==", "signatures": [{"sig": "MEYCIQDDUFTP1/xC+z0U73BLlKgcNjeQ91ZeMxcnqzk4KBRwuwIhAPruhvQqwmgKYDhX0ThgHFNRxMx69/kHbdzBYfQhJDzk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/vA/+NXwaxR9ohuK2366wL/TPwy9c1wnAb4Sfsw/sFfxDlGqlWejV\r\nXq0crgsOSYcAO4fuPhygCAeHO37hBiY+JsnvZm0bNMkgBD4amnkRgKA490AW\r\nqvO2hovbOJo55xP/iX0VCxpt36n/fS4NnM2lobqbbRNtPs2ZWefgvUwQQm7C\r\n1mjfaXueRR0K2ni6L1Wr0z9/MscWK1rovWW91SSQeuNn2yXStZhSrdOuGEp1\r\nRFV9tysz6OVZiF4U2ncsVXxOntun+1tTxMdBz96zJUiwg8gNQma7PZVCd0ur\r\nSWehDyMhJupaSRKrUAkM4xYn6bpZeLsxQtmVhvHDKzgjYsgAiMvWLpuXXkDq\r\nuYwnIl5jy+PMUt/rtTH2FoHhPkz84DEFFO5J2m46JkcL0sNyjUBMxhSwUvvW\r\nanv/mJJM82v0f0N2YGOY//AELrN1FfgvZ/5eZOQABSD0nD1bBM0tOG0MWs/z\r\nz5WOZzDCnq1qtcpHTWVCD6gJL6JUWBOm1OEYfnBAf6SDKd4pLWIJY6IUH//S\r\nNMQQ7k2ElxwKKecd7KKhvKyxm4RAabk1D2mUwGRDf0ddvOjnbSBi7KunabGP\r\nwUx5c6fu8CgCzieHATIdCDSXnoDH/xvb57BUCufpi6UfFoaJYSOiTyJetHmZ\r\nIssejf1jhhDC7yhHOdnVGdFg8eGf2jHUFcA=\r\n=ZYwm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm", "@babel/template": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm", "@babel/helper-simple-access": "^7.21.4-esm", "@babel/helper-module-imports": "^7.21.4-esm", "@babel/helper-environment-visitor": "^7.21.4-esm", "@babel/helper-validator-identifier": "^7.21.4-esm", "@babel/helper-split-export-declaration": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.21.4-esm_1680617394197_0.8169778183050214", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-module-transforms", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "0e08be678391d5eb1fbd801f7dcf967bf33edd01", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.4-esm.1.tgz", "fileCount": 16, "integrity": "sha512-M7LqH122+QakWGfDYK/vYbJv2jfNolwXaWs4Ng/pnlLzk3/ponmJdaq71R0cvTC/kDiazM1h1qgrkGQibwboXw==", "signatures": [{"sig": "MEUCIQCV7i90BPH+nvffWed+YQ4Xre69QB4mQgFIJ44iAAM+9AIgLfzUmDNumgbD97odkGg90PPxO+w6Gmrmg9F8XzSoZIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDKAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYQg//SH2vZesKVIYhE11W9ygwjGMrNZwBC74VI6dVY1OV6Kqsyx2r\r\nKkubRjTF8+1fxqiLoL0cazwqnY21+xJBJkeKHZ2Q6eA18XUHND00VfP6cDCB\r\nPPJI9H1zlpunlrOLS/ikV8onz+ZsV2V5T2vTrnkq0nVJvw8EmXRQKDzF86GY\r\ntgE4xT2dJJzQuGWUuH16c+mVSqCk2VpBUnTmpzodCfr2sed2zGypN+EOF2x2\r\nkfpHRNFLbm3/lk7L6mx6Fxga6DkCZq4B6BUV6hCGQn1cHCpwVwYni1yMknW5\r\n9r5TTb2cl0FBvowVIC/u5eYKPgbB4AzGlcqyh+dg+cSl418brEf18BZXA58f\r\nE7xdBLoS1KN46uHpUpsx3hU0U/9C1OAiqF5zbsL+Qkrkx1FyyaWen1bJMn6U\r\nwhNEUJfKJZjKaOe6mso4bk5/PUdS/i4Ei6rPO7b59McVBhdN/57Jsr7FFstv\r\nhXaz1Bi5dHieM4FuzGU1l9+B+nfV750fH0F3MTy9ApEfscDCEc39ug8N0m0L\r\nBVbE1fGJJpcioqqh03EO1zMkN+Bm8ijY2/hQTwsg7ZbbI3kfvtCxji/sB7Kk\r\njsQ/XaLUj8D9i54oJAbss61A82fu0z39rt+KkHthW08KXHLwMz9ifx3L9iWn\r\nWoM2AOGO3WDZl3YZdC8q3DVy1rndX6x3TgM=\r\n=hFCh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1", "@babel/template": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1", "@babel/helper-simple-access": "^7.21.4-esm.1", "@babel/helper-module-imports": "^7.21.4-esm.1", "@babel/helper-environment-visitor": "^7.21.4-esm.1", "@babel/helper-validator-identifier": "^7.21.4-esm.1", "@babel/helper-split-export-declaration": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.21.4-esm.1_1680618111789_0.5456417044096504", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-module-transforms", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "6c9cc653496ad3a9fa99ad123e0f297bdf8722d3", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.4-esm.2.tgz", "fileCount": 15, "integrity": "sha512-EZKUfFsbrp5xzIzR1AMbuz525b/DE6BhXtu8igdT9vraHwFKiO8Be4jJ8gQbeSGTEY8q6ny1838g30B0lQfb/w==", "signatures": [{"sig": "MEUCICV2NpcprE3xtzraxi/iHE0fTLxByuVlyi7Vd13Zij16AiEAzRIf1Z1sc+MVCn6Ahd+anG53YNU720r0PV5UMTe+SUc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDa7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUTQ//ZbHrci2kWFJIA/iMhamCh2ZqLK6CjckTPwjf9bKfo17VUIVg\r\nHVmV43TK94zztCmPI90FB/1ySouet4842UhGgsbXRse731z/PEKGhlIGDgh8\r\n5+h5nriZqhQs/Gjr1IM9DCGaS//YY5PqChgzxQd99dh35tjZqW4ynX0OoLip\r\nAYt3lm09VREdeZBLPRfDtetR4DTIVjzXC0LGZyg+hw3ysPoog6raxnf7gl0c\r\nc7i9wqz0RpWC2d6OBmSaTCBa3Hjw7D06gDboNzuD9aMs/YFNF8OJhcqDkCMk\r\nKO4T04FKGJTBBaqYsPJB1hkDWdval+frWDgvJggc3aZrB9gS9P7y7zW5Ni64\r\nQrbsjlV9k2/+xok6TrMfR/p1NF/bX313sNLEkbFJorvd37fw7Ky9M7hIozQr\r\nWjzEv5XvJq/62VSL84o+SzvBLy4UVIqBYTHW84LR7bFaq3j2xVMsPSPaWjEL\r\nZ2aCcWxP8SxYGchW9As3el7PqBj63Dcho4KdI9v2bNk7UKCjqt2K/kNYkv26\r\nRN+I4+cJHMbGyOid9d7L3w2CHdL3Nd4Ymp/nfZ57E0hwqrIvNEGVlcIt16aY\r\n6vULigY6rdNLLgsIEnY1OiRiOEdVz1ebftC1I21pn78WQUMr+TH6cylMkyyA\r\n3r4tQ2ghx4cH2iWOVmZXZHKuRG7Qr+n4Mjo=\r\n=CRak\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2", "@babel/template": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2", "@babel/helper-simple-access": "7.21.4-esm.2", "@babel/helper-module-imports": "7.21.4-esm.2", "@babel/helper-environment-visitor": "7.21.4-esm.2", "@babel/helper-validator-identifier": "7.21.4-esm.2", "@babel/helper-split-export-declaration": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.21.4-esm.2_1680619195645_0.8083435066449811", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-module-transforms", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "501fc5ed89d7b5bf9c138396bf878d495ce479a9", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.4-esm.3.tgz", "fileCount": 15, "integrity": "sha512-S6XvbLVeetRtaa8Xh+cGDnKsGd+VvXjdcp47TXgeMMF6fCHcUtz9dEYFivInTOuJJ9/2UsssTE4De6b553D8hg==", "signatures": [{"sig": "MEUCIA6fh3qlr2l3TVx46P90fthGJdctcsDO/unQmgKP90DSAiEA2XiGySF9jU6ArgNyXhl0lD6xhGJgijHeUzUA47XoIAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 147320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmohew/+LRioOE3Hr0GFYpJ7cis9+ybJjk+VMZ3DgoAsWGaqsL04rvmi\r\n/MewYUM9IeM0hw1x3Ix9gtCZxdliw/VT7o1PRD6qRdu1gV0bSV9893OzwTK8\r\nr8bSoEannF81RmP+0rspP531sVlNh6kaAWPPi+OmI10dE+Q29O2nApT+VT69\r\nUPCHSTY8Fgx4/TgEfIIgis3ILgq/QPiqxQlVT2dVeO1Elj5aWegHQG1l9bNL\r\nzZCRwaZTJRGMmj2WfACQhSI+QWtQFM1Q11kHz3Jtt/3xpz/R93WUfokZmlaX\r\nNV7v48TjxI//vzIqyGghWyJNDqqKwcUKR2gXJfjcBcKRjEjxukd0AqTXPZvv\r\nLGuDm8hEGCv02NkW7dlo+xUMNZTtvngyLy2Nci/vutheNw6OEKGMKZYokFvb\r\nf3dUIaY/4z8IBy9QV2Lwb0oUuLqXKKnb3xVBEIFMJ//3DqFpHD54jjoVI7u1\r\nJHC7KQud4smhUwzfJctbfDB/i13Sp2wy3vfEucUJ7Rrwnemaf1EAOsGgTApb\r\nrG00NI4K6YUcttowF+KcQR6j9c26TfHuXTMzWuRMl8SN0kbbUqlxSbXUgk95\r\nAaJRbHZ0LxifrcQXsehyVdGbasntjeUZXiVyC3hicL50vhqxZwC2zQsVdBzJ\r\nmvnjMV2C16SXMt2pFVkRaCQTzU2BgFTvu0c=\r\n=aGcP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3", "@babel/template": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3", "@babel/helper-simple-access": "7.21.4-esm.3", "@babel/helper-module-imports": "7.21.4-esm.3", "@babel/helper-environment-visitor": "7.21.4-esm.3", "@babel/helper-validator-identifier": "7.21.4-esm.3", "@babel/helper-split-export-declaration": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.21.4-esm.3_1680620199070_0.45939900078782103", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-module-transforms", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "8536ed1ec8f9bc99c3e5716d860a8cf6daa1c419", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.4-esm.4.tgz", "fileCount": 16, "integrity": "sha512-Hf7EKZmbQQQxBqMipOKwVZs9ubxiiM6pCXJPUv3OmkGlbReGU6UptgqFgvyof9m7y24XAfE2eeVZsB/UI6PgkQ==", "signatures": [{"sig": "MEUCIQD9P5E59vRl2uNbGZPSWOi5Kot3DVLDO1OpTRFXTYhwbgIgKoL1LQ9J4tp32zbk0xMr6GOlgms/eg4sxBlUXTDT4D4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 144696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLRg/9H8MfIM3I5xgEY2zY9QxJghfngmB2pog6pQKkIrhqn5T34syF\r\nn8yn5MY4TD/RVpMc6eA0axyePzflaaHHGS2OY1mrSIe+9MAtXVqD8ykTLITP\r\neCf3unTxDDKiWNpf0+HhOMvoupf89vp2XrD/2o7/9mG0ItudE3g35olGLYlW\r\nmTaCnSqWXqUXSoZoHVu6quh8pFjvBdbjUH31JZKbsumx1k8c76wbrpj035mz\r\nI9VFCrEBQZvQQs0KBS9aaEBVAkjUBPgsfvFcI6rAC3C7IR7A0CNlAuZlrV5k\r\nLR/n+wPBLsiyol/VyhA1bugZWaf/bXWDVgrF8Ps+qoYK+gAXDbxvxOP8npuS\r\ni3KylBU5gpFl8p3kWKFct8jaSWoSM3uw9goFh6zOp0Zr8ZVqnmGqc8fNDvsb\r\nQZ0/b0kdx7xBeT+vj+POTQ4omMr07pF5kKZfPyIHw7chKPXuAZa1zYl8WA6d\r\np/DZhu1KmwX8Y8OhDUpFuCyHd1ph/4mY09pql4hn2ix0oyqJGIiI7KJHltWK\r\noVXsmIxNpjF+y3jKx4MODWdDuo5JgildJALZaZDzcZW+WdbUAI19fpAYgTtZ\r\n/6Jk2kJYQlAOK6VlqvIikbxGGJMF1FqCSpdrE49RbwbAQyBcrNXU5sLIyfW1\r\nojZgUhdSHyhlTuyzE+OthuHmn5OHAWj+dsM=\r\n=cn8R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4", "@babel/template": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4", "@babel/helper-simple-access": "7.21.4-esm.4", "@babel/helper-module-imports": "7.21.4-esm.4", "@babel/helper-environment-visitor": "7.21.4-esm.4", "@babel/helper-validator-identifier": "7.21.4-esm.4", "@babel/helper-split-export-declaration": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.21.4-esm.4_1680621227436_0.6190421821563914", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/helper-module-transforms", "version": "7.21.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "d937c82e9af68d31ab49039136a222b17ac0b420", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.21.5.tgz", "fileCount": 15, "integrity": "sha512-bI2Z9zBGY2q5yMHoBvJ2a9iX3ZOAzJPm7Q8Yz6YeoUjU/Cvhmi2G4QyTNyPBqqXSgTjUxRg3L0xV45HvkNWWBw==", "signatures": [{"sig": "MEQCIDAzo9zZeorJJFamuLMi6Arlo/dGhSbwxHwbTOzRvTK7AiAq+WR+kI3ardW4e71nb0B/alJKo2Tnn1wIFJxfpucIMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCOEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpluhAAjBFC6KvftroYAKKyT+FEOB1RnOZgyBZn2sNx2ph18a3jOH+Y\r\nlrw13HTXbERmv3ILfjgHO2HC0WmPifb6ghSolHFoIVGhcqSNpy86iAblDPV0\r\n6NEtwDqwqhQI8pr1I5xZHMqQT2lb3j6d4iO7MSOy4FKAyzxCz/O4E3prfGwf\r\nRrgwGp1mn0wmnRWLf3/zclCGQRmPvBJjN5bE02dUVEBZ3VXn9pJCwXIwCw58\r\nq9vK8QHhXKlYUK0XXxxrO+cKihNh+N9xh3vzy+fwWjHqhI0E5/CzEat0EXCg\r\nqvLOyxW71k8/rIXTBv7v/vLRRIAxDWCX3riPGFXVoLobB40wgGeU0uXUekw5\r\nRre17EIhenQ+ATSx4MfhjHLXT/yIV6IqXdpmSL/OND4p7+yDd4jxZa5JPti/\r\nbj/3S3dSKeQqTDv7JDpF0CY63RsQNIIez2lgocLF1iAwg2uy7tdsOqK6uPqi\r\noQ3gVINjpGvKaNIzV+mqVL6MX2ztbDK8enPXeOh8fcw2u14MOHZuFD5IdWYZ\r\n8cCE3uv7Wwi6WyLDEYsNAicEzNGmHQPK/3vnlD8m6qNMZje9a8Cw8uWcJnVl\r\nnsRVZOg56UE5Qq9QScyHX5SehdpWLp8yu9lslbuOYsdNFJv2zNJ2w9i+loud\r\nAwwlyRekn/7zDVZD5Kj+OuJp6XjQhRcftrs=\r\n=z7ig\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.21.5", "@babel/template": "^7.20.7", "@babel/traverse": "^7.21.5", "@babel/helper-simple-access": "^7.21.5", "@babel/helper-module-imports": "^7.21.4", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.21.5_1682711428009_0.8112531537125398", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/helper-module-transforms", "version": "7.22.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "a33fdd76bb115b0db085c649b11b4e82219c5a09", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.0.tgz", "fileCount": 16, "integrity": "sha512-drsR5/3eHuYs31uYLIXRK91+THB9+VAd2s3/4TY87Os5qrwr6YesM6GcNX5aEpCF6e9iKK0ZvTBTKqNyntEkvQ==", "signatures": [{"sig": "MEYCIQDOohMJ4ITbtzKvlZKUy640JMuZ2dE4vJgEEEU4zxa5+wIhAO1kelGH5BeHtpt/Xmdr4HERt2oE7LmVG1uN7YxYduwZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148580}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.22.0", "@babel/template": "^7.21.9", "@babel/traverse": "^7.22.0", "@babel/helper-simple-access": "^7.21.5", "@babel/helper-module-imports": "^7.21.4", "@babel/helper-environment-visitor": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.0_1685108748578_0.3702975979092906", "host": "s3://npm-registry-packages"}}, "7.22.1": {"name": "@babel/helper-module-transforms", "version": "7.22.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "e0cad47fedcf3cae83c11021696376e2d5a50c63", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.1.tgz", "fileCount": 15, "integrity": "sha512-dxAe9E7ySDGbQdCVOY/4+UcD8M9ZFqZcZhSPsPacvCG4M+9lwtDDQfI2EoaSvmf7W/8yCBkGU0m7Pvt1ru3UZw==", "signatures": [{"sig": "MEQCIFEUkL3YUP9jHmUpNWMhhgV7TCuWIYKCb63C2uH/gu8pAiA8kefqsiP7bbpIMy8U2DOIUiIt9DFVYAw2Je3hGzNnTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148794}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.22.0", "@babel/template": "^7.21.9", "@babel/traverse": "^7.22.1", "@babel/helper-simple-access": "^7.21.5", "@babel/helper-module-imports": "^7.21.4", "@babel/helper-environment-visitor": "^7.22.1", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-split-export-declaration": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.1_1685118895567_0.6435728497974487", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-module-transforms", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "0f65daa0716961b6e96b164034e737f60a80d2ef", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.5.tgz", "fileCount": 15, "integrity": "sha512-+hGKDt/Ze8GFExiVHno/2dvG5IdstpzCq0y4Qc9OJ25D4q3pKfiIP/4Vp3/JvhDkLKsDK2api3q3fpIgiIF5bw==", "signatures": [{"sig": "MEQCIF4h3SvxsmQG4L2mD53RUYoDxl+MEn+yX95QPs+KVow5AiA51QVyHo9AM5VcrSzv3m7tR0VnQ9Uow0s99idedGMBlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148794}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/types": "^7.22.5", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.5", "@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.5_1686248502495_0.5827991244296946", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/helper-module-transforms", "version": "7.22.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "92dfcb1fbbb2bc62529024f72d942a8c97142129", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.9.tgz", "fileCount": 15, "integrity": "sha512-t+WA2Xn5K+rTeGtC8jCsdAH52bjggG5TKRuRrAGNM/mjIbO4GxvlLMFOEz9wXY5I2XQ60PMFsAG2WIcG82dQMQ==", "signatures": [{"sig": "MEUCIBlOr+x7Hnrp7aaZPbcI4cuMLz4A4EYVAyREB4eDXX3hAiEA1CrTpIxGsOGtdxpqnfqaYunsUthl4gnJnsUEZgzNmP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148476}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.9", "@babel/traverse": "^7.22.8"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.9_1689180812701_0.3532140921843221", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "58f8f594ae464d6171259e57e8ce0c6db7f4e908", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.0.tgz", "fileCount": 15, "integrity": "sha512-3YNVBBVUb0AwRPUuH14gGxaC9ox6/ECGU1i6mwxi/f42oUZ1pNJirnw/cooOzaaPBhIoYB/FBaGpHPK8ecPe3g==", "signatures": [{"sig": "MEUCIQCao1vguCqDaOhb6ax1bY8zpGFIH3MA6PnixD8/GY5nNQIgJEqvMH0tS7IWJJwhNz9wCMReexFBv/UAZ+qzjf/idyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247097}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.0", "@babel/helper-module-imports": "^8.0.0-alpha.0", "@babel/helper-environment-visitor": "^8.0.0-alpha.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.0", "@babel/helper-split-export-declaration": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.0_1689861612102_0.6625583432908653", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "63f5c0eed440c18c3f11ab49c865d01b47f08e03", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.1.tgz", "fileCount": 15, "integrity": "sha512-MGmGg9P4wmXm3rpvpyF1dfb9z9mUXCZvERDailCPYhuvsWMTaesXLX3uqv3Reuxu1im4I/fSptCV0nT++iQ4uw==", "signatures": [{"sig": "MEUCICNaltbPCuQOulROErWxp9OLBSxnjFSjbMULZQDAUmu0AiEAiRiwbovcBsn1Pmft/PkKFJ59g1wGppG+V/1c0d+soGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247097}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.1", "@babel/helper-module-imports": "^8.0.0-alpha.1", "@babel/helper-environment-visitor": "^8.0.0-alpha.1", "@babel/helper-validator-identifier": "^8.0.0-alpha.1", "@babel/helper-split-export-declaration": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.1_1690221149476_0.58255317847392", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "1fc7a4684746634674047f4dac8ed0f2d8990d6f", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.2.tgz", "fileCount": 15, "integrity": "sha512-SBNejgjTuNR/GiheGg7IB2uwGCJkkRXAEGh/6FD4VpjSIiD9rMiurQ0SKgSTYTzdNhD585m2KUMBwjbgS1k0/w==", "signatures": [{"sig": "MEYCIQDQHI0gltNOni7RESrr9nBKF1F2K5OJlC7EsyvO4wV7vwIhAMty9AGfJuBeALao56FybPL/NeFdqg1MShW2XtO4UMbl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247097}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.2", "@babel/helper-module-imports": "^8.0.0-alpha.2", "@babel/helper-environment-visitor": "^8.0.0-alpha.2", "@babel/helper-validator-identifier": "^8.0.0-alpha.2", "@babel/helper-split-export-declaration": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.2_1691594110289_0.06512099995781062", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/helper-module-transforms", "version": "7.22.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "40ad2f6950f143900e9c1c72363c0b431a606082", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.15.tgz", "fileCount": 15, "integrity": "sha512-l1UiX4UyHSFsYt17iQ3Se5pQQZZHa22zyIXURmvkmLCD4t/aU+dvNWHatKac/D9Vm9UES7nvIqHs4jZqKviUmQ==", "signatures": [{"sig": "MEUCIG1VXaJm+zkoYFKKqek/jY4a4/tNQoKCqOJjQjdnTeAzAiEAhr0XVlcK8fiDPm3UGaI5Ht2QWCTZlM/M6khD0l6ga5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148539}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.15", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.15_1693830319840_0.6034137481278765", "host": "s3://npm-registry-packages"}}, "7.22.17": {"name": "@babel/helper-module-transforms", "version": "7.22.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "7edf129097a51ccc12443adbc6320e90eab76693", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.17.tgz", "fileCount": 15, "integrity": "sha512-XouDDhQESrLHTpnBtCKExJdyY4gJCdrvH2Pyv8r8kovX2U8G0dRUOT45T9XlbLtuu9CLXP15eusnkprhoPV5iQ==", "signatures": [{"sig": "MEYCIQC4ouJQQIif3mGw+iHLUX39D/+dD0OBtWeophs3szvRmAIhANzs80fDJCXyK7y4NNFxlXNmJ60JQDE+kD5qaDIupF6L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148629}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.15", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.17", "@babel/traverse": "^7.22.17"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.17_1694181209037_0.9701981644613316", "host": "s3://npm-registry-packages"}}, "7.22.18": {"name": "@babel/helper-module-transforms", "version": "7.22.18", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.18", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "fddeafc0ffd7f188aa6f5ec286860b8414e87640", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.18.tgz", "fileCount": 15, "integrity": "sha512-82k8pC614ge5BCWOShgzpmlK7OszfYdI8yjehn1OfweDxHY8tVFm9CTl3EHy5PaTpLHATJvwGzmwBBrOPXLHcw==", "signatures": [{"sig": "MEUCIQD9pTkvn8V7v379yNcg6X3BGCTotRm5v+1so7Cg4qtq4QIgDMVtRjpLUOJx0tBMvjK2H8Diiyr2QM0cdT7ZcC5TqGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148865}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.18", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.18", "@babel/traverse": "^7.22.18"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.18_1694707145854_0.5733834925363825", "host": "s3://npm-registry-packages"}}, "7.22.19": {"name": "@babel/helper-module-transforms", "version": "7.22.19", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.19", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "94b1f281caa6518f02ec0f5ea2b5348e298ce266", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.19.tgz", "fileCount": 15, "integrity": "sha512-m6h1cJvn+OJ+R3jOHp30faq5xKJ7VbjwDj5RGgHuRlU9hrMeKsGC+JpihkR5w1g7IfseCPPtZ0r7/hB4UKaYlA==", "signatures": [{"sig": "MEQCIAG1ublwzNC6308UsDw/98aMcLe3AYFsd4KNsz+uolmQAiBQYegGfrnLOIIstymaw2ieXIFLJftCNeDHfJNaImy1AQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148865}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.19", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.19", "@babel/traverse": "^7.22.19"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.19_1694709125019_0.6374966019057831", "host": "s3://npm-registry-packages"}}, "7.22.20": {"name": "@babel/helper-module-transforms", "version": "7.22.20", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.22.20", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "da9edc14794babbe7386df438f3768067132f59e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.22.20.tgz", "fileCount": 15, "integrity": "sha512-dLT7JVWIUUxKOs1UnJUBR3S70YK+pKX6AbJgB2vMIvEkZkrfJDbYDJesnPshtKV4LhDOR3Oc5YULeDizRek+5A==", "signatures": [{"sig": "MEUCIQDNt3KNsXpKbFw3YPXNLO9V1/MnjH7nmF5N45zmcXUv7gIgQaFfhmM3FXVinprhBB19rJ5Kh654prAmwVOHBKD9OrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148656}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.20", "@babel/traverse": "^7.22.20"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.22.20_1694881722840_0.26763624967955124", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/helper-module-transforms", "version": "7.23.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "3ec246457f6c842c0aee62a01f60739906f7047e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.23.0.tgz", "fileCount": 17, "integrity": "sha512-WhDWw1tdrlT0gMgUJSlX0IQvoO1eN279zrAUbVB+KpV2c3Tylz8+GnKOLllCS6Z/iZQEyVYxhZVUdPTqs2YYPw==", "signatures": [{"sig": "MEUCICN34LNfAWcjB+MLdbQ3MpZzpYJ5qJBWPEJEJuKXhY3rAiEA1sR0oTWtyRyEJ1fgzu6kuVHqI1KiQu2c25NCLcwrcoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157496}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.0", "@babel/traverse": "^7.23.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.23.0_1695629431575_0.9390291641794664", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "29850263652552f0bdd50f70090f474f715df808", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-a5cZnN7C4aQHFvH5ycH613Bt061vDGpwcV/EoE/xNJqa2WVevqbUgNMIy0ZUhTvc1Ruyir9OvpCDPwB58xenOQ==", "signatures": [{"sig": "MEYCIQCwIftpQlRLSr9b8cOyUFDpEkGoiDxwkaaPE7K8cz0qswIhAOXdKo4KGqJJo8F0p0Sp3lokYQaOSFk59DeLHyCgkYBc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154018}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.3", "@babel/helper-module-imports": "^8.0.0-alpha.3", "@babel/helper-environment-visitor": "^8.0.0-alpha.3", "@babel/helper-validator-identifier": "^8.0.0-alpha.3", "@babel/helper-split-export-declaration": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.3_1695740237638_0.2854997207846959", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "86531a09917346c9088365f38c36b526d793881e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-9lC9s2C8gY5GlFGMAFPzET5clRPyGxDHbauouS05KFNKhiDFbsAzCQ1C6pBPrBh/CunDAL5yDBh9WNiCPK34Pw==", "signatures": [{"sig": "MEUCIQD+ukfuOSk3nwgXVgyr8jiig6/yPW1Bsp8fGABxvkCsuQIgXicQzQzuDbqlvVKBgV5O+oFc96ZTPr4SQML37miesKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154018}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.4", "@babel/helper-module-imports": "^8.0.0-alpha.4", "@babel/helper-environment-visitor": "^8.0.0-alpha.4", "@babel/helper-validator-identifier": "^8.0.0-alpha.4", "@babel/helper-split-export-declaration": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.4_1697076394669_0.75053578809734", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/helper-module-transforms", "version": "7.23.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "d7d12c3c5d30af5b3c0fcab2a6d5217773e2d0f1", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.23.3.tgz", "fileCount": 17, "integrity": "sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==", "signatures": [{"sig": "MEQCIH82Ol479Gpo+kli1xp8nKTzNLbhQnxEggWkGiqTiI+WAiBuHlOJz/TSYIJKbQzDApBbte/5W2//FBYxpqvJgmmdBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158061}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-split-export-declaration": "^7.22.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3", "@babel/traverse": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.23.3_1699513415870_0.0029019092536062274", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "6c4be084333a643a2740bc48243cf81a5da026a1", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-ceBgK3UyiYtEqerXhWUpwm4dVjwQHcEbyOAcF9BjYXlWnxjDVjIQ4/hhIn8L3+B9pTBjrIHgYez/sRUfL1feTA==", "signatures": [{"sig": "MEYCIQDWMZpcGsmMhgoVubb27q91buq/9YcrewF3EGqEl4HFBgIhAPLIxKSmSr8sYLcWFJ0bOsYBZs2z6Xwlcyr5st9m3xi3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154608}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.5", "@babel/helper-module-imports": "^8.0.0-alpha.5", "@babel/helper-environment-visitor": "^8.0.0-alpha.5", "@babel/helper-validator-identifier": "^8.0.0-alpha.5", "@babel/helper-split-export-declaration": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.5_1702307962706_0.29599671032738684", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "4e628a62e394510e4ff2b40a8e7d9ae2ff784b59", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-NRG3LB9AXng09w+6ix1BV+45yoKdqBO64fpqpzUYk7rCzvpyxMSVvnJfdxmJnxgobd5/bkMS4UB7ejEj3rWm3g==", "signatures": [{"sig": "MEQCIDex+H7Ti+G3udkopNPxYSpDNVlaTErOnvIdwX5RSTj+AiBUx4C+O9iZweiLt8OlYQCuSqDZRXSH3Vja5W+pHJupbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154608}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.6", "@babel/helper-module-imports": "^8.0.0-alpha.6", "@babel/helper-environment-visitor": "^8.0.0-alpha.6", "@babel/helper-validator-identifier": "^8.0.0-alpha.6", "@babel/helper-split-export-declaration": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.6_1706285661968_0.942550933158097", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "2523f04148be51944563f98d1b12a7239cc81e2f", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-8msf2LeBE9qOnYCPF2+azJ9NI5HhWlu3Xmu90ogMuxaj7oeZAOF+teAB2MqJV+r9YXINjL7pUmRXBFXZ0QCOVw==", "signatures": [{"sig": "MEYCIQC8AwJ7KOVG3IG3I9l9vwfEImlheqUM4isiEdT/4SXagQIhAOjSdoQETCXTJQ9AXP7XrtJtSlKRrtvFQkYUF7cxIWWd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154608}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.7", "@babel/helper-module-imports": "^8.0.0-alpha.7", "@babel/helper-environment-visitor": "^8.0.0-alpha.7", "@babel/helper-validator-identifier": "^8.0.0-alpha.7", "@babel/helper-split-export-declaration": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.7_1709129118326_0.5618601799944107", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "5ff7ac464e3e7df9dab7703291dcf6ffc43f9d01", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-U1CEpL7yUxTgJ58crSUM7MfgH9VJdszUOezg26xLwgJSRqE6B4cvX95FWgQAmo/Uylh6vvOwUAr9slEI4c9AOw==", "signatures": [{"sig": "MEUCIBiPnJljHEUe1ElLpFYh/CZEDcW/oi3DVnZXmdEfToe1AiEA0q97uLk4SaU0MDJn9xhmZbO8DnjDu+8S4FVaKQb3FLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 154608}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.8", "@babel/helper-module-imports": "^8.0.0-alpha.8", "@babel/helper-environment-visitor": "^8.0.0-alpha.8", "@babel/helper-validator-identifier": "^8.0.0-alpha.8", "@babel/helper-split-export-declaration": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.8_1712236804503_0.21576630872890923", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/helper-module-transforms", "version": "7.24.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "ea6c5e33f7b262a0ae762fd5986355c45f54a545", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.24.5.tgz", "fileCount": 17, "integrity": "sha512-9GxeY8c2d2mdQUP1Dye0ks3VDyIMS98kt/llQ2nUId8IsWqTF0l1LkSX0/uP7l7MCDrzXS009Hyhe2gzTiGW8A==", "signatures": [{"sig": "MEUCIHPFnLIaWlvxaiDHKdR6F0oSYkD0W2vgKz3qzftrNd+uAiEAlEDYhNUUVXs8bfg3tsb/wDMjzebKb2jtrK/0gKYP+R0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158059}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.24.5", "@babel/helper-module-imports": "^7.24.3", "@babel/helper-environment-visitor": "^7.22.20", "@babel/helper-validator-identifier": "^7.24.5", "@babel/helper-split-export-declaration": "^7.24.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.5", "@babel/traverse": "^7.24.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.24.5_1714415665244_0.6245522070072025", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-module-transforms", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "22346ed9df44ce84dee850d7433c5b73fab1fe4e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.24.6.tgz", "fileCount": 17, "integrity": "sha512-Y/YMPm83mV2HJTbX1Qh2sjgjqcacvOlhbzdCCsSlblOKjSYmQqEbO6rUniWQyRo9ncyfjT8hnUjlG06RXDEmcA==", "signatures": [{"sig": "MEQCICQuzNu4JiA4e6pbV954WKJmsJGGjrNHHoyUBnVDgKL2AiBYd82oxUMA+QVIqMb0j5XF5ekaWbVZS/Mm+aIZi9piPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157977}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.24.6", "@babel/helper-module-imports": "^7.24.6", "@babel/helper-environment-visitor": "^7.24.6", "@babel/helper-validator-identifier": "^7.24.6", "@babel/helper-split-export-declaration": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.24.6_1716553492410_0.4909482518343964", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "3baba457861bea1af88f41ab2c21b5df1f98cf68", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-61k/hzrd4elE2comN1/zdWc9Q5ZSbLTSscGVEyKCWzbvnc4CyW66BB9Vuum/Hp5TgcZFus/prrEVpLBjAg8m8g==", "signatures": [{"sig": "MEYCIQCpVf0+UuoMQ+RAxMvHDE2crUroq88zrySwzjqTcIn3+wIhAO/dzCG+mt+sL1f2F5sCaSkit02CcHPhhonN+p5sg9DL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161547}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.9", "@babel/helper-module-imports": "^8.0.0-alpha.9", "@babel/helper-environment-visitor": "^8.0.0-alpha.9", "@babel/helper-validator-identifier": "^8.0.0-alpha.9", "@babel/helper-split-export-declaration": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.9_1717423520286_0.44602792145911585", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "486289576d0270dff4c36e1b3a22746f6a5207b3", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-d/XK3VVtNxL58hQlWdOsahYako/y6AZMUlFrT6/pg5MWnucPiy86MryASIelZxPwkb+2eJ39qqSVc6Qoyw0VuQ==", "signatures": [{"sig": "MEUCIFoRW2JVmsyrs5En+Y+zOCcWlzA/eZwE6VA9fdcZnZLAAiEAspV0Jery0QNau4j7B/S3Mm4nU3F4VWBMNmUGoHpFnfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161555}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.10", "@babel/helper-module-imports": "^8.0.0-alpha.10", "@babel/helper-environment-visitor": "^8.0.0-alpha.10", "@babel/helper-validator-identifier": "^8.0.0-alpha.10", "@babel/helper-split-export-declaration": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.10_1717500034336_0.32579514553926625", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-module-transforms", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "31b6c9a2930679498db65b685b1698bfd6c7daf8", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.24.7.tgz", "fileCount": 17, "integrity": "sha512-1fuJEwIrp+97rM4RWdO+qrRsZlAeL1lQJoPqtCYWv0NL115XM93hIH4CSRln2w52SqvmY5hqdtauB6QFCDiZNQ==", "signatures": [{"sig": "MEUCIGcrESN1+vtuZhC0HVTCbsDOXVpaedPmJWrYD1/v6IHDAiEA73/mW+HdYobO0SBXQxDUkHs7WH1bmRjCTx3n8RxY0aU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157931}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.24.7_1717593348575_0.3346499924257369", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "b6d60217c5504b0f66fa501a3198438e0b6478f3", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-74gGGcH3YWL29jpdRHOAxkJ2+n9+IdR0Y24IqBTmi1Aaci9U7xbv1fsoPNeIpciPrrOsJLWO0hA5WEC2VQKwyQ==", "signatures": [{"sig": "MEQCID2bmIvjEmVpA3MQy543RkWwfEtv+xXwiC1rxTqnUmLoAiAWxOcm+tlV52L8IGG4UhLlCtaFA5jrhb841Clrzbg/Pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161555}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^8.0.0-alpha.11", "@babel/helper-module-imports": "^8.0.0-alpha.11", "@babel/helper-environment-visitor": "^8.0.0-alpha.11", "@babel/helper-validator-identifier": "^8.0.0-alpha.11", "@babel/helper-split-export-declaration": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.11_1717751759066_0.5131984051640641", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/helper-module-transforms", "version": "7.24.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "b1f2df4f96f3465b0d035b697ec86cb51ff348fe", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.24.8.tgz", "fileCount": 17, "integrity": "sha512-m4vWKVqvkVAWLXfHCCfff2luJj86U+J0/x+0N3ArG/tP0Fq7zky2dYwMbtPmkc/oulkkbjdL3uWzuoBwQ8R00Q==", "signatures": [{"sig": "MEUCICvPI8Coy87UNbxmJXMdSmJfXcXhyEkPEIQlp5YD+YB1AiEAkR6gBKpWdTAstQmEbCxK/oqTuPI8jhjh/oUYuSxVvDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157912}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.8"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.24.8_1720709682482_0.007079145295766365", "host": "s3://npm-registry-packages"}}, "7.24.9": {"name": "@babel/helper-module-transforms", "version": "7.24.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.24.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "e13d26306b89eea569180868e652e7f514de9d29", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.24.9.tgz", "fileCount": 17, "integrity": "sha512-oYbh+rtFKj/HwBQkFlUzvcybzklmVdVV3UU+mN7n2t/q3yGHbuVdNxyFvSBO1tfvjyArpHNcWMAzsSPdyI46hw==", "signatures": [{"sig": "MEUCIQDE+Hllz3FyFzmijKTPwdQXl+y3b5boU5+mlIocYoaFwAIgEzxLsCTY1KVL9IUaLhZ3b6NJT59MKZg+YMG06QhCwmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159712}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-environment-visitor": "^7.24.7", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-split-export-declaration": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.24.9_1721039668979_0.28558895426864184", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/helper-module-transforms", "version": "7.25.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "3ffc23c473a2769a7e40d3274495bd559fdd2ecc", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.25.0.tgz", "fileCount": 17, "integrity": "sha512-bIkOa2ZJYn7FHnepzr5iX9Kmz8FjIz4UKzJ9zhX3dnYuVW0xul9RuR3skBfoLu+FPTQw90EHW9rJsSZhyLQ3fQ==", "signatures": [{"sig": "MEYCIQDzXovaLuY9Rs9TD3OGjUypPuzhXWqTJa2fQnQfCoN+XQIhAIMYb++3bqTDWEcU7lqpVqz7ZxiqN0u+IphJXzqs53hs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159837}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.0", "@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.25.0_1722013169168_0.019769323948992", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "e3ccef0bf85a93cc45e82dc63a1e38b4f41ff63f", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-ygoIMV9cOMAb8xioOpc0UeCf+Gx354uiHIcTxUKQbl88QOsUp0sIoJiQth09JEucYoJwk+ItUrMVxXZW0IOPZw==", "signatures": [{"sig": "MEUCIQCBiFTOdJMMnu/0aLRtclbUNNWbrO/W815hHGrWsa6XCQIgIqHo+9bpDaGFgVBcBDPl3eqbWf5cyZzX1DpvAwZE1K8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162695}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.12", "@babel/helper-simple-access": "^8.0.0-alpha.12", "@babel/helper-module-imports": "^8.0.0-alpha.12", "@babel/helper-validator-identifier": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.12_1722015233308_0.49854892227884173", "host": "s3://npm-registry-packages"}}, "7.25.2": {"name": "@babel/helper-module-transforms", "version": "7.25.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.25.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "ee713c29768100f2776edf04d4eb23b8d27a66e6", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.25.2.tgz", "fileCount": 17, "integrity": "sha512-BjyRAbix6j/wv83ftcVJmBt72QtHI56C7JXZoG2xATiLpmoC7dpd8WnkikExHDVPpi/3qCmO6WY1EaXOluiecQ==", "signatures": [{"sig": "MEQCIAlI+Ko3FKl4CnoPdUwcDVh8gK/HrZQLIoKeZg54CG0QAiAfZX2aAGcNkbDNlfXypLabBvS0tbhfdAbTGAY6xm1+eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159831}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.2", "@babel/helper-simple-access": "^7.24.7", "@babel/helper-module-imports": "^7.24.7", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.2"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.25.2_1722308093293_0.34604769256563817", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-module-transforms", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "2ac9372c5e001b19bc62f1fe7d96a18cb0901d1a", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.25.7.tgz", "fileCount": 19, "integrity": "sha512-k/6f8dKG3yDz/qCwSM+RKovjMix563SLxQFo0UhRNo239SP6n9u5/eLtKD6EAjwta2JHJ49CsD8pms2HdNiMMQ==", "signatures": [{"sig": "MEQCIBJOr5Rfc3c/LUdmT27bfoNKfzyt3+vAj+zt5jQ3DpdPAiAmI5HYHOZGd7mQXhPo03lLESHEDGTjy5sMVol7VecE9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 232797}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.7", "@babel/helper-simple-access": "^7.25.7", "@babel/helper-module-imports": "^7.25.7", "@babel/helper-validator-identifier": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.25.7_1727882121908_0.9159380184450217", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-module-transforms", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "12e4fb2969197ef6d78ea8a2f24375ce85b425fb", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.25.9.tgz", "fileCount": 17, "integrity": "sha512-TvLZY/F3+GvdRYFZFyxMvnsKi+4oJdgZzU3BoGN9Uc2d9C6zfNwJcKKhjqLAhK8i46mv93jsO74fDh3ih6rpHA==", "signatures": [{"sig": "MEQCIAi8jiLUmai3mnAI5aL3VqYg8bG1UrNVitW1hI0KOOH2AiBsUWlrFfpxtkLL/h0X4eyIJ1yVs2pLQgjjMP+7cw6nJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159965}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.9", "@babel/helper-simple-access": "^7.25.9", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.25.9_1729610497715_0.9023514908241166", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/helper-module-transforms", "version": "7.26.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "8ce54ec9d592695e58d84cd884b7b5c6a2fdeeae", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz", "fileCount": 17, "integrity": "sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==", "signatures": [{"sig": "MEUCIQCXEOsDNHM5bMLZoaoRlp1UsS3lQIrFthts28CvfXgQpAIgKk3c4q/30Ah2ttZQp7EZwEi6pv4N7KsJvZufRcUT2J4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160842}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^7.25.9", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.26.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.26.0_1729863006230_0.40522908267687074", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "9ba7dcf6ee9220072af52a35f2b9eabc708a4906", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-lhIixtM2TF7UXZ3zVSZ4c2XzfkzoMyYykEGdlt0ATeOSaAsTpHHOshdlSFTa7Z2JqF3jpnUhL1mzt6DySXmrqQ==", "signatures": [{"sig": "MEYCIQD9NtqpWdrDHyN6NdeMJrLqTP0v4gVuSnUUcupAr9cBmAIhAPUvNfFt2h/AZQx8fBwYHx0ThEq+pvQA+krC7npE9tVg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163885}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.13", "@babel/helper-module-imports": "^8.0.0-alpha.13", "@babel/helper-validator-identifier": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.13_1729864478263_0.3653827163942036", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "b38722145eeedf848ab848eaba743374f267456f", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-LOeJJ0mhRdy+ZEtRkgvMrOOu4/Tart5PNku4HoUYrj5C2zQPWNUxLpT2ZyW6Gqa11kFKBGOahliND0kh6mApnA==", "signatures": [{"sig": "MEUCIFMwz5JVsncx8e/uI9dKFeLx46DbyVqf63Ywbk0USEnvAiEAwDbgueWXCyDAmnRsfoZFvkDiXREL6+Pop3bbKalNLiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163885}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.14", "@babel/helper-module-imports": "^8.0.0-alpha.14", "@babel/helper-validator-identifier": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.14_1733504067717_0.9691368662337019", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "45fd2f3dde411f9317ade2083c54fe0dc6f2f5be", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-WBjq3WwxjrnyZpwO2CvWUQ8e3FHVTazAHoA7dk3o7sw1v1C95xT8Y9PNFFMDpAynenU7BdgAYC9RxvpfbGVGNw==", "signatures": [{"sig": "MEUCIF3ikC5aTt5WpdN0HkXddadTC679DAav5KyGRFehQm1VAiEAjhXM39ulFQEpU9gCwCRSw+unkj9+72c0YsZiagp6slc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163885}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.15", "@babel/helper-module-imports": "^8.0.0-alpha.15", "@babel/helper-validator-identifier": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.15_1736529895807_0.13184252177730849", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "a89e1257e6cc87ac20e65a98ea3254e286b85c4e", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-0PARmYzXyjYd9Ba4e5uAbEI/l2SexextVTwEOwC7fYiM2R4ZH+MMn94C+GQqQuXD3hcR+Ac9+mah+QTFBRbu7g==", "signatures": [{"sig": "MEUCIQC6NoKQ9Xo80fSxae1XI17avTRsq+cx+wzgM1opmJBSdQIgGs35iW9TWp0lfd4XDjhLYD08TxLctudMOsVPZ9CXTZg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 163885}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.16", "@babel/helper-module-imports": "^8.0.0-alpha.16", "@babel/helper-validator-identifier": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.16_1739534371900_0.41566406388019494", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-module-transforms", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "07a360f11b8528b712268cc877240094b838ce04", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-9IQekd2nuBDD5Nzdj11QFN4Vyo9iHgPi7oJ0WIuCO4iTtkVw7O231IIJKtTfe6OZDq+wKspXQeccZowYhFm7Xg==", "signatures": [{"sig": "MEUCIEl+iLQCUhyqzKW6RiJ1IbUaxlECospy6rUkILc1yCgWAiEAuGSYBIHd9C1GZWmW1kAVtXX+IzZUAq6kQGBHP9EnlAU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 163885}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-alpha.17", "@babel/helper-module-imports": "^8.0.0-alpha.17", "@babel/helper-validator-identifier": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-alpha.17_1741717525072_0.1941308791141294", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-module-transforms", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "e1663b8b71d2de948da5c4fb2a20ca4f3ec27a6f", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.1.tgz", "fileCount": 17, "integrity": "sha512-9yHn519/8KvTU5BjTVEEeIM3w9/2yXNKoD82JifINImhpKkARMJKPP59kLo+BafpdN5zgNeIcS4jsGDmd3l58g==", "signatures": [{"sig": "MEUCIQDirJ3WI/H1GJjylao772atJznCVyNHKxeYORyfgC/VdAIgApKosUZoVRRixCM9Elhoo4z/faqmQIfa7/M4DQJhRZI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 160852}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.27.1_1746025760630_0.3282476462823478", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.3": {"name": "@babel/helper-module-transforms", "version": "7.27.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@7.27.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "db0bbcfba5802f9ef7870705a7ef8788508ede02", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "fileCount": 17, "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "signatures": [{"sig": "MEQCIB7ihRE/LOE0koGg0FZ1sXGzxasBaeplh29W0Y0E0aqDAiBiVHop5TiglnPlJev6W7LF5azOFUbCsk/OK8UwLx5muw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 161825}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^7.27.3", "@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.3"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_7.27.3_1748335166078_0.23821326945570287", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-module-transforms", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-transforms@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "dist": {"shasum": "4901b0581e09d471570620f11cdb31663864e757", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-dHct7TMKjW5re1r1Ot0SEy7fNila0lBV71HibFViXHtcN0uM9KN4P3i0KXvbCsp+NlXZDEiZf58tcBu6fc6ijw==", "signatures": [{"sig": "MEYCIQDTghOIP+yXDgtghanHDVYiUmHV95Lpyj6YdXv9ffxwSQIhAIXY/VN0GiClZXEMF2ghPldPVIffpTJfoLIuSSVUD1s8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 164556}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "directories": {}, "dependencies": {"@babel/traverse": "^8.0.0-beta.0", "@babel/helper-module-imports": "^8.0.0-beta.0", "@babel/helper-validator-identifier": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-transforms_8.0.0-beta.0_1748620296796_0.048892714054477926", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-module-transforms", "version": "8.0.0-beta.1", "description": "Babel helper functions for implementing ES6 module transformations", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-transforms"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-module-imports": "^8.0.0-beta.1", "@babel/helper-validator-identifier": "^8.0.0-beta.1", "@babel/traverse": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-module-transforms@8.0.0-beta.1", "dist": {"shasum": "ac6f53b25805d92ec649896dbbbab81714a5d4ea", "integrity": "sha512-w2io48X5RpEOCnKVF5MjkbHTH6plnYBmk7KdZS4aXskrgY8Ke/N7c6VjOmdqeMUHOfKS/3+gStU6XLWRmDlc4A==", "tarball": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 164556, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGfVVw6I82yMroHYqddlOOM6v0M+ZPcDnloZrc76dUzAAiBOV0UukPrEI6PPUT3iMS7KDJMu5w9wgAOaqwAXJgByyA=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-module-transforms_8.0.0-beta.1_1751447080139_0.13131272069814837"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:51.655Z", "modified": "2025-07-02T09:04:40.546Z", "7.0.0-beta.4": "2017-10-30T18:35:51.655Z", "7.0.0-beta.5": "2017-10-30T20:57:30.222Z", "7.0.0-beta.31": "2017-11-03T20:04:12.912Z", "7.0.0-beta.32": "2017-11-12T13:33:54.688Z", "7.0.0-beta.33": "2017-12-01T14:29:11.670Z", "7.0.0-beta.34": "2017-12-02T14:40:08.631Z", "7.0.0-beta.35": "2017-12-14T21:48:21.486Z", "7.0.0-beta.36": "2017-12-25T19:05:31.147Z", "7.0.0-beta.37": "2018-01-08T16:03:36.747Z", "7.0.0-beta.38": "2018-01-17T16:32:34.497Z", "7.0.0-beta.39": "2018-01-30T20:28:40.617Z", "7.0.0-beta.40": "2018-02-12T16:42:35.579Z", "7.0.0-beta.41": "2018-03-14T16:26:44.319Z", "7.0.0-beta.42": "2018-03-15T20:52:01.425Z", "7.0.0-beta.43": "2018-04-02T16:48:52.498Z", "7.0.0-beta.44": "2018-04-02T22:20:31.517Z", "7.0.0-beta.45": "2018-04-23T01:58:14.953Z", "7.0.0-beta.46": "2018-04-23T04:32:33.130Z", "7.0.0-beta.47": "2018-05-15T00:17:34.711Z", "7.0.0-beta.48": "2018-05-24T19:24:29.886Z", "7.0.0-beta.49": "2018-05-25T16:04:08.446Z", "7.0.0-beta.50": "2018-06-12T19:47:56.377Z", "7.0.0-beta.51": "2018-06-12T21:20:35.878Z", "7.0.0-beta.52": "2018-07-06T00:59:44.023Z", "7.0.0-beta.53": "2018-07-11T13:40:43.405Z", "7.0.0-beta.54": "2018-07-16T18:00:25.108Z", "7.0.0-beta.55": "2018-07-28T22:07:50.239Z", "7.0.0-beta.56": "2018-08-04T01:08:13.784Z", "7.0.0-rc.0": "2018-08-09T15:59:47.093Z", "7.0.0-rc.1": "2018-08-09T20:09:39.562Z", "7.0.0-rc.2": "2018-08-21T19:25:44.706Z", "7.0.0-rc.3": "2018-08-24T18:09:29.188Z", "7.0.0-rc.4": "2018-08-27T16:46:11.141Z", "7.0.0": "2018-08-27T21:44:40.483Z", "7.1.0": "2018-09-17T19:30:47.205Z", "7.2.2": "2018-12-15T10:05:43.951Z", "7.4.3": "2019-04-02T19:55:33.564Z", "7.4.4": "2019-04-26T21:04:42.874Z", "7.5.5": "2019-07-17T21:21:41.029Z", "7.7.0": "2019-11-05T10:53:48.223Z", "7.7.4": "2019-11-22T23:33:42.201Z", "7.7.5": "2019-12-06T13:17:49.480Z", "7.8.0": "2020-01-12T00:17:23.731Z", "7.8.3": "2020-01-13T21:42:18.566Z", "7.8.6": "2020-02-27T12:21:45.910Z", "7.9.0": "2020-03-20T15:39:40.216Z", "7.10.1": "2020-05-27T22:08:22.348Z", "7.10.4": "2020-06-30T13:13:23.296Z", "7.10.5": "2020-07-14T18:18:04.484Z", "7.11.0": "2020-07-30T21:27:13.664Z", "7.12.0": "2020-10-14T20:03:28.315Z", "7.12.1": "2020-10-15T22:41:54.908Z", "7.12.13": "2021-02-03T01:11:57.665Z", "7.12.17": "2021-02-18T15:13:37.925Z", "7.13.0": "2021-02-22T22:50:18.711Z", "7.13.12": "2021-03-22T15:47:26.201Z", "7.13.14": "2021-03-29T14:15:42.722Z", "7.14.0": "2021-04-29T20:10:15.675Z", "7.14.2": "2021-05-12T17:09:39.758Z", "7.14.5": "2021-06-09T23:13:09.770Z", "7.14.8": "2021-07-20T18:02:51.438Z", "7.15.0": "2021-08-04T21:13:12.463Z", "7.15.4": "2021-09-02T21:39:51.960Z", "7.15.7": "2021-09-17T23:06:18.801Z", "7.15.8": "2021-10-06T20:54:55.156Z", "7.16.0": "2021-10-29T23:47:55.479Z", "7.16.5": "2021-12-13T22:28:39.440Z", "7.16.7": "2021-12-31T00:22:57.046Z", "7.17.6": "2022-02-21T22:58:23.053Z", "7.17.7": "2022-03-14T17:07:12.695Z", "7.17.12": "2022-05-16T19:33:14.491Z", "7.18.0": "2022-05-19T18:16:39.752Z", "7.18.6": "2022-06-27T19:50:33.679Z", "7.18.8": "2022-07-08T09:32:38.873Z", "7.18.9": "2022-07-18T09:17:40.380Z", "7.19.0": "2022-09-05T19:02:21.240Z", "7.19.6": "2022-10-20T09:03:34.883Z", "7.20.2": "2022-11-04T18:51:09.995Z", "7.20.7": "2022-12-22T09:45:36.394Z", "7.20.11": "2022-12-23T21:08:05.907Z", "7.21.0": "2023-02-20T15:31:17.129Z", "7.21.2": "2023-02-23T09:31:38.459Z", "7.21.4-esm": "2023-04-04T14:09:54.401Z", "7.21.4-esm.1": "2023-04-04T14:21:52.019Z", "7.21.4-esm.2": "2023-04-04T14:39:55.880Z", "7.21.4-esm.3": "2023-04-04T14:56:39.278Z", "7.21.4-esm.4": "2023-04-04T15:13:47.625Z", "7.21.5": "2023-04-28T19:50:28.201Z", "7.22.0": "2023-05-26T13:45:48.805Z", "7.22.1": "2023-05-26T16:34:55.893Z", "7.22.5": "2023-06-08T18:21:42.659Z", "7.22.9": "2023-07-12T16:53:32.968Z", "8.0.0-alpha.0": "2023-07-20T14:00:12.270Z", "8.0.0-alpha.1": "2023-07-24T17:52:29.673Z", "8.0.0-alpha.2": "2023-08-09T15:15:10.489Z", "7.22.15": "2023-09-04T12:25:20.073Z", "7.22.17": "2023-09-08T13:53:29.312Z", "7.22.18": "2023-09-14T15:59:06.067Z", "7.22.19": "2023-09-14T16:32:05.215Z", "7.22.20": "2023-09-16T16:28:43.132Z", "7.23.0": "2023-09-25T08:10:31.814Z", "8.0.0-alpha.3": "2023-09-26T14:57:17.873Z", "8.0.0-alpha.4": "2023-10-12T02:06:34.923Z", "7.23.3": "2023-11-09T07:03:36.113Z", "8.0.0-alpha.5": "2023-12-11T15:19:22.867Z", "8.0.0-alpha.6": "2024-01-26T16:14:22.166Z", "8.0.0-alpha.7": "2024-02-28T14:05:18.549Z", "8.0.0-alpha.8": "2024-04-04T13:20:04.684Z", "7.24.5": "2024-04-29T18:34:25.387Z", "7.24.6": "2024-05-24T12:24:52.558Z", "8.0.0-alpha.9": "2024-06-03T14:05:20.492Z", "8.0.0-alpha.10": "2024-06-04T11:20:34.633Z", "7.24.7": "2024-06-05T13:15:48.753Z", "8.0.0-alpha.11": "2024-06-07T09:15:59.239Z", "7.24.8": "2024-07-11T14:54:42.622Z", "7.24.9": "2024-07-15T10:34:29.122Z", "7.25.0": "2024-07-26T16:59:29.337Z", "8.0.0-alpha.12": "2024-07-26T17:33:53.450Z", "7.25.2": "2024-07-30T02:54:53.477Z", "7.25.7": "2024-10-02T15:15:22.086Z", "7.25.9": "2024-10-22T15:21:38.025Z", "7.26.0": "2024-10-25T13:30:06.520Z", "8.0.0-alpha.13": "2024-10-25T13:54:38.575Z", "8.0.0-alpha.14": "2024-12-06T16:54:27.929Z", "8.0.0-alpha.15": "2025-01-10T17:24:56.058Z", "8.0.0-alpha.16": "2025-02-14T11:59:32.099Z", "8.0.0-alpha.17": "2025-03-11T18:25:25.279Z", "7.27.1": "2025-04-30T15:09:20.853Z", "7.27.3": "2025-05-27T08:39:26.325Z", "8.0.0-beta.0": "2025-05-30T15:51:36.992Z", "8.0.0-beta.1": "2025-07-02T09:04:40.304Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-transforms"}, "description": "Babel helper functions for implementing ES6 module transformations", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}