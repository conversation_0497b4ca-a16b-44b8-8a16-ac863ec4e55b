{"_id": "onetime", "_rev": "28-a0f4deea58facf570ef0a9ab22f30f99", "name": "onetime", "description": "Ensure a function is only called once", "dist-tags": {"latest": "7.0.0"}, "versions": {"0.1.0": {"name": "onetime", "version": "0.1.0", "description": "Only call a function once. Unlike the module `once`, this one isn't naughty extending `Function.prototype`.", "keywords": ["once", "one", "single", "call", "function", "prevent"], "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["onetime.js"], "main": "onetime.js", "repository": {"type": "git", "url": "git://github.com/sindresorhus/onetime"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "~1.x"}, "engines": {"node": ">=0.8.0"}, "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime", "_id": "onetime@0.1.0", "dist": {"shasum": "971beb7a622f1396b9cf4ef7277262f2b01c8565", "tarball": "https://registry.npmjs.org/onetime/-/onetime-0.1.0.tgz", "integrity": "sha512-ooCyps9+028xrQdZS6a/ClweDcU7Jnh0UbLWWxpBpQwfZSbly6A1TtvNrm3VWqq7ADeA4rC8nCrtxcp5Om6nrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhlF94zETR7MrZCWOsz8OPGmy5TaoRbIjQybvdCmT9+AIgLas8VlD8bFYqr3FhUze27fjx7ynodXJs3uQzazbCg/k="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "onetime", "version": "0.1.1", "description": "Only call a function once. Unlike the module `once`, this one isn't naughty extending `Function.prototype`.", "keywords": ["once", "one", "single", "call", "function", "prevent"], "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["onetime.js"], "main": "onetime.js", "repository": {"type": "git", "url": "git://github.com/sindresorhus/onetime"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.8.0"}, "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime", "_id": "onetime@0.1.1", "dist": {"shasum": "f4204dc15a47f80c43779340d093ad786c1bab21", "tarball": "https://registry.npmjs.org/onetime/-/onetime-0.1.1.tgz", "integrity": "sha512-0528Ygf3OMseW/vSGJtB+Vcbq2Ax+BgjKlI15z7Ped6Ymifib/7gqWQVUCZd4Gnw6RboKwNKVd3FNdiA/nuWxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxEdvMyedfzhF1VazYxDr2K/yIk3nC1eUeDXj4o2SuwwIgLylAdx4XKmimDSZ2fAitv6HOjIltUHiEPxhGJCuCrFI="}]}, "_from": ".", "_npmVersion": "1.4.6", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "onetime", "version": "1.0.0", "description": "Only call a function once. Unlike the module `once`, this one isn't naughty extending `Function.prototype`.", "keywords": ["once", "one", "single", "call", "function", "prevent"], "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "files": ["index.js"], "repository": {"type": "git", "url": "git://github.com/sindresorhus/onetime"}, "scripts": {"test": "mocha"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">=0.10.0"}, "gitHead": "a4aa1ce5291cc5f32ecb8f4b0a918b12cb47ea5e", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime", "_id": "onetime@1.0.0", "_shasum": "3a08a8e39d7816df52d34886374fb8ed8b651f62", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3a08a8e39d7816df52d34886374fb8ed8b651f62", "tarball": "https://registry.npmjs.org/onetime/-/onetime-1.0.0.tgz", "integrity": "sha512-l2qriHPrawRFZp04MO6cYpmoAiJYLkOlMWhT0iEytynh4SpEz8aVYhmabUbPpUSHMMYsLypm9hAOuonLO7NEqA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHVD85P0BoREZbIv8GD0XE7DOw3J+PbRAc5ubfwUCINfAiEAi0NWvhwxmo/7YAzMmgFMSx3fK3IqUlmPmFhV1LfB6/E="}]}, "directories": {}}, "1.1.0": {"name": "onetime", "version": "1.1.0", "description": "Only call a function once", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/onetime"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["once", "one", "single", "call", "function", "prevent"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "6fae2fb77b95b49719d1c270d8ba07d9515bdfe8", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime", "_id": "onetime@1.1.0", "_shasum": "a1f7838f8314c516f05ecefcbc4ccfe04b4ed789", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a1f7838f8314c516f05ecefcbc4ccfe04b4ed789", "tarball": "https://registry.npmjs.org/onetime/-/onetime-1.1.0.tgz", "integrity": "sha512-GZ+g4jayMqzCRMgB2sol7GiCLjKfS1PINkjmx8spcKce1LiVqcbQreXwqs2YAFXC6R03VIG28ZS31t8M866v6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIALnMOnlI6Xi7iUNa8L0cgedvF4eGcrLB4fsrYp6FveZAiBx5A1MM4JCkloikT6B+s2Op2XngIDOd0uAvX/Zd4m4rQ=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "onetime", "version": "2.0.0", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "bb498adb2da79e62b923173480803a9c6f759766", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@2.0.0", "_shasum": "52aa8110e52fc5126ffc667bd8ec21c2ed209ce6", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "52aa8110e52fc5126ffc667bd8ec21c2ed209ce6", "tarball": "https://registry.npmjs.org/onetime/-/onetime-2.0.0.tgz", "integrity": "sha512-mEmAlKE3vyOliiPShkY8zIXRY+jR+Bns1oWRlM9mIiqM04BxsHuolKc8eISRqUloNzJQEF9HE6AfoN9AWz44EA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvaNILtsfJbIegfkDuWoGI/U9ZamiT4p2Rj1X53iRLnQIhAJPOCe/RvUHE7nNtrqoJPgQgMnIx/28kb6mUxSynrXRa"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/onetime-2.0.0.tgz_1476902515797_0.18329595238901675"}, "directories": {}}, "2.0.1": {"name": "onetime", "version": "2.0.1", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "32bca382f5934c8fe7fd78bcef9ad16b3474948f", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@2.0.1", "_shasum": "067428230fd67443b2794b22bba528b6867962d4", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "067428230fd67443b2794b22bba528b6867962d4", "tarball": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "integrity": "sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpcgSTTHOnAvmHO+YBNmWoLqYczXdKiCgGoWG3kxaxQgIhANueugwrWHtq2caFMzdzznRP/7XVjJlTdVcy8oZAKDuG"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/onetime-2.0.1.tgz_1489980257371_0.244376125279814"}, "directories": {}}, "3.0.0": {"name": "onetime", "version": "3.0.0", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^1.0.0"}, "devDependencies": {"ava": "^1.1.0", "xo": "^0.24.0"}, "gitHead": "2057a659c432c544e2f14bee3b7e99abc210ba9c", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@3.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.6.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-t2j1nTo7vb2m/ZQAq5rcWjQgAglF/2rnvlO0cxkZ1GFOSEt0sQBHaytm5tC1ZNUlmKZAp5XF44kolGL9W/XJ2w==", "shasum": "7fc4d348742091d2510328455a556cf137e16d75", "tarball": "https://registry.npmjs.org/onetime/-/onetime-3.0.0.tgz", "fileCount": 4, "unpackedSize": 3798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRJXlCRA9TVsSAnZWagAAKKkP/jZDW6DTGZu4c6tLTFPc\nGHyKtLQvDOeo8WECIMrmK4QsId/RgJmlEHSjxEnWm0mXaM5A5GbrtARfl92U\nhUurfEdm1lkLljrmRZxMJ3/NFMMhAuRjXYsZ2LP45anqxEwreEspp59iL36b\nxSpi6uDDspYxleIgCmeXY5cSq/I7aXx1j42oCJzgznWSuVCBn5/S7g3sTZan\nwHfGMLi0jg733ccG8SgnNUgKCJuUEjMgWm8geS6Ks2I9gxJItipl2yYqo50S\n65nYEo94T9Q+bIz3hoyBLj25+ajT6oAtj1OQWI4XqBGEcKLPKyrjdwRc1uLB\nv7wUdoIwerc1Qm7mLqEiSLGabsH+CXOG/G1d3rFbYeZqKDapWhEP9S1ZAOzx\n+Rk5UJnw9AENOK5jfx32BvShAzAoyNj/ZYWMsQyIgQ6kORXoh/1pRBv1nRnK\n3p2TvSFwIEvEGPvwMz3bWCjAt4FZlDu34x80J5h7YxjUuEKuE5h0Omup2UcR\nXwXbob7oytHDtbJ5kSljeIWXe7+SHGAGa944nU5LWo5T0T/9jCohBzOe25h+\nh8ue2KvarQPkO7JUHHUIQMvqpv3spQ2k7yPh8mKl/MSUTQVShF3CbzG52CHw\ngvkbCRX1SSVEG3nnb9EO4yJIlTdF6xAS7k1saJyz/wTN0nyFZqV69jB/lEPg\nkD2W\r\n=NG+A\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7OSQ2HkJ60qXDmnLyemFFVaHTwpb2cJKAuQRoQZwmrgIhAIvvBuB9aLhCqVG894cmVR/WfIY0G++1NDbQ8tTkhfeF"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/onetime_3.0.0_1547998692929_0.8925190275053334"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "onetime", "version": "4.0.0", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^1.0.0"}, "devDependencies": {"ava": "^1.1.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "65ad3c02a2ad06d6ec1f49ce80c98f4a5c172bf6", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@4.0.0", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-woeNhttKxY5jRwaoDMB74JYFaJAAegWXNcXm4CLd4DBfd4ZkrQeIPol0YylMyUd5uG/g6cPAUULYlPIruQSz6A==", "shasum": "143112c91abc470e9e70a7b2636ce773c1fdf5b2", "tarball": "https://registry.npmjs.org/onetime/-/onetime-4.0.0.tgz", "fileCount": 5, "unpackedSize": 5117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcePgqCRA9TVsSAnZWagAAn5UP/2yF7Iu4Gs4i4sWgyl3m\nbEW8JvbJ6ac/Xkik8CJbUdzOfCW7YO+/P9TJHX0VP0yylOuaW2r/bcap4ZpO\nXmwwSICCsLQs3DLnjNGGONjDyI8JwozR9JdzIP129KorQXesOkhamPb0PsCa\nvi2pb2TuFqNDS3WFaoxCj+U2tNIXCxv3sN4BRK9iCSx8Qme9IKT79ECDaxOh\n1YJsFoBr//z6nKgmJZ2x8NZM6vYc01AGnYwFNFbODl1O1T6Qye4jbABMI/NH\nb680FQeBfweg2jVr6CPNBr4KzAOsqiwc8DCI12E5lxnET+8oGiPCvRQ7IlX+\nrwo1OdQGofi/Pdo6al281E4ZH8NzyXgMYSabqIp49w9zQmDX2z30a3jOKWww\n5u5yYBcIlxWDuh5hjheyOVSI+bqD/iwhvkxX+sBW2SWicUZRULHsuNI+j8q7\nj0OSZ9Wnc++fpciIeBh59BQ3PhE53hsekkjxMrKwGoC3YCsjQ/WcygbCqyDh\npg63g34BRpuXt+oreIkJF+nOFmnwhCIvH0yTMnicIwTpy0qMwxsTdxf99nJa\nI8uJJJSxsfb7icQI5Oq+339FVAg2QK66eHcPWMOlEl7TyucqvwYPFUZCvuxm\nJQJyPsnOjxUTCWqzZ1TaictfK5QPQ8euKwZXEhnqoKD/0PqslTFJT+4CAWv3\nso2T\r\n=Ehwi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG0jv1mJ58PKKrDZBBXdu5H0wj6fCcCiGauN1NCAV1utAiEAvFhsB6/BojRGGtiVtf1L17xYoE22HNuBePWMPcwnlw8="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/onetime_4.0.0_1551431721222_0.5847573580409424"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "onetime", "version": "5.0.0", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^2.0.0"}, "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "4f1b26154e811b5af3958b759b7c29a2c4692ebd", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@5.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0RaJAdwj6KznGJJdJNHmjCUmtsURmC1SLoTzoa9qI5LY8hODas/vjVlEUL19xaSSZfualrMh4myK0dNhVSgDJA==", "shasum": "eae9df4c4e114f4fe194167bc14cce2d71939606", "tarball": "https://registry.npmjs.org/onetime/-/onetime-5.0.0.tgz", "fileCount": 5, "unpackedSize": 5264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcggSKCRA9TVsSAnZWagAAcroP/0nvOtQL8JNaCBWgt82Y\nrwN3R4G7THTdkAWVlOSwP4icxL5JJAY2O6e2zetTtsh0283pbE0MEVAby3ul\nV+2p/uZk9eCRB5SHBmt0gC6z0FXjJ1+lWEnwE9I0Vl2VtkLlpQu3oX6uWLef\nuvY5BcLGTj/xa8sDXlLIURgVrOE/I+yXaYmd0KBoA3CQfaMpnSk2txFq8gO1\nu51gvc954fIGuzQLy4vJ4A9RbCMaDP9fVmBS9G+YcZBVec5g/WzMtQska/D2\ni9XkJvTyTuyMGlU5lyDsu/V2PGKXM8O3peqmCsTcnF6YtFbURUclwX81hlSn\nyeH6NZTmPfg6TLixRNl9KhR7u1jV+o8kWNUY6PYjMtjw5bDECTVs7l8Y4oiX\n0CqKygv+h6T7Os6eu+gWTOix30OqEejxinc60x88DiuOzuCI07X8/YX0QQfB\nebVImZO4k2Vv+J2sqgjDjnz9gU16pGqdeCkYACaxPWbkM4/rByUfIXlwiubu\nkZSM/JxKwG+MYj5zj3ld/XMFj3YzLLjgmHbe1oIKNg4Kf9sHQDg5Idh5irU3\nEzpFioQTVodjIPuuLL2qNhQTUYcaPSL9oL8fbTrhOYmBbe0r50vzokpc2O17\nTjgVG5MBt5nbyRYAtztr2iqctPeXkQAP5+nzsCu5HZ4hvVOAFEHZo4/bT1OA\nP37m\r\n=XBd0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDz8Yt+6+27rHmmb6vzE3S+W2+PaQUeK8wO9biW3use4wIgeQoCkdFhqDpO4iHcQX9YYhFy5yJjccnoN8tKtIsrkII="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/onetime_5.0.0_1552024713328_0.24572920152994504"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "onetime", "version": "5.1.0", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^2.1.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "8bb24f70649ee69849a0701477ebc7170e7a4f87", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@5.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5NcSkPHhwTVFIQN+TUqXoS5+dlElHXdpAWu9I0HP20YOtIi+aZ0Ct82jdlILDxjLEAWwvm+qj1m6aEtsDVmm6Q==", "shasum": "fff0f3c91617fe62bb50189636e99ac8a6df7be5", "tarball": "https://registry.npmjs.org/onetime/-/onetime-5.1.0.tgz", "fileCount": 5, "unpackedSize": 5343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcost0CRA9TVsSAnZWagAAgbwP/3keiMz92/ghCXk+03Zf\nCK4b/Tip5syC5+mB+ZocghkVETcpmVxyJP6rpz2Jy/I3uSv18oKWRvBAv4xP\nYeDmGVfvNotyp9rKvgCfNDQOyVfskdSyRoG4b+SSyWDFQx4+CquAsdOOIRzh\nrdFH0VIttukJRmQsSjpNQ75TByL09bNMc83oLD9atKNURYPVbny8IcnMp/Ml\nbLtZbRvIG8WuSoEE2EozESMAG48q8/LK1aYEuMWdxllUSPSQsLaxOpLjbHfB\nSlp4Ilz6OPAjrwv912izwJfljIH2y0w30PoprGoF2p7+Xz8cmH9WcQMszQZz\nWQvQdZV2MquJGppoJ5hbK6gqxFeIeZ78HLFvyowp7lx2I27aei2AEVHIE7jv\ntywjziWNxSdbKwLCGEdpES2lAzIQqVG6LQEBTa19TR8yNgFlX5AFVZfx4Mtw\nbEfwWejKyuQMohSDRnGQLUXiosIdFlEIrwH/FMZL3D9d4AC+5+AcZjDP9wID\nafIpFvpDyYzBAAKVi0EIB6tp8+T7zZ7XXnk1z0tAAJ6SpxjVroffjzIBf8OS\nqRRNBBsPgsp6bLnsm7NQcatfcciOtzzo3RP4bLcGiLVwYr2MPLhc+NZFmZqU\nKg2Wdh1nR9Yf5cNuKa1DEf24vLOwhcoraS0ZuStmiR/ofuZxET2ZLAof3aVV\nC9Kk\r\n=RzSo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBUrWLl41ddY9hhlvKVORR01cefsvq3ykwGJ6ythkLtRAiB9jUap7LqWbka6rhIDsYPRvZ6bdlOH0TXq3qCbLrsDSA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/onetime_5.1.0_1554172787964_0.04244092407114519"}, "_hasShrinkwrap": false}, "5.1.1": {"name": "onetime", "version": "5.1.1", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^2.1.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "59e0ad2204b5d44b448a71afb13f228d1977f07f", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@5.1.1", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZpZpjcJeugQfWsfyQlshVoowIIQ1qBGSVll4rfDq6JJVO//fesjoX808hXWfBjY+ROZgpKDI5TRSRBSoJiZ8eg==", "shasum": "5c8016847b0d67fcedb7eef254751cfcdc7e9418", "tarball": "https://registry.npmjs.org/onetime/-/onetime-5.1.1.tgz", "fileCount": 5, "unpackedSize": 6173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJpVjCRA9TVsSAnZWagAA2xQP/2GUXW7XqJBsodgB7rig\nzrVUJhnp0fxhrAf6zqeeCkCnBJRNGQToqg+c+wZVoMo70+r+vwH3sGbJXJLd\n7vraWS3wJBGrrImzLDGck4YF0DKQ4vzNBrWvEpdTZhiJp3/IUSY2C5GJ8CyB\nMiR8Q66/ylWf8OqbRsbCn4iGPCnm4aOGHObXVVehbHgIxPgzKnykp4bRjr3P\nDTrKolDIOGQV5poP0BK0qBeYvLcWeCDa/+nAUVxjXNVsBrpfeXeqjfUkfmAf\nuKdFiD/b0DCo4qcpKu9TNB77ZYraUDHSYrISwt7A2TffhzGzIUX1liZ5oDyh\nYrvHok8Fc7Rf0fvKpCdoNdsrkUpWtS+ESS7usSnp5c/Kb9Vq1nCPeoSyS2Kh\nswlP1n1JHzDsDUa3aeS9jZV7KxxERIJZfOXAwpzzHG3d7HaVa379C7BslKSY\nL6PhlYZesTXg/j8qRyXdOppF1T145VvV8JLHKpvj38v+F8P8t6LnHUAeFuAE\nW7c81AFYgPckuIzlu5cwY1kuFXE5y9uXv/5HxneOe1s+jjTYTM+i8BYEOMPp\nx1BTymCRNlm7X+uHQkrxsEp94UIUBXFM0XGgtf4wwr7vS/lyxVtZiADrTjlC\nccNfRI7GowFc6DFUBTqqJAUXaHfDVBapOHSuYVYRjlzXUuPznysun2tPa5PC\n8EMe\r\n=/FeN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFGklvIdWWjxf5NBdjrjwjkjpzM0FsGsvUedoAknnjWwAiEA35WN5BfEIzrPEzU29e/DtOS2LkBEpIYtIZPQ+lf+h/o="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/onetime_5.1.1_1596364130751_0.20576953897408923"}, "_hasShrinkwrap": false}, "5.1.2": {"name": "onetime", "version": "5.1.2", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^2.1.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "4fa3c9a40798476514fec36a1720dc85b897cc04", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@5.1.2", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "shasum": "d0e96ebb56b07476df1dd9c4806e5237985ca45e", "tarball": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "fileCount": 5, "unpackedSize": 6173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMFyRCRA9TVsSAnZWagAAInsP/RTd8G7KIVrwux1TNO4h\nS+Ymvz0874gBCS5I8tXFkdVSVAqPUNX3undeJIAHSqV7UMdAPx8wiFDqmga1\n28xR3LA8iHSyo1pxxIboH1LWJgaG2NWIfFV/fAPLvPJzEqmQ4zU0ioYhiwjj\n+MRlfRbkvFSqF9bdIm29VEKLzJPBtLM+BnHHmHigB3IA+fkhy81oo5ESmUPC\nUBeMJyXVMHU9v6r/0PQtqfTnVD68c+1o6yfevC8P0CjOCydJlWltCfKXhMNw\nxTVUxzaU4rfEHXsosGV79jCfLDcibLVdbjAMFzq1P6HaAKSl0ry2TrHKC/o/\nusdJe6U/jqxRN9moiuyh+qxR/9heTMXGgP1xrq0m9jwJlU681vlaPcQaQlM0\nZ0x3/TC1F+Nj6vOTG3lPBunne+H/0D4vu5MSDha8uzyAJ0mHarP+/hPP9gEZ\nUB9cT2djKZATqv4S+S3WKvYZa+m5pupDzVV4YbF3XgCSBe4mB23GdzllY76x\n9SCs7s7d6jUc+0FO/WhyASagXxeqqEwsrN4GRhfUB5l2y9/Le/j7sACW+YU7\nS4c6ony0Tojzb3ncc5zbebH/tskKZb6SCd6nZFBKs/CpDNqDueIkIyjbWLPH\nHUcN6AgD/j5xyIxeIbYc0HH7bc0NovpQbGeP8sIe1o+nS2WOTnjx0LPMCUP1\noLdv\r\n=J6Zn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSvlynRcdgHdta0H4svzquLsEgRP7Iy+uD+ExsJFl/eQIhAPxJGn9aczZEPZWZyI8oAgKAiLlTZyWB+FB7/LkcTRgI"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/onetime_5.1.2_1597004944849_0.8241938525832997"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "onetime", "version": "6.0.0", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-fn": "^4.0.0"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "e5fd86f2303546a2d9cc82f2a72c3ea840f6a2dc", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@6.0.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==", "shasum": "7c24c18ed1fd2e9bca4bd26806a33613c77d34b4", "tarball": "https://registry.npmjs.org/onetime/-/onetime-6.0.0.tgz", "fileCount": 5, "unpackedSize": 5882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbwmVCRA9TVsSAnZWagAARaAQAIYR90u67hRC49WkfGqS\n9OQMcfbMt4ftU1V5Je1ksDpkbXjb+ZOFCk+oCuQd/PXLY4x0g6oJxKb4BDbk\n7KnHD0divsyJi0zhFh/YrGppudlOyfRNjMayyBjlfXXtcVRkf7EWYYILqBSo\nzukCbwPoi17vTWPhhz13PCdnfhGMzLpZuBd41LSkv58tb70ocwWND3NVGn39\n+ZlR5B70FkUVnshl9OMZW9Zpn+6BUzYvKu58uT9lUCcNt2EIvNZi72nXiOLt\naOlTSCqHkjpA6Jag4uJf45qj4cJbaHBKI9MhvKusxOTmuaTzi54omfXNmfjb\nRotyFDsb3B2nhpuKN7yfNjWpRiT0Ky0TRBGL361AKsTdvKFTtkkIzPfkltIh\n0ZtA7hYrEt71K7jHGkkJQtO/dlTxkhqfxOD/rZFVpi5oxF+q/l2tnqf8ryT8\ncPB4Sn8pQoUtH3z5kS5n13rdg31S7OyC7226zc/ztLbitZDaWgFdIMpIn0La\nh862sPaesXn9brcbkOTohEj2VTEZGnatEwfEx64pOYEm3ws5Z2N6VxUKiJaV\nLC+WNtHqFOlKvos85jGdYPuNUc+1A5SB2hOMTsNmR7ycsExf98PppPF6X803\nb8OLhbOWCjAUr9SzHWrznaE28BuBozd9yn2SQhV1nrMKC5n1K/JjJkshmVp0\nZBq1\r\n=ELkF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFqcBwB60cJl2njYtLGOfrmMBQlvZSTXg7j9oKZNuo/UAiB0uaXy3+XFJB/ZoDIz5Vv/rdTuRumoFHfmNTYsH+rQjg=="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/onetime_6.0.0_1617889684671_0.003830851530065793"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "onetime", "version": "7.0.0", "description": "Ensure a function is only called once", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "dependencies": {"mimic-function": "^5.0.0"}, "devDependencies": {"ava": "^5.3.1", "tsd": "^0.29.0", "xo": "^0.56.0"}, "types": "./index.d.ts", "gitHead": "660d06d232ab9f18fce7f47d60d4eeb7e885a35e", "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "_id": "onetime@7.0.0", "_nodeVersion": "20.9.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-VXJjc87FScF88uafS3JllDgvAm+c/Slfz06lorj2uAY34rlUu0Nt+v8wreiImcrgAjjIHp1rXpTDlLOGw29WwQ==", "shasum": "9f16c92d8c9ef5120e3acd9dd9957cceecc1ab60", "tarball": "https://registry.npmjs.org/onetime/-/onetime-7.0.0.tgz", "fileCount": 5, "unpackedSize": 5447, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB8pl9KG/4QqxN7rIXTi2ZdObax844waKu+rfeMzkc7qAiAzKiTZAlVGqlBo2lhhB92v2jtL3sui6eo+te70UXwTSA=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/onetime_7.0.0_1699216923245_0.23876281160998514"}, "_hasShrinkwrap": false}}, "readme": "# onetime\n\n> Ensure a function is only called once\n\nWhen called multiple times it will return the return value from the first call.\n\n*Unlike the module [once](https://github.com/isaacs/once), this one isn't naughty and extending `Function.prototype`.*\n\n## Install\n\n```sh\nnpm install onetime\n```\n\n## Usage\n\n```js\nimport onetime from 'onetime';\n\nlet index = 0;\n\nconst foo = onetime(() => ++index);\n\nfoo(); //=> 1\nfoo(); //=> 1\nfoo(); //=> 1\n\nonetime.callCount(foo); //=> 3\n```\n\n```js\nimport onetime from 'onetime';\n\nconst foo = onetime(() => {}, {throw: true});\n\nfoo();\n\nfoo();\n//=> Error: Function `foo` can only be called once\n```\n\n## API\n\n### onetime(fn, options?)\n\nReturns a function that only calls `fn` once.\n\n#### fn\n\nType: `Function`\n\nThe function that should only be called once.\n\n#### options\n\nType: `object`\n\n##### throw\n\nType: `boolean`\\\nDefault: `false`\n\nThrow an error when called more than once.\n\n### onetime.callCount(fn)\n\nReturns a number representing how many times `fn` has been called.\n\nNote: It throws an error if you pass in a function that is not wrapped by `onetime`.\n\n```js\nimport onetime from 'onetime';\n\nconst foo = onetime(() => {});\n\nfoo();\nfoo();\nfoo();\n\nconsole.log(onetime.callCount(foo));\n//=> 3\n```\n\n#### fn\n\nType: `Function`\n\nThe function to get call count from.\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-11-05T20:42:03.583Z", "created": "2013-12-13T21:20:15.964Z", "0.1.0": "2013-12-13T21:20:18.951Z", "0.1.1": "2014-04-30T22:39:14.004Z", "1.0.0": "2014-08-13T17:03:51.426Z", "1.1.0": "2015-12-18T00:30:55.390Z", "2.0.0": "2016-10-19T18:41:56.004Z", "2.0.1": "2017-03-20T03:24:17.619Z", "3.0.0": "2019-01-20T15:38:13.275Z", "4.0.0": "2019-03-01T09:15:21.375Z", "5.0.0": "2019-03-08T05:58:33.472Z", "5.1.0": "2019-04-02T02:39:48.168Z", "5.1.1": "2020-08-02T10:28:50.856Z", "5.1.2": "2020-08-09T20:29:04.963Z", "6.0.0": "2021-04-08T13:48:04.866Z", "7.0.0": "2023-11-05T20:42:03.405Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/onetime.git"}, "homepage": "https://github.com/sindresorhus/onetime#readme", "keywords": ["once", "function", "one", "onetime", "func", "fn", "single", "call", "called", "prevent"], "bugs": {"url": "https://github.com/sindresorhus/onetime/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"schnittstabil": true, "n370": true, "anhulife": true, "quocnguyen": true, "rocket0191": true, "lamansky": true, "reyronald": true}}