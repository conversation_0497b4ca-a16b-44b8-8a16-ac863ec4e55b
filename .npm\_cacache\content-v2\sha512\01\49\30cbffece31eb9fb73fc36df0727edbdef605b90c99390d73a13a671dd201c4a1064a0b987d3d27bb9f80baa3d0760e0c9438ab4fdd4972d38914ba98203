{"_id": "braces", "_rev": "69-69affc6ab73028ee68c70d39e6bd3bdd", "name": "braces", "dist-tags": {"latest": "3.0.3"}, "versions": {"0.1.0": {"name": "braces", "version": "0.1.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "_id": "braces@0.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "2ea567d1f9622ba2395135bbf81fe140da7fefeb", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.0.tgz", "integrity": "sha512-59/QQZW1FIbvnxuRWTNaulRIvblwjvGKwz88r/HJIN1TE0N12M2pbRNZpYUWuE7DAyl8C2ucWeX9M7DYWnZVpg==", "signatures": [{"sig": "MEYCIQCOL3eGBoq0k7nH2F691tD0m6exLWdtfTnzmOp3Z53VJAIhAJMIiKzk64c+FOlumGZrtXflk2hTJ61cqjUxmG7RDMbN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2ea567d1f9622ba2395135bbf81fe140da7fefeb", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Faster brace expansion for file paths.", "directories": {}, "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"verb": ">= 0.2.6", "mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "verb-tag-jscomments": ">= 0.2.0"}}, "0.1.1": {"name": "braces", "version": "0.1.1", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "file", "filepath", "fp", "fs", "glob", "globbing", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "_id": "braces@0.1.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "9b3c273b426d1a7b3c9265094a9434ab84643ba3", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.1.tgz", "integrity": "sha512-biKFllQsz6LzOEwAFWX/CMy8NtDzGwXYUXFgNWWRuehPC3X0okAb5lEFf2PtnHwHYe6MoJZD0fuy+BFtoJC5Sg==", "signatures": [{"sig": "MEQCIHpjFQZQTuLjI2kVC0/j4P7nCNB6h/bqDAwNiyjYMUucAiBrPb8F3o5/9t0ABtKhRDH6uEUPCU48IUyR+H84pUiqrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9b3c273b426d1a7b3c9265094a9434ab84643ba3", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Faster brace expansion for file paths.", "directories": {}, "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"verb": ">= 0.2.6", "mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "verb-tag-jscomments": ">= 0.2.0"}}, "0.1.2": {"name": "braces", "version": "0.1.2", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "file", "filepath", "fp", "fs", "glob", "globbing", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "_id": "braces@0.1.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "f794ff9aabe60eaf557e9607cc850c8f531a12be", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.2.tgz", "integrity": "sha512-tQ1ltvLIdYGIpKDwnjDqqJ7ZYxDOHsJiBNgp2N9RsEAtPzTcKq3aJlQ40+qXWYHFpy4cC5wthznUE2M89xlMSw==", "signatures": [{"sig": "MEUCIQCNq6CFY2R4ob2MAgFcDmF2kxY/bNOvA0IFG49cedj+wgIgVqDI7ub/vsFCPDEb5ljAVtDVisI9QjnxJIMzi04RaKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f794ff9aabe60eaf557e9607cc850c8f531a12be", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Faster brace expansion for file paths.", "directories": {}, "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"verb": ">= 0.2.6", "mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0", "verb-tag-jscomments": ">= 0.2.0"}}, "0.1.4": {"name": "braces", "version": "0.1.4", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "file", "filepath", "fp", "fs", "glob", "globbing", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "_id": "braces@0.1.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "45ed440222debb0fec60b06bb75059e421c590fd", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.4.tgz", "integrity": "sha512-5ZaWT3BXeL4rfMmOgZUktJ3YExoTc/tfHFV8xJRb5At1uATRxLwXJY/cItMFzCF4e33yPZWOR08mbmUJneCQIw==", "signatures": [{"sig": "MEQCIDY8GVKmrYczFFWJwUqAAHdRXL7ERQbSaGpn+Z+Eov/TAiBn/z7Xq1jxNbeoZd7j8GgmZVjOZm7KegKIOPxFzCtxgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "45ed440222debb0fec60b06bb75059e421c590fd", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Fastest brace expansion lib. Typically used with file paths, but can be used with any string. Expands comma-separated values (e.g. `foo/{a,b,c}/bar`) and alphabetical or numerical ranges (e.g. `{1..9}`)", "directories": {}, "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0"}}, "0.1.5": {"name": "braces", "version": "0.1.5", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "file", "filepath", "fp", "fs", "glob", "globbing", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "_id": "braces@0.1.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "c085711085291d8b75fdd74eab0f8597280711e6", "tarball": "https://registry.npmjs.org/braces/-/braces-0.1.5.tgz", "integrity": "sha512-EIMHIv2UXHWFY2xubUGKz+hq9hNkENj4Pjvr7h58cmJgpkK2yMlKA8I484f7MSttkzVAy/lL7X9xDaILd6avzA==", "signatures": [{"sig": "MEYCIQCDFqf6OmryqizB/xGTkW3Sv+ZJAWKx7c1g6DJJpjfrGAIhAO+baQ23z9OplIU4Ba9KhNamedIpq8a9EarynCSgYPck", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c085711085291d8b75fdd74eab0f8597280711e6", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Fastest brace expansion lib. Typically used with file paths, but can be used with any string. Expands comma-separated values (e.g. `foo/{a,b,c}/bar`) and alphabetical or numerical ranges (e.g. `{1..9}`)", "directories": {}, "dependencies": {"expand-range": "^0.1.0"}, "devDependencies": {"mocha": "*", "lodash": "^2.4.1", "should": "^4.1.0", "minimatch": "^1.0.0", "benchmarked": "^0.1.1", "brace-expansion": "0.0.0"}}, "1.0.0": {"name": "braces", "version": "1.0.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "file", "filepath", "fp", "fs", "glob", "globbing", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE-MIT", "type": "MIT"}, "_id": "braces@1.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "991c57970805294a6f1a8aade96c9037fcea1246", "tarball": "https://registry.npmjs.org/braces/-/braces-1.0.0.tgz", "integrity": "sha512-96tjnHiAQnke1h8ibSkJXk60GMG2JFSad0zwnM2duZDfWtJXEyJ8xDlRoMLvPPotk/0uJtAk2XIp0VwLrJ+GTg==", "signatures": [{"sig": "MEYCIQC9Sj6lFLjKhO6jg4sjR7WZRVfDkhcIsbNSjDL3LhJXLwIhANRKzJ078ZWecvsGRHEWRwDm0fDmioQBnsEVueKuqwn0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "991c57970805294a6f1a8aade96c9037fcea1246", "engines": {"node": ">=0.10.0"}, "gitHead": "1acbc5c311a615b0cb94f5309f40329a6d8b6bcb", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Fastest brace expansion lib, with the most complete implementation of the Bash 4.3 spec.", "directories": {}, "dependencies": {"expand-range": "^0.3.1"}, "devDependencies": {"mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.4.1", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.1.0": {"name": "braces", "version": "1.1.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "1fd01ee8420a7196d234f2a29dc1bfe6e259854b", "tarball": "https://registry.npmjs.org/braces/-/braces-1.1.0.tgz", "integrity": "sha512-/41xAQljVWmYtUjUrfjRGdEAUD/Z4fEJKziIDuQN20hniJX/opnWHGjyvNhf7u920PinSkh58l7vsjM43HpcmA==", "signatures": [{"sig": "MEUCIQDwFF1Vw2Yqpj+A/cPHCFIYGCArjD7YQ6B4hLdWHdyUxwIgd851beoqMm41jkTBCOGwhdHzLTAL83818OMgVcjMNh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "1fd01ee8420a7196d234f2a29dc1bfe6e259854b", "engines": {"node": ">=0.10.0"}, "gitHead": "762112a749c669e7fae57eace98e481e145b3bfa", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Fastest brace expansion for node.js, with more complete support of Bash 4.3 than minimatch.", "directories": {}, "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.0", "expand-range": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.2.0": {"name": "braces", "version": "1.2.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "b86f8f3898d11dc1008d29176a45b4863190e7b9", "tarball": "https://registry.npmjs.org/braces/-/braces-1.2.0.tgz", "integrity": "sha512-LrWXOZAaORkFo9btgFznnPWxqe0xfUUIse+7OLWzt84ddPX0747PZS+BV86W1F3VyIeOhckQ4CRBhp6DnPhgYw==", "signatures": [{"sig": "MEUCIFfkmBRM20jzAJxcfeIGGBQ7QJQSuDljwofQxSFYrxowAiEAz8Z3AdNhmnzaFspZXctXsDMM5WAxEXqomDoorCWvcaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b86f8f3898d11dc1008d29176a45b4863190e7b9", "engines": {"node": ">=0.10.0"}, "gitHead": "0255915a73f3feaf271261e589cc8975c1e15596", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Fastest brace expansion for node.js, with more complete support of Bash 4.3 than minimatch.", "directories": {}, "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.2.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.3.0": {"name": "braces", "version": "1.3.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.3.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "e0495cf4796c137fb0d80a922ffef431fbde6bef", "tarball": "https://registry.npmjs.org/braces/-/braces-1.3.0.tgz", "integrity": "sha512-5T2hlMwxt1IhrjcL6wdbCi/wOTwtdEFc58D/G0ER1MkO8syeMF0TcCf0KTSUpuEdrbN3FOCf+SN/xl8vWRHBAA==", "signatures": [{"sig": "MEYCIQDN/7B8Og/Wy28kURLe2hLBCnnOnmSukcH27p7q2DJkPwIhAIRKMM4e+qvYvR+qghOhJYWkIjWj8o+fTVtcfPeKDqUC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "e0495cf4796c137fb0d80a922ffef431fbde6bef", "engines": {"node": ">=0.10.0"}, "gitHead": "0255915a73f3feaf271261e589cc8975c1e15596", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Fastest brace expansion for node.js, with more complete support of Bash 4.3 than minimatch.", "directories": {}, "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.4.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.4.0": {"name": "braces", "version": "1.4.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.4.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "c95b1b8fd045b765703c2c0cd95e0369e5696492", "tarball": "https://registry.npmjs.org/braces/-/braces-1.4.0.tgz", "integrity": "sha512-A7mrTs9OAQrOpAHne8Ukiit7Aue9TNkcg0QO/hw/U8T43VUWPvIbhCCbgpoCgCEza8Y50YpKpC89+0SlWa9jWQ==", "signatures": [{"sig": "MEQCIEzrJDaiFot3RuXZxYT5quEj3HsGh34zG6msGJcqORFhAiBouLfTe9foAWFl0dfYujva7ubt6sfpihmjKqZNdHUvNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "c95b1b8fd045b765703c2c0cd95e0369e5696492", "engines": {"node": ">=0.10.0"}, "gitHead": "4026752b8795c282a4ba2546eb88c9d1ee9b6653", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Fastest brace expansion for node.js, with more complete support of Bash 4.3 than minimatch.", "directories": {}, "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.5.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.5.0": {"name": "braces", "version": "1.5.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.5.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "3600d82f04390dfc7c6016f091657b2d86189a15", "tarball": "https://registry.npmjs.org/braces/-/braces-1.5.0.tgz", "integrity": "sha512-lxYGtXXPqFES/5rDs1l01Fy6GM3hlZm65uxHgbcHvkcz0TenXPm7rbye2JaKl/RSJ6/cJwW+tC1ax34rKrk3IQ==", "signatures": [{"sig": "MEYCIQCsG1NX/QLtOljA09R70Mar4YY8P/Fw0DRlToIDsDF01QIhAP7C2kx+EL6qNY1F2E0te1BgJ9mq4Fy4pv4QQKyyYAR4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "3600d82f04390dfc7c6016f091657b2d86189a15", "engines": {"node": ">=0.10.0"}, "gitHead": "46547e1d1553c497a061d13b8ed3c85b2d16b6d7", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Fastest brace expansion for node.js, with more complete support of Bash 4.3 than minimatch.", "directories": {}, "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.5.0", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.5.1": {"name": "braces", "version": "1.5.1", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.5.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "faefcd7e5251c84c21127e8f079b14775b6c06ce", "tarball": "https://registry.npmjs.org/braces/-/braces-1.5.1.tgz", "integrity": "sha512-PhxDD2jLYwNFNO0hYkYGTMIh+n+ioNXi6cM+TYyWaqruoGOpjWTLGj5XFnIHciR5snxwtQtLzfOU85gEqR0QPA==", "signatures": [{"sig": "MEQCIFW3wVrwoHuYTlPJptKRAC1TFsjBsdKJpQh7w2KPgSC6AiBuJh+IuAE0FAo9CsCP8ceL7TBqQllQCSarmURQZ3qNkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "faefcd7e5251c84c21127e8f079b14775b6c06ce", "engines": {"node": ">=0.10.0"}, "gitHead": "a4a43ccb73b67bb8c86ded66f57417e5b13bba15", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Fastest brace expansion for node.js, with more complete support of Bash 4.3 than minimatch.", "directories": {}, "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.5.0", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.6.0": {"name": "braces", "version": "1.6.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.6.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "438c23185022e8fa968af9feeaa056723a3efe55", "tarball": "https://registry.npmjs.org/braces/-/braces-1.6.0.tgz", "integrity": "sha512-bq8Ul/s4vyD8GX2jSqUT/Db+BIPktohKewWzaczlB9+BVMVx0Y423mbsPlbRussx5F5n0OH6n0PJODyqWugCBg==", "signatures": [{"sig": "MEUCIQDP3e1ne1dpP/BKoALWvQemr1ChV7BXzXEe/F1UeJdYlwIgIPcZ0OzKtsifQXsxYRjvHGTozSVFl5o3d6M9riTEUIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "438c23185022e8fa968af9feeaa056723a3efe55", "engines": {"node": ">=0.10.0"}, "gitHead": "25551ac2f8dade4ee68b4eeece4a338a9d8263f2", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Fastest brace expansion for node.js, with more complete support of Bash 4.3 than minimatch.", "directories": {}, "dependencies": {"preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.8.0", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.7.0": {"name": "braces", "version": "1.7.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.7.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "ed954c25e8a170971202316c1899fa98fdae6d6d", "tarball": "https://registry.npmjs.org/braces/-/braces-1.7.0.tgz", "integrity": "sha512-vUUn0ZC0UApO70cQQfi6Pd7HNU6drH7opymAFr3gYtZ+Go45nimpawAdnDnKj3wrPDFsmqqHc7JUE7PZHznmvA==", "signatures": [{"sig": "MEYCIQCvOmFd0P4aAwckgwbe5LQxlio5OQaTO4xDHkcQtnhsHAIhAN+Z+xknTEdwP/amfK1Lc8ArDjjGI4jrRI4NnhU0R2F8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "ed954c25e8a170971202316c1899fa98fdae6d6d", "engines": {"node": ">=0.10.0"}, "gitHead": "3960c6f34a4383b657e9bac59fe1345e5d3177c1", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "directories": {}, "dependencies": {"arr-map": "^1.0.0", "preserve": "^0.2.0", "arr-filter": "^1.0.2", "expand-range": "^1.8.0", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.8.0": {"name": "braces", "version": "1.8.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/braces/blob/master/LICENSE", "type": "MIT"}, "_id": "braces@1.8.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "3a4a005aae0391817c17b074dca8f08e6fc9e4c4", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.0.tgz", "integrity": "sha512-zmetXdAbxy7iu++21YqfIvdufiV+ndJzUck78ObHHMMiM/HRNLWGiI052nUT5A623rRudpld0hfkOBiwoIiCIw==", "signatures": [{"sig": "MEUCIQDXD59y/lpCgSIGsGDtSM5xoDm0fx71LLvRdfhZoe5mQgIgNQnPJ1UcgREamXcgQY+eCelSIA9XDdiuqUyTmEbCBvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "3a4a005aae0391817c17b074dca8f08e6fc9e4c4", "engines": {"node": ">=0.10.0"}, "gitHead": "3960c6f34a4383b657e9bac59fe1345e5d3177c1", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"preserve": "^0.2.0", "expand-range": "^1.8.1", "repeat-element": "^1.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.8.1": {"name": "braces", "version": "1.8.1", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@1.8.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "2d195b85a0a997ec21be78a7f1bc970480b12a1a", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.1.tgz", "integrity": "sha512-92peuXcKVAXcbTGlsftd1PHiVAi363Ijh/IkNA6+7tWCupPvZDsNn9p5/7ecs7SUz1SrJrAPf0WM2vU8Vl8qqw==", "signatures": [{"sig": "MEUCIQCw5WQkzyCgll3e+W4R/nh78vzg539fX8CK2iW8Di4X2wIgIVd55OQ6w4vdS7V5/RMulNvGZaG228oonKMPh2Bi/rE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "2d195b85a0a997ec21be78a7f1bc970480b12a1a", "engines": {"node": ">=0.10.0"}, "gitHead": "04aa03dccdadaf469bb7cad86ac8a31d21e974d7", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"preserve": "^0.2.0", "lazy-cache": "^0.2.3", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.8.2": {"name": "braces", "version": "1.8.2", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@1.8.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "036e024051d4bbc7096428b4d6f20ea1f137a794", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.2.tgz", "integrity": "sha512-SY1uQ2Z0f8nLzUp6jJIvNVMLmzmzpVb6C13ipLEJlPL4XbFc7fEhVYJxMNpqt05SKg46oX3h1clGdqEbfBtwyA==", "signatures": [{"sig": "MEYCIQC/5jL4D38DykU/Z1jdJjEH3L2LcWpB1ku55nt1+4VfZgIhAOVXa+j8zMmIBNXiyChoYtdJ88PgZZpYWRFnzwfveVQR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "utils.js"], "_shasum": "036e024051d4bbc7096428b4d6f20ea1f137a794", "engines": {"node": ">=0.10.0"}, "gitHead": "9ab31c0dbf663ad08a7d9519286858f24e2ea9ac", "scripts": {"test": "mocha"}, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"preserve": "^0.2.0", "lazy-cache": "^0.2.3", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.8.3": {"name": "braces", "version": "1.8.3", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@1.8.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "35d4e7dda632b33e215d38a8a9cf4329c9c75d2c", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.3.tgz", "integrity": "sha512-a1Y3ZnTdzkI10NdmKkDpgJGq4pXLTy/QCvsyHFEhMM+8fMnx2+SB8r+7HoKYMmqlSv8tgbs3nKypNEEZSTC4uA==", "signatures": [{"sig": "MEUCIBkqnveOyF2nqvCby4J8lhsJdOLAlgdtbOFWfVLhV9DpAiEA2XOrMIrwocYjaHeB3/XjNKrdKaA1H50ZVcnMubh3eks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "utils.js"], "_shasum": "35d4e7dda632b33e215d38a8a9cf4329c9c75d2c", "engines": {"node": ">=0.10.0"}, "gitHead": "9ab31c0dbf663ad08a7d9519286858f24e2ea9ac", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"preserve": "^0.2.0", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3", "brace-expansion": "^1.1.0"}}, "1.8.4": {"name": "braces", "version": "1.8.4", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@1.8.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "75e2d6456d48b06dbb5205ed63442a3bfc5eefce", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.4.tgz", "integrity": "sha512-Ed5g6san2X6MCCzrwAPTAfe9Ak9zkgXQ9jUhgSRiUsb5+/ceptlRtHNbHOlc2e2SVkrGW1Jayf6AMR8YFyMoQg==", "signatures": [{"sig": "MEUCIQDusQlg241xbLlkaSpcXF+SxZ1+DU8p6zyaFcQa4RnhrQIgf7kVVR4gRv810KJNL090zGwLSPG9eoZ4ksO7Cpq4ITE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "expand-range", "fill-range"]}, "reflinks": ["verb"]}, "_from": ".", "files": ["index.js"], "_shasum": "75e2d6456d48b06dbb5205ed63442a3bfc5eefce", "engines": {"node": ">=0.10.0"}, "gitHead": "de218311bfb9d3c72531beafec67c6572b5e9c18", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"preserve": "^0.2.0", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^1.1.3", "mocha": "^2.4.5", "should": "^8.3.1", "minimist": "^1.2.0", "minimatch": "^3.0.0", "benchmarked": "^0.1.5", "gulp-format-md": "^0.1.8", "brace-expansion": "^1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/braces-1.8.4.tgz_1461140683394_0.9431159163359553", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.5": {"name": "braces", "version": "1.8.5", "keywords": ["alpha", "alphabetical", "bash", "brace", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@1.8.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "ba77962e12dff969d6b76711e914b737857bf6a7", "tarball": "https://registry.npmjs.org/braces/-/braces-1.8.5.tgz", "integrity": "sha512-xU7bpz2ytJl1bH9cgIurjpg/n8Gohy9GTw81heDYLJQ4RU60dlyJsa+atVF2pI0yMMvKxI9HkKwjePCj5XI1hw==", "signatures": [{"sig": "MEUCICyEz9Yu77563hTY24b9FT8m3wzp6vo4XAXfMvaMnUq1AiEAxO+ub+3gGJDKnFmASOt4CsPYLHU6RpwJK4YeGKH6RiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["micromatch", "expand-range", "fill-range"]}, "reflinks": ["verb"]}, "_from": ".", "files": ["index.js"], "_shasum": "ba77962e12dff969d6b76711e914b737857bf6a7", "engines": {"node": ">=0.10.0"}, "gitHead": "24874614ebeda1c5405180f1f6c9f374bcf384ce", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Fastest brace expansion for node.js, with the most complete support for the Bash 4.3 braces specification.", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"preserve": "^0.2.0", "expand-range": "^1.8.1", "repeat-element": "^1.1.2"}, "devDependencies": {"chalk": "^1.1.3", "mocha": "^2.4.5", "should": "^8.3.1", "minimist": "^1.2.0", "minimatch": "^3.0.0", "benchmarked": "^0.1.5", "gulp-format-md": "^0.1.8", "brace-expansion": "^1.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/braces-1.8.5.tgz_1463843581552_0.5618140168953687", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.0": {"name": "braces", "version": "2.0.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/doowb", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "3d809da0a3e2024761d48651955fd9452e25ec85", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.0.tgz", "integrity": "sha512-oUsVNHRH2pbD2nhJuU6oHgMTpwH/V4ABbzRshMPcIc9GZeYi57sfTrPGGXyLfBreFlGlbvv7aFrQd+GYLwo8yw==", "signatures": [{"sig": "MEUCIQDxUfIisBerizKkTa5SADKwhyszEM9xpZXE6c0bUhF1owIgcp6xX/ckp8i4gi36xqZWUi7r6nK1bWN7gghavF8BDxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}, "reflinks": ["brace-expansion", "to-regex-range", "verb", "verb-generate-readme"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "3d809da0a3e2024761d48651955fd9452e25ec85", "engines": {"node": ">=0.10.0"}, "gitHead": "e6761d8bf6e430ef73713a3a7d7b0115f6183c90", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.2.0", "isobject": "^2.1.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.0.3", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.1.2", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^0.2.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^4.0.2", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.0", "pretty-bytes": "^4.0.2", "yargs-parser": "^4.0.2", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "brace-expansion": "^1.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.0.0.tgz_1476927129221_0.6485608567018062", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.1": {"name": "braces", "version": "2.0.1", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.0.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/doowb", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm", "email": "<EMAIL>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "aef8fb862f7f429d9840a931cbbbbf8355107905", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.1.tgz", "integrity": "sha512-qRHNcprYklsNT+kuUQLUxluiz3eFb7utwCuTBhrlfA4tYFG4cyUSw0vgTs8Z6yFgpssXiQiDHsoeU0wDaW+5Jw==", "signatures": [{"sig": "MEUCID4Q+PwlbsCMVk+Tuir5q6OdH/CVmuLIpopwGcsX++P8AiEA6lbfaZamu62BIk7ZovfkmgG3MZgW0OnxWSBHiZX6mD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}, "reflinks": ["brace-expansion", "to-regex-range", "verb", "verb-generate-readme"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "aef8fb862f7f429d9840a931cbbbbf8355107905", "engines": {"node": ">=0.10.0"}, "gitHead": "2c8155e67b85fe134110d178e4b916f5bd0db034", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.2.0", "isobject": "^2.1.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.0.3", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.1.2", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^0.2.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^4.0.2", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.0", "pretty-bytes": "^4.0.2", "yargs-parser": "^4.0.2", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "brace-expansion": "^1.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.0.1.tgz_1476997523234_0.9440339561551809", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.2": {"name": "braces", "version": "2.0.2", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.0.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/doowb", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm", "email": "<EMAIL>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "d4c9b44820e63a7b79437a4ef094b8ab53a4ae44", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.2.tgz", "integrity": "sha512-WhhjRmZ3ksdEwciX8KbK3E0FjExG4aSetO70d3X4hM/g0elE8XYJNLBq1ov82HTPXEGg0/0DGaSqpvqwzwXyGw==", "signatures": [{"sig": "MEUCIHsG4SCjwwH/U3d1vDFts54s+M0cbp97J5yX1TZcPwxyAiEAvHEFatnqFrFkKqfgxAw/VGZnfnCE6Mf5oLZOEYvmDW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}, "reflinks": ["brace-expansion", "to-regex-range", "verb", "verb-generate-readme"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "d4c9b44820e63a7b79437a4ef094b8ab53a4ae44", "engines": {"node": ">=0.10.0"}, "gitHead": "d2bb3cf243719334f245580c9eed4c4f5b90184c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "6.7.0", "dependencies": {"debug": "^2.2.0", "isobject": "^2.1.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.0.3", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.1.2", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^0.2.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^4.0.2", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.0", "pretty-bytes": "^4.0.2", "yargs-parser": "^4.0.2", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "brace-expansion": "^1.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.0.2.tgz_1477032399842_0.88175986148417", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.3": {"name": "braces", "version": "2.0.3", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.0.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/doowb", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm", "email": "<EMAIL>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "7ac586e593bdc43a262daf45673661464844198f", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.3.tgz", "integrity": "sha512-vp/DHMo3GvQgTvOp5Nvm+Rcd5DAWCYT+vDL9A3ZTm6flVOw0WH8IjcyBleC9H5BrNu2JhEQmyPUYgFHExUUY/w==", "signatures": [{"sig": "MEUCIBrHNTok99w4c6d2y9xPUhsRta0Z8O4D38Ct5JW3dh/5AiEA+zHGbtHmbJ3N+JdiIHtLPkGn8sUbjQ7x2/GvLTbgPus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}, "reflinks": ["brace-expansion", "to-regex-range", "verb", "verb-generate-readme"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "7ac586e593bdc43a262daf45673661464844198f", "engines": {"node": ">=0.10.0"}, "gitHead": "3b9f881ae5d07d1e6d1d66aec61e134731671807", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.0.3", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^5.0.1", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.0", "pretty-bytes": "^4.0.2", "yargs-parser": "^4.2.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.11", "brace-expansion": "^1.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.0.3.tgz_1481418802700_0.12234931578859687", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.4": {"name": "braces", "version": "2.0.4", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.0.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm", "email": "<EMAIL>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/braces", "bugs": {"url": "https://github.com/jonschlinkert/braces/issues"}, "dist": {"shasum": "6381bfe9154fbc2b0b1b420aab574b1ac87534ee", "tarball": "https://registry.npmjs.org/braces/-/braces-2.0.4.tgz", "integrity": "sha512-+fxIrbtFLL6T/b1N86npW5J3WFiGNo6mKBel0tLT6zLDRK5uggXgaPW3T3Bua4JCUTmktzENYjY6P82Dcv40kw==", "signatures": [{"sig": "MEQCIALpwIdl1nf9/9Jsl28FFlQWsZOEXXY9CP2wsrj2/QHWAiA44HRDhvZm3tc69EN/y8GW11M2HkDf5GjQhsC5ETDfVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}, "reflinks": ["multimatch", "to-regex-range"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "6381bfe9154fbc2b0b1b420aab574b1ac87534ee", "engines": {"node": ">=0.10.0"}, "gitHead": "1e18cfad8e1fba642b80737d0987dbe988b30b57", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/braces.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "regex-not": "^1.0.0", "fill-range": "^3.1.1", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.1", "array-unique": "^0.3.2", "split-string": "^2.0.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^0.2.5", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.0.4.tgz_1491959561858_0.7802031957544386", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.0": {"name": "braces", "version": "2.1.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm", "email": "<EMAIL>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "27dc86dfb1add13b2974fc4276e01941a246e14a", "tarball": "https://registry.npmjs.org/braces/-/braces-2.1.0.tgz", "integrity": "sha512-qrKq9EBrp6asnvy70BJGJzjtIoirGrpteVrEQwWkzxFFbVy6z7vgijSXsI2z3mcjsCnjrJE4huxvlIcyQWBlVw==", "signatures": [{"sig": "MEUCIBPA5mxXXONzyGbNeV2zSWtHNJTlyCF9DskHDlcaOfXqAiEA5KgFuWAc6M5i4TZbDWyceenEy8QVJQzFIPgPVP4l0OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}, "reflinks": ["multimatch", "to-regex-range"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "27dc86dfb1add13b2974fc4276e01941a246e14a", "engines": {"node": ">=0.10.0"}, "gitHead": "3bfb5df3f4cf18cf098f22f0005b365d96a7ca62", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.0.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.1.0.tgz_1493246364083_0.3190110339783132", "host": "packages-18-east.internal.npmjs.com"}}, "2.1.1": {"name": "braces", "version": "2.1.1", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.1.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm", "email": "<EMAIL>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "b33581be8553a651fdc79012760a7e767f82b834", "tarball": "https://registry.npmjs.org/braces/-/braces-2.1.1.tgz", "integrity": "sha512-hUsQnnSix9sYcu2hgn/CwF7LR5YP+ICow571lAORgz3R++N0zA51MV5z41skZdE1XsEU9+4eHUro/K8/SVLh2A==", "signatures": [{"sig": "MEUCIQD9/Owdr7T9KbKxBHv0fnvkY+G9qIa+WyQYL1mDyHzgLgIgIyCZreoctCMLnv87jl2B8IhfoV38OmhudkRVTvYU//U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}, "reflinks": ["multimatch", "to-regex-range"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "b33581be8553a651fdc79012760a7e767f82b834", "engines": {"node": ">=0.10.0"}, "gitHead": "9a2ca2624823578aa68b624ffe71e089da66261d", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Fast, comprehensive, bash-like brace expansion implemented in JavaScript. Complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.1.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "fs-exists-sync": "^0.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.1.1.tgz_1493280609200_0.7220009674783796", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.0": {"name": "braces", "version": "2.2.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "9edd1d6ee0acd760f1d1e5c13bc66d428ad0a3c0", "tarball": "https://registry.npmjs.org/braces/-/braces-2.2.0.tgz", "integrity": "sha512-RZWGwDQ3cdGQTqajHnk4KXjmTdpdwzlotZ1nZa9GCoyG/fkzyjuMvDXGlkgzNDSvcxGfApZmIwVtpXUzht6vLA==", "signatures": [{"sig": "MEYCIQDL/PnMphK2sHIdNRkP3ikXsL9fRLYAB7nC8Vgl8UtrkAIhALh7CYG1iEUQtDMItshXr1LX0xQdLf3VWONaHVc+exd0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "9edd1d6ee0acd760f1d1e5c13bc66d428ad0a3c0", "engines": {"node": ">=0.10.0"}, "gitHead": "6acf24280dbdeff24ea0b2d76fef3314ee2869dd", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.1.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.2.0.tgz_1496014212571_0.4040629423689097", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "braces", "version": "2.2.1", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.2.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "cccce9e092a93faa0bba2c1174cd262d2369794d", "tarball": "https://registry.npmjs.org/braces/-/braces-2.2.1.tgz", "integrity": "sha512-saw1qqYe6p9B1zaZJco2fuIk1xl+NvrvXg29DYjUEv4S1FvSFZFzjHMtfDFbKkXHKKZsQDLBgASKRaz5mnV2nw==", "signatures": [{"sig": "MEQCIA+pYRljfNCuSindKQX68ZhW1zLoYJ9EVWmpp/dd7AZ8AiBBeY5kU/JjY3uoOMvAf516KfL0va8D71A4m1fHl8fRVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "cccce9e092a93faa0bba2c1174cd262d2369794d", "engines": {"node": ">=0.10.0"}, "gitHead": "13f2def55bc873764c9638eaf8ded1646f48fc17", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.1.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.2.1.tgz_1496193670172_0.42343820934183896", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "braces", "version": "2.2.2", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.2.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "241f868c2b2690d9febeee5a7c83fbbf25d00b1b", "tarball": "https://registry.npmjs.org/braces/-/braces-2.2.2.tgz", "integrity": "sha512-LsV+Z9xUYWR9XX0fnDceeu4+X+RCZ18P8YHJlgSUEzRjsOFfMl1gjzIg7vfbW6cJg8mfJIuoWpVbgAkimkAWAA==", "signatures": [{"sig": "MEUCICdbpOu8hITnf0KOknAb8ZxnfWySKMPgm8zHfmwcFaTnAiEA2kBUKns5XT+Cmf1umeVBAhgxYDPBnEyqT4IiIOX88ik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "241f868c2b2690d9febeee5a7c83fbbf25d00b1b", "engines": {"node": ">=0.10.0"}, "gitHead": "f888915cb261fc633a2a221b33b45b80ed5e0bb8", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"isobject": "^3.0.0", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.0.3", "array-unique": "^0.3.2", "split-string": "^2.1.0", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.3", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.0", "text-table": "^0.2.0", "benchmarked": "^1.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^3.0.1", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^5.0.0", "gulp-istanbul": "^1.1.1", "noncharacters": "^1.1.0", "gulp-format-md": "^0.1.12", "brace-expansion": "^1.1.7"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.2.2.tgz_1496224590043_0.89456413150765", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "braces", "version": "2.3.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.3.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "a46941cb5fb492156b3d6a656e06c35364e3e66e", "tarball": "https://registry.npmjs.org/braces/-/braces-2.3.0.tgz", "integrity": "sha512-P4O8UQRdGiMLWSizsApmXVQDBS6KCt7dSexgLKBmH5Hr1CZq7vsnscFh8oR1sP1ab1Zj0uCHCEzZeV6SfUf3rA==", "signatures": [{"sig": "MEYCIQCElhIYU0FtxiFWvH1X8pU0dIJgLTkBzB5lxzEIrqL26AIhAKq5gmsttltOw8L9sHpxi3uymMPwtrx+iP6EOoO45LBm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "d442bde36a17c2ed7a982866d85073338fda8c85", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"isobject": "^3.0.1", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "split-string": "^3.0.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.4", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "text-table": "^0.2.0", "benchmarked": "^2.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^4.0.0", "gulp-unused": "^0.2.1", "pretty-bytes": "^4.0.2", "yargs-parser": "^8.0.0", "gulp-istanbul": "^1.1.2", "noncharacters": "^1.1.0", "gulp-format-md": "^1.0.0", "brace-expansion": "^1.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/braces-2.3.0.tgz_1508420228531_0.9479703973047435", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "braces", "version": "2.3.1", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.3.1", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "7086c913b4e5a08dbe37ac0ee6a2500c4ba691bb", "tarball": "https://registry.npmjs.org/braces/-/braces-2.3.1.tgz", "fileCount": 12, "integrity": "sha512-SO5lYHA3vO6gz66erVvedSCkp7AKWdv6VcQ2N4ysXfPxdAlxAMMAdwegGGcv1Bqwm7naF1hNdk5d6AAIEHV2nQ==", "signatures": [{"sig": "MEYCIQC3TrmkxatUqP/YQ8Xdctd63DNG0KvHv/WU5tjqlXHJEwIhAJ/oVFHtrRi0CF/wAyHNCw8J0A9gtQnHWAv57REQLlJu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94768}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "b37f6972503348af5ecec46ff65a3a9d12d21274", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"kind-of": "^6.0.2", "isobject": "^3.0.1", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "split-string": "^3.0.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "define-property": "^1.0.0", "snapdragon-node": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.4", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "text-table": "^0.2.0", "benchmarked": "^2.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^4.0.0", "gulp-unused": "^0.2.1", "yargs-parser": "^8.0.0", "gulp-istanbul": "^1.1.2", "noncharacters": "^1.1.0", "gulp-format-md": "^1.0.0", "brace-expansion": "^1.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/braces_2.3.1_1518927409826_0.33240580605170256", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "braces", "version": "2.3.2", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@2.3.2", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "5979fd3f14cd531565e5fa2df1abfff1dfaee729", "tarball": "https://registry.npmjs.org/braces/-/braces-2.3.2.tgz", "fileCount": 8, "integrity": "sha512-aNdbnj9P8PjdXU4ybaWLK2IF3jc/EoDYbC7AazW6to3TRsfXxscC9UXOB5iDiEQrkyIbWp2SLQda4+QAa7nc3w==", "signatures": [{"sig": "MEUCIQCYZtepWok2C8goON8SfWxHt1DfjzDxpj/PbdbbMbUYBAIgJ+xU01i1GI7aH1A3DoH1eDubI2rkP43XWZa9fLtVmNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59699}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["expand-brackets", "extglob", "fill-range", "micromatch", "nanomatch"]}}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "8a3edbb31955881ae87ba540b9f86eb390e5c4bd", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "5.8.0", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "9.9.0", "dependencies": {"isobject": "^3.0.1", "to-regex": "^3.0.1", "fill-range": "^4.0.0", "snapdragon": "^0.8.1", "arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "split-string": "^3.0.2", "extend-shallow": "^2.0.1", "repeat-element": "^1.1.2", "snapdragon-node": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "ansi-cyan": "^0.1.1", "minimatch": "^3.0.4", "time-diff": "^0.3.1", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "text-table": "^0.2.0", "benchmarked": "^2.0.0", "cross-spawn": "^5.1.0", "gulp-eslint": "^4.0.0", "gulp-unused": "^0.2.1", "yargs-parser": "^8.0.0", "gulp-istanbul": "^1.1.2", "noncharacters": "^1.1.0", "gulp-format-md": "^1.0.0", "brace-expansion": "^1.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/braces_2.3.2_1523197311772_0.8796414074016354", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "braces", "version": "3.0.0", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@3.0.0", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "1d9a1a84daea4606d3a6a03d1fd97cc6bd37d543", "tarball": "https://registry.npmjs.org/braces/-/braces-3.0.0.tgz", "fileCount": 12, "integrity": "sha512-JrED+3ZoiTW3KmWkrajE5zm5Pl69XD1DjItKTX9KS+dsfrge66nho5fAOC+tBwMH5p5bPv97EYm8loTb7mxPKg==", "signatures": [{"sig": "MEQCIHl0v+gsmlp96abkAFFO7qd+hGWtiD+zTmrqGNDGhOFKAiBDTo73OfMndcELEAXDM1WPVLNGT8M3u0tXgAR9peqiHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcq89rCRA9TVsSAnZWagAAY1oP/Ak0JTSaZUQv/2xXrF1Z\nD4zJ0bXdRNtkOIRwO1wqrl5Jn6LKc0lQmhfAanVxFE7LhuCFvS0f3bi/yMBY\nTkKlnMy376/Z5VIpxqwtOprvx0UG2k1/1LTgfLWt1sLlC1exAxK1HDnUUHhi\nGCiiPlAKvhB+ygp5334F2Mcwp9keW2yIMjH+7vZTxVToBwJYv3lHvcc3U8c0\nqifRFQTLK3vyoKTXCkP0EhmzbPBtcs5PFsi6b5qv5N8/3uHphqMg8YFhfpcr\nFtwh+NWLFdLEYIpFa2dHbIzISiRFFIkFIqTmPg7P89IPHXQAQL6xGi50VedN\nOvWxVQZyV1yGE/xVJgfR66FSZUGvL61ScW5zJ2noBqjsKwjv0XJoQL+aWDH0\ny0HgWdTQ0uylZjXqoL9h7Y8+jMfUO4DyukdqiqmPJmqziA4wJcyK8XKjfqZ/\nH88ej3z9TitURBb5ZJpGGEuMKkGSY2My03fq5aac//428dZ0BsXKBe2G5j/b\nzIZ6gNUVLWC6EpztEEC4S90Q3H/uoDan4i/rpw9rM9buPw4tUyqL2tU0YuBo\nu1/aDTuOOh+BHp8MAVzn/EmsfivhEh9uBZhrF+yxKMlSWanUkPmytMUcIoPq\nE/SAO3Ho2LSd3b+vZvi0ZcX5R1bLUdcx29wSxtux8QjbCHLAVifz+1S4Y2IJ\nquHV\r\n=Z/eb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"]}, "engines": {"node": ">=8"}, "gitHead": "abcf3418f13b1082fccf65fa2f4a5afb9432f295", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"fill-range": "^7.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.1", "bash-path": "^2.0.1", "ansi-colors": "^3.2.4", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/braces_3.0.0_1554763626998_0.08917727299226375", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "braces", "version": "3.0.1", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@3.0.1", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "dd8f330ba1c895e39de73ec33e99275443ff0fed", "tarball": "https://registry.npmjs.org/braces/-/braces-3.0.1.tgz", "fileCount": 12, "integrity": "sha512-Vmyh3JAr5DRUKCdRrC+WyAAsWBez8HLnBmVb6Ux2VYbvC8DjqMC228WHx24fiQG5BiDOVo+otK1scdkK5S6YNg==", "signatures": [{"sig": "MEUCIQCd3BZh7HjN+utxxh8q58KmnQIGBV386u8+wMZqkpwULwIgJw5ekWt4cyzV93JOp8IHlTgJM63DFCuZDVh11jqIotE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55387, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrdPFCRA9TVsSAnZWagAA9ZsP/1rIkCKn2vfIdy1HvAGt\nY532MHHBmRKiy5UhFTnR9xvYpQYMOFgiXnuClYJBh0j/u3CtzgDkP+T/zDjw\nteMLJxcat86yNrt42ZBHRBjRPHjRy7ALyk/ocgC2M5bXENaWKVRGAq15vIXU\nQM3kqjAvN78Ea7y9DKA1yesI+1xLTVjBZQ1iAFFkRzYZPhKMX15xRHMnMKPE\n+9ZQCQjUBj0SEimbxV87UeTorj2RSGuhjkNurLAMvvUyQ5thPGGBiGt7JKSF\niR81fJBXijrOtzNle5sENmP3OkBLN5JEhksGwBUxS1h/XJhr+FBOiaI/64Wk\niZbzg0EtoaJ509oOzy1laWL9YAo+kiPKKVVeCYD+nWpNLxumWNq9hrOSgtM3\nRc/C9Q/91B03IWeaYUZFXaLW7zh+VSKAqFl0V1L5O7O/e1OrDg88f8FBzdQA\nnijtV51AuO9pTcvtHNlhKwYwV6+wYO08xMEn0KUCTiPNugrdvsvHPlRfk+hd\nWf9Igdp6pq1PYYtVjTPcCev3bRzKNo/3p755k/4sqgpfMX3QSQ/zOanED3QO\nfvaDEBerTLtfzFhTZfK/wySUHzb4hTCO1qcOeXj7qbDnVJvVUiEwZnKsMQlm\nWM7os3QUzYuIRtCzemsxcgYgWN7TB3SkwABGlUKmot9xWrXxSmvJw0GP+LPt\nuuaR\r\n=QRl/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"]}, "engines": {"node": ">=8"}, "gitHead": "3c51342023661b8d07444509c32ce294b6cb43e7", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"fill-range": "^7.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.1", "bash-path": "^2.0.1", "ansi-colors": "^3.2.4", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/braces_3.0.1_1554895812543_0.4256142650947923", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "braces", "version": "3.0.2", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@3.0.2", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "3454e1a462ee8d599e236df336cd9ea4f8afe107", "tarball": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "fileCount": 11, "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "signatures": [{"sig": "MEYCIQDx963+PwpwtRhYsYrHDTB5Dz59LG71waTrE16auk36hgIhAJ2pBgwmBsPPT+35dD0W86nmV5KBEScDs2J8+olS4QX1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49239, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctjIxCRA9TVsSAnZWagAAwA4P/i6j6DyxGZfojgDNzaNX\n6GlgCFb4mF64au4BAi+9q2gJTJOVbr2P/0iEl9tjdM5LU+tqEv1eLKkHenBi\ndc+ktaVD/c+XaTMubGNhtBfDxQUiU/QqI6dLMCEOb5EOlSclHsTiLV3frQxI\nEUzh9+LL3V6okecvADaWgt17xTWXSDRktZJoFeAK5q0017ras8yWLqp+5uDO\nRpapuec1wK4RRUDUS9/I+K9R7Kxv9aTyfdGQ5sueLj1QdAmNKDAVH77OG9Rl\nACDijw5L1KpzRkGxGkrwUHyjh0V7EMDwj74XgddEEE3eUgn5UN5n+Dmyj0/H\nP5tcGxOqNEpj9UueU8m6A18URJatSzDwqhAEKbJ1jyYs3LyElDUdiOxYedib\nHz8Ny9+5TJqEeEhU0de/8bsafjV4QsC/JZLI8nc9W65HVQ8M1fAgvxQ6O7Uj\naoZiqhME6OaE43tR4ldNnnYVujQInyOnTocL3GkbxNWvuQQieLNvBsV0p1H4\n6A9Q4y0fzsfdH+NoEJ3ghgSMn2P5Bb9EuExg0McW//H1Gj5+V2nfXEFGXoQE\n874CBL5gIxyAGSGiWB57y6bGCMWvo6gKhFdeEF+msvMSiv18hw4CcgogVzy/\n1pWI+R9hSWbaWD6q/Em9HmIJDqNS633NWy+KMMrEQLGZjfrEa3VsRjiXGCaH\nUQTq\r\n=kTks\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"]}, "engines": {"node": ">=8"}, "gitHead": "25791512d219b284bd62bb068cae85d8e68bd05b", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "10.15.2", "dependencies": {"fill-range": "^7.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.1", "bash-path": "^2.0.1", "ansi-colors": "^3.2.4", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/braces_3.0.2_1555444272661_0.9578113107001449", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "braces", "version": "3.0.3", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "braces@3.0.3", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}], "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/braces", "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "dist": {"shasum": "490332f40919452272d55a8480adc0c441358789", "tarball": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "fileCount": 10, "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "signatures": [{"sig": "MEQCICfWLKNcFCGtU07ikrPJ+s1dTDH3VZTE+vQZzC22s6o+AiAEVUE/YXRqHYgctcwHqNawvsOviSDFZPvgNm8+SUXD3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44635}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"]}, "engines": {"node": ">=8"}, "gitHead": "74b2db2938fad48a2ea54a9c8bf27a37a62c350d", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "directories": {}, "_nodeVersion": "20.12.1", "dependencies": {"fill-range": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.1.1", "bash-path": "^2.0.1", "ansi-colors": "^3.2.4", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/braces_3.0.3_1716281951275_0.19583852040362504", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-10-20T16:26:32.656Z", "modified": "2024-09-18T05:27:12.449Z", "0.1.0": "2014-10-20T16:26:32.656Z", "0.1.1": "2014-10-22T18:19:54.228Z", "0.1.2": "2014-10-22T18:51:58.044Z", "0.1.3": "2014-11-15T03:23:28.705Z", "0.1.4": "2014-11-15T03:24:22.095Z", "0.1.5": "2014-11-24T02:08:00.293Z", "1.0.0": "2014-12-23T11:35:57.637Z", "1.1.0": "2015-01-12T00:45:29.358Z", "1.2.0": "2015-01-16T11:55:08.268Z", "1.3.0": "2015-01-24T11:32:14.592Z", "1.4.0": "2015-01-25T00:33:05.711Z", "1.5.0": "2015-01-28T18:08:49.158Z", "1.5.1": "2015-01-30T13:27:20.249Z", "1.6.0": "2015-01-30T13:35:38.758Z", "1.7.0": "2015-01-31T00:24:25.055Z", "1.8.0": "2015-03-18T11:37:22.409Z", "1.8.1": "2015-08-21T10:17:04.643Z", "1.8.2": "2015-10-19T17:46:05.744Z", "1.8.3": "2015-12-19T23:03:30.884Z", "1.8.4": "2016-04-20T08:24:45.636Z", "1.8.5": "2016-05-21T15:13:02.602Z", "2.0.0": "2016-10-20T01:32:09.939Z", "2.0.1": "2016-10-20T21:05:23.946Z", "2.0.2": "2016-10-21T06:46:42.002Z", "2.0.3": "2016-12-11T01:13:25.003Z", "2.0.4": "2017-04-12T01:12:43.808Z", "2.1.0": "2017-04-26T22:39:25.950Z", "2.1.1": "2017-04-27T08:10:11.271Z", "2.2.0": "2017-05-28T23:30:13.765Z", "2.2.1": "2017-05-31T01:21:11.257Z", "2.2.2": "2017-05-31T09:56:31.159Z", "2.3.0": "2017-10-19T13:37:09.763Z", "2.3.1": "2018-02-18T04:16:49.881Z", "2.3.2": "2018-04-08T14:21:51.992Z", "3.0.0": "2019-04-08T22:47:07.141Z", "3.0.1": "2019-04-10T11:30:12.815Z", "3.0.2": "2019-04-16T19:51:12.829Z", "3.0.3": "2024-05-21T08:59:11.390Z"}, "bugs": {"url": "https://github.com/micromatch/braces/issues"}, "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/micromatch/braces", "keywords": ["alpha", "alphabetical", "bash", "brace", "braces", "expand", "expansion", "filepath", "fill", "fs", "glob", "globbing", "letter", "match", "matches", "matching", "number", "numerical", "path", "range", "ranges", "sh"], "repository": {"url": "git+https://github.com/micromatch/braces.git", "type": "git"}, "description": "Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.", "contributors": [{"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/eush77", "name": "<PERSON>"}, {"url": "http://h3manth.com", "name": "hemanth.hm"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}], "maintainers": [{"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "doowb"}], "readme": "# braces [![Donate](https://img.shields.io/badge/Donate-PayPal-green.svg)](https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=W8YFZ425KND68) [![NPM version](https://img.shields.io/npm/v/braces.svg?style=flat)](https://www.npmjs.com/package/braces) [![NPM monthly downloads](https://img.shields.io/npm/dm/braces.svg?style=flat)](https://npmjs.org/package/braces) [![NPM total downloads](https://img.shields.io/npm/dt/braces.svg?style=flat)](https://npmjs.org/package/braces) [![Linux Build Status](https://img.shields.io/travis/micromatch/braces.svg?style=flat&label=Travis)](https://travis-ci.org/micromatch/braces)\n\n> Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save braces\n```\n\n## v3.0.0 Released!!\n\nSee the [changelog](CHANGELOG.md) for details.\n\n## Why use braces?\n\nBrace patterns make globs more powerful by adding the ability to match specific ranges and sequences of characters.\n\n- **Accurate** - complete support for the [Bash 4.3 Brace Expansion](www.gnu.org/software/bash/) specification (passes all of the Bash braces tests)\n- **[fast and performant](#benchmarks)** - Starts fast, runs fast and [scales well](#performance) as patterns increase in complexity.\n- **Organized code base** - The parser and compiler are easy to maintain and update when edge cases crop up.\n- **Well-tested** - Thousands of test assertions, and passes all of the Bash, minimatch, and [brace-expansion](https://github.com/juliangruber/brace-expansion) unit tests (as of the date this was written).\n- **Safer** - You shouldn't have to worry about users defining aggressive or malicious brace patterns that can break your application. Braces takes measures to prevent malicious regex that can be used for DDoS attacks (see [catastrophic backtracking](https://www.regular-expressions.info/catastrophic.html)).\n- [Supports lists](#lists) - (aka \"sets\") `a/{b,c}/d` => `['a/b/d', 'a/c/d']`\n- [Supports sequences](#sequences) - (aka \"ranges\") `{01..03}` => `['01', '02', '03']`\n- [Supports steps](#steps) - (aka \"increments\") `{2..10..2}` => `['2', '4', '6', '8', '10']`\n- [Supports escaping](#escaping) - To prevent evaluation of special characters.\n\n## Usage\n\nThe main export is a function that takes one or more brace `patterns` and `options`.\n\n```js\nconst braces = require('braces');\n// braces(patterns[, options]);\n\nconsole.log(braces(['{01..05}', '{a..e}']));\n//=> ['(0[1-5])', '([a-e])']\n\nconsole.log(braces(['{01..05}', '{a..e}'], { expand: true }));\n//=> ['01', '02', '03', '04', '05', 'a', 'b', 'c', 'd', 'e']\n```\n\n### Brace Expansion vs. Compilation\n\nBy default, brace patterns are compiled into strings that are optimized for creating regular expressions and matching.\n\n**Compiled**\n\n```js\nconsole.log(braces('a/{x,y,z}/b'));\n//=> ['a/(x|y|z)/b']\nconsole.log(braces(['a/{01..20}/b', 'a/{1..5}/b']));\n//=> [ 'a/(0[1-9]|1[0-9]|20)/b', 'a/([1-5])/b' ]\n```\n\n**Expanded**\n\nEnable brace expansion by setting the `expand` option to true, or by using [braces.expand()](#expand) (returns an array similar to what you'd expect from Bash, or `echo {1..5}`, or [minimatch](https://github.com/isaacs/minimatch)):\n\n```js\nconsole.log(braces('a/{x,y,z}/b', { expand: true }));\n//=> ['a/x/b', 'a/y/b', 'a/z/b']\n\nconsole.log(braces.expand('{01..10}'));\n//=> ['01','02','03','04','05','06','07','08','09','10']\n```\n\n### Lists\n\nExpand lists (like Bash \"sets\"):\n\n```js\nconsole.log(braces('a/{foo,bar,baz}/*.js'));\n//=> ['a/(foo|bar|baz)/*.js']\n\nconsole.log(braces.expand('a/{foo,bar,baz}/*.js'));\n//=> ['a/foo/*.js', 'a/bar/*.js', 'a/baz/*.js']\n```\n\n### Sequences\n\nExpand ranges of characters (like Bash \"sequences\"):\n\n```js\nconsole.log(braces.expand('{1..3}')); // ['1', '2', '3']\nconsole.log(braces.expand('a/{1..3}/b')); // ['a/1/b', 'a/2/b', 'a/3/b']\nconsole.log(braces('{a..c}', { expand: true })); // ['a', 'b', 'c']\nconsole.log(braces('foo/{a..c}', { expand: true })); // ['foo/a', 'foo/b', 'foo/c']\n\n// supports zero-padded ranges\nconsole.log(braces('a/{01..03}/b')); //=> ['a/(0[1-3])/b']\nconsole.log(braces('a/{001..300}/b')); //=> ['a/(0{2}[1-9]|0[1-9][0-9]|[12][0-9]{2}|300)/b']\n```\n\nSee [fill-range](https://github.com/jonschlinkert/fill-range) for all available range-expansion options.\n\n### Steppped ranges\n\nSteps, or increments, may be used with ranges:\n\n```js\nconsole.log(braces.expand('{2..10..2}'));\n//=> ['2', '4', '6', '8', '10']\n\nconsole.log(braces('{2..10..2}'));\n//=> ['(2|4|6|8|10)']\n```\n\nWhen the [.optimize](#optimize) method is used, or [options.optimize](#optionsoptimize) is set to true, sequences are passed to [to-regex-range](https://github.com/jonschlinkert/to-regex-range) for expansion.\n\n### Nesting\n\nBrace patterns may be nested. The results of each expanded string are not sorted, and left to right order is preserved.\n\n**\"Expanded\" braces**\n\n```js\nconsole.log(braces.expand('a{b,c,/{x,y}}/e'));\n//=> ['ab/e', 'ac/e', 'a/x/e', 'a/y/e']\n\nconsole.log(braces.expand('a/{x,{1..5},y}/c'));\n//=> ['a/x/c', 'a/1/c', 'a/2/c', 'a/3/c', 'a/4/c', 'a/5/c', 'a/y/c']\n```\n\n**\"Optimized\" braces**\n\n```js\nconsole.log(braces('a{b,c,/{x,y}}/e'));\n//=> ['a(b|c|/(x|y))/e']\n\nconsole.log(braces('a/{x,{1..5},y}/c'));\n//=> ['a/(x|([1-5])|y)/c']\n```\n\n### Escaping\n\n**Escaping braces**\n\nA brace pattern will not be expanded or evaluted if _either the opening or closing brace is escaped_:\n\n```js\nconsole.log(braces.expand('a\\\\{d,c,b}e'));\n//=> ['a{d,c,b}e']\n\nconsole.log(braces.expand('a{d,c,b\\\\}e'));\n//=> ['a{d,c,b}e']\n```\n\n**Escaping commas**\n\nCommas inside braces may also be escaped:\n\n```js\nconsole.log(braces.expand('a{b\\\\,c}d'));\n//=> ['a{b,c}d']\n\nconsole.log(braces.expand('a{d\\\\,c,b}e'));\n//=> ['ad,ce', 'abe']\n```\n\n**Single items**\n\nFollowing bash conventions, a brace pattern is also not expanded when it contains a single character:\n\n```js\nconsole.log(braces.expand('a{b}c'));\n//=> ['a{b}c']\n```\n\n## Options\n\n### options.maxLength\n\n**Type**: `Number`\n\n**Default**: `10,000`\n\n**Description**: Limit the length of the input string. Useful when the input string is generated or your application allows users to pass a string, et cetera.\n\n```js\nconsole.log(braces('a/{b,c}/d', { maxLength: 3 })); //=> throws an error\n```\n\n### options.expand\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: Generate an \"expanded\" brace pattern (alternatively you can use the `braces.expand()` method, which does the same thing).\n\n```js\nconsole.log(braces('a/{b,c}/d', { expand: true }));\n//=> [ 'a/b/d', 'a/c/d' ]\n```\n\n### options.nodupes\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: Remove duplicates from the returned array.\n\n### options.rangeLimit\n\n**Type**: `Number`\n\n**Default**: `1000`\n\n**Description**: To prevent malicious patterns from being passed by users, an error is thrown when `braces.expand()` is used or `options.expand` is true and the generated range will exceed the `rangeLimit`.\n\nYou can customize `options.rangeLimit` or set it to `Inifinity` to disable this altogether.\n\n**Examples**\n\n```js\n// pattern exceeds the \"rangeLimit\", so it's optimized automatically\nconsole.log(braces.expand('{1..1000}'));\n//=> ['([1-9]|[1-9][0-9]{1,2}|1000)']\n\n// pattern does not exceed \"rangeLimit\", so it's NOT optimized\nconsole.log(braces.expand('{1..100}'));\n//=> ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99', '100']\n```\n\n### options.transform\n\n**Type**: `Function`\n\n**Default**: `undefined`\n\n**Description**: Customize range expansion.\n\n**Example: Transforming non-numeric values**\n\n```js\nconst alpha = braces.expand('x/{a..e}/y', {\n  transform(value, index) {\n    // When non-numeric values are passed, \"value\" is a character code.\n    return 'foo/' + String.fromCharCode(value) + '-' + index;\n  },\n});\nconsole.log(alpha);\n//=> [ 'x/foo/a-0/y', 'x/foo/b-1/y', 'x/foo/c-2/y', 'x/foo/d-3/y', 'x/foo/e-4/y' ]\n```\n\n**Example: Transforming numeric values**\n\n```js\nconst numeric = braces.expand('{1..5}', {\n  transform(value) {\n    // when numeric values are passed, \"value\" is a number\n    return 'foo/' + value * 2;\n  },\n});\nconsole.log(numeric);\n//=> [ 'foo/2', 'foo/4', 'foo/6', 'foo/8', 'foo/10' ]\n```\n\n### options.quantifiers\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: In regular expressions, quanitifiers can be used to specify how many times a token can be repeated. For example, `a{1,3}` will match the letter `a` one to three times.\n\nUnfortunately, regex quantifiers happen to share the same syntax as [Bash lists](#lists)\n\nThe `quantifiers` option tells braces to detect when [regex quantifiers](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#quantifiers) are defined in the given pattern, and not to try to expand them as lists.\n\n**Examples**\n\n```js\nconst braces = require('braces');\nconsole.log(braces('a/b{1,3}/{x,y,z}'));\n//=> [ 'a/b(1|3)/(x|y|z)' ]\nconsole.log(braces('a/b{1,3}/{x,y,z}', { quantifiers: true }));\n//=> [ 'a/b{1,3}/(x|y|z)' ]\nconsole.log(braces('a/b{1,3}/{x,y,z}', { quantifiers: true, expand: true }));\n//=> [ 'a/b{1,3}/x', 'a/b{1,3}/y', 'a/b{1,3}/z' ]\n```\n\n### options.keepEscaping\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Description**: Do not strip backslashes that were used for escaping from the result.\n\n## What is \"brace expansion\"?\n\nBrace expansion is a type of parameter expansion that was made popular by unix shells for generating lists of strings, as well as regex-like matching when used alongside wildcards (globs).\n\nIn addition to \"expansion\", braces are also used for matching. In other words:\n\n- [brace expansion](#brace-expansion) is for generating new lists\n- [brace matching](#brace-matching) is for filtering existing lists\n\n<details>\n<summary><strong>More about brace expansion</strong> (click to expand)</summary>\n\nThere are two main types of brace expansion:\n\n1. **lists**: which are defined using comma-separated values inside curly braces: `{a,b,c}`\n2. **sequences**: which are defined using a starting value and an ending value, separated by two dots: `a{1..3}b`. Optionally, a third argument may be passed to define a \"step\" or increment to use: `a{1..100..10}b`. These are also sometimes referred to as \"ranges\".\n\nHere are some example brace patterns to illustrate how they work:\n\n**Sets**\n\n```\n{a,b,c}       => a b c\n{a,b,c}{1,2}  => a1 a2 b1 b2 c1 c2\n```\n\n**Sequences**\n\n```\n{1..9}        => 1 2 3 4 5 6 7 8 9\n{4..-4}       => 4 3 2 1 0 -1 -2 -3 -4\n{1..20..3}    => 1 4 7 10 13 16 19\n{a..j}        => a b c d e f g h i j\n{j..a}        => j i h g f e d c b a\n{a..z..3}     => a d g j m p s v y\n```\n\n**Combination**\n\nSets and sequences can be mixed together or used along with any other strings.\n\n```\n{a,b,c}{1..3}   => a1 a2 a3 b1 b2 b3 c1 c2 c3\nfoo/{a,b,c}/bar => foo/a/bar foo/b/bar foo/c/bar\n```\n\nThe fact that braces can be \"expanded\" from relatively simple patterns makes them ideal for quickly generating test fixtures, file paths, and similar use cases.\n\n## Brace matching\n\nIn addition to _expansion_, brace patterns are also useful for performing regular-expression-like matching.\n\nFor example, the pattern `foo/{1..3}/bar` would match any of following strings:\n\n```\nfoo/1/bar\nfoo/2/bar\nfoo/3/bar\n```\n\nBut not:\n\n```\nbaz/1/qux\nbaz/2/qux\nbaz/3/qux\n```\n\nBraces can also be combined with [glob patterns](https://github.com/jonschlinkert/micromatch) to perform more advanced wildcard matching. For example, the pattern `*/{1..3}/*` would match any of following strings:\n\n```\nfoo/1/bar\nfoo/2/bar\nfoo/3/bar\nbaz/1/qux\nbaz/2/qux\nbaz/3/qux\n```\n\n## Brace matching pitfalls\n\nAlthough brace patterns offer a user-friendly way of matching ranges or sets of strings, there are also some major disadvantages and potential risks you should be aware of.\n\n### tldr\n\n**\"brace bombs\"**\n\n- brace expansion can eat up a huge amount of processing resources\n- as brace patterns increase _linearly in size_, the system resources required to expand the pattern increase exponentially\n- users can accidentally (or intentially) exhaust your system's resources resulting in the equivalent of a DoS attack (bonus: no programming knowledge is required!)\n\nFor a more detailed explanation with examples, see the [geometric complexity](#geometric-complexity) section.\n\n### The solution\n\nJump to the [performance section](#performance) to see how Braces solves this problem in comparison to other libraries.\n\n### Geometric complexity\n\nAt minimum, brace patterns with sets limited to two elements have quadradic or `O(n^2)` complexity. But the complexity of the algorithm increases exponentially as the number of sets, _and elements per set_, increases, which is `O(n^c)`.\n\nFor example, the following sets demonstrate quadratic (`O(n^2)`) complexity:\n\n```\n{1,2}{3,4}      => (2X2)    => 13 14 23 24\n{1,2}{3,4}{5,6} => (2X2X2)  => 135 136 145 146 235 236 245 246\n```\n\nBut add an element to a set, and we get a n-fold Cartesian product with `O(n^c)` complexity:\n\n```\n{1,2,3}{4,5,6}{7,8,9} => (3X3X3) => 147 148 149 157 158 159 167 168 169 247 248\n                                    249 257 258 259 267 268 269 347 348 349 357\n                                    358 359 367 368 369\n```\n\nNow, imagine how this complexity grows given that each element is a n-tuple:\n\n```\n{1..100}{1..100}         => (100X100)     => 10,000 elements (38.4 kB)\n{1..100}{1..100}{1..100} => (100X100X100) => 1,000,000 elements (5.76 MB)\n```\n\nAlthough these examples are clearly contrived, they demonstrate how brace patterns can quickly grow out of control.\n\n**More information**\n\nInterested in learning more about brace expansion?\n\n- [linuxjournal/bash-brace-expansion](http://www.linuxjournal.com/content/bash-brace-expansion)\n- [rosettacode/Brace_expansion](https://rosettacode.org/wiki/Brace_expansion)\n- [cartesian product](https://en.wikipedia.org/wiki/Cartesian_product)\n\n</details>\n\n## Performance\n\nBraces is not only screaming fast, it's also more accurate the other brace expansion libraries.\n\n### Better algorithms\n\nFortunately there is a solution to the [\"brace bomb\" problem](#brace-matching-pitfalls): _don't expand brace patterns into an array when they're used for matching_.\n\nInstead, convert the pattern into an optimized regular expression. This is easier said than done, and braces is the only library that does this currently.\n\n**The proof is in the numbers**\n\nMinimatch gets exponentially slower as patterns increase in complexity, braces does not. The following results were generated using `braces()` and `minimatch.braceExpand()`, respectively.\n\n| **Pattern**                 | **braces**          | **[minimatch][]**            |\n| --------------------------- | ------------------- | ---------------------------- |\n| `{1..9007199254740991}`[^1] | `298 B` (5ms 459μs) | N/A (freezes)                |\n| `{1..1000000000000000}`     | `41 B` (1ms 15μs)   | N/A (freezes)                |\n| `{1..100000000000000}`      | `40 B` (890μs)      | N/A (freezes)                |\n| `{1..10000000000000}`       | `39 B` (2ms 49μs)   | N/A (freezes)                |\n| `{1..1000000000000}`        | `38 B` (608μs)      | N/A (freezes)                |\n| `{1..100000000000}`         | `37 B` (397μs)      | N/A (freezes)                |\n| `{1..10000000000}`          | `35 B` (983μs)      | N/A (freezes)                |\n| `{1..1000000000}`           | `34 B` (798μs)      | N/A (freezes)                |\n| `{1..100000000}`            | `33 B` (733μs)      | N/A (freezes)                |\n| `{1..10000000}`             | `32 B` (5ms 632μs)  | `78.89 MB` (16s 388ms 569μs) |\n| `{1..1000000}`              | `31 B` (1ms 381μs)  | `6.89 MB` (1s 496ms 887μs)   |\n| `{1..100000}`               | `30 B` (950μs)      | `588.89 kB` (146ms 921μs)    |\n| `{1..10000}`                | `29 B` (1ms 114μs)  | `48.89 kB` (14ms 187μs)      |\n| `{1..1000}`                 | `28 B` (760μs)      | `3.89 kB` (1ms 453μs)        |\n| `{1..100}`                  | `22 B` (345μs)      | `291 B` (196μs)              |\n| `{1..10}`                   | `10 B` (533μs)      | `20 B` (37μs)                |\n| `{1..3}`                    | `7 B` (190μs)       | `5 B` (27μs)                 |\n\n### Faster algorithms\n\nWhen you need expansion, braces is still much faster.\n\n_(the following results were generated using `braces.expand()` and `minimatch.braceExpand()`, respectively)_\n\n| **Pattern**     | **braces**                  | **[minimatch][]**            |\n| --------------- | --------------------------- | ---------------------------- |\n| `{1..10000000}` | `78.89 MB` (2s 698ms 642μs) | `78.89 MB` (18s 601ms 974μs) |\n| `{1..1000000}`  | `6.89 MB` (458ms 576μs)     | `6.89 MB` (1s 491ms 621μs)   |\n| `{1..100000}`   | `588.89 kB` (20ms 728μs)    | `588.89 kB` (156ms 919μs)    |\n| `{1..10000}`    | `48.89 kB` (2ms 202μs)      | `48.89 kB` (13ms 641μs)      |\n| `{1..1000}`     | `3.89 kB` (1ms 796μs)       | `3.89 kB` (1ms 958μs)        |\n| `{1..100}`      | `291 B` (424μs)             | `291 B` (211μs)              |\n| `{1..10}`       | `20 B` (487μs)              | `20 B` (72μs)                |\n| `{1..3}`        | `5 B` (166μs)               | `5 B` (27μs)                 |\n\nIf you'd like to run these comparisons yourself, see [test/support/generate.js](test/support/generate.js).\n\n## Benchmarks\n\n### Running benchmarks\n\nInstall dev dependencies:\n\n```bash\nnpm i -d && npm benchmark\n```\n\n### Latest results\n\nBraces is more accurate, without sacrificing performance.\n\n```bash\n● expand - range (expanded)\n     braces x 53,167 ops/sec ±0.12% (102 runs sampled)\n  minimatch x 11,378 ops/sec ±0.10% (102 runs sampled)\n● expand - range (optimized for regex)\n     braces x 373,442 ops/sec ±0.04% (100 runs sampled)\n  minimatch x 3,262 ops/sec ±0.18% (100 runs sampled)\n● expand - nested ranges (expanded)\n     braces x 33,921 ops/sec ±0.09% (99 runs sampled)\n  minimatch x 10,855 ops/sec ±0.28% (100 runs sampled)\n● expand - nested ranges (optimized for regex)\n     braces x 287,479 ops/sec ±0.52% (98 runs sampled)\n  minimatch x 3,219 ops/sec ±0.28% (101 runs sampled)\n● expand - set (expanded)\n     braces x 238,243 ops/sec ±0.19% (97 runs sampled)\n  minimatch x 538,268 ops/sec ±0.31% (96 runs sampled)\n● expand - set (optimized for regex)\n     braces x 321,844 ops/sec ±0.10% (97 runs sampled)\n  minimatch x 140,600 ops/sec ±0.15% (100 runs sampled)\n● expand - nested sets (expanded)\n     braces x 165,371 ops/sec ±0.42% (96 runs sampled)\n  minimatch x 337,720 ops/sec ±0.28% (100 runs sampled)\n● expand - nested sets (optimized for regex)\n     braces x 242,948 ops/sec ±0.12% (99 runs sampled)\n  minimatch x 87,403 ops/sec ±0.79% (96 runs sampled)\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Contributors\n\n| **Commits** | **Contributor**                                               |\n| ----------- | ------------------------------------------------------------- |\n| 197         | [jonschlinkert](https://github.com/jonschlinkert)             |\n| 4           | [doowb](https://github.com/doowb)                             |\n| 1           | [es128](https://github.com/es128)                             |\n| 1           | [eush77](https://github.com/eush77)                           |\n| 1           | [hemanth](https://github.com/hemanth)                         |\n| 1           | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |\n\n### Author\n\n**Jon Schlinkert**\n\n- [GitHub Profile](https://github.com/jonschlinkert)\n- [Twitter Profile](https://twitter.com/jonschlinkert)\n- [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n---\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on April 08, 2019._\n", "readmeFilename": "README.md", "users": {"oboochin": true, "jonschlinkert": true, "simone.sanfra": true}}