{"_id": "@babel/plugin-syntax-logical-assignment-operators", "_rev": "46-a8e7e94f3fafd99130937099a193e911", "name": "@babel/plugin-syntax-logical-assignment-operators", "dist-tags": {"latest": "7.10.4"}, "versions": {"7.0.0-beta.41": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.41", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5OJFSoHBmK3zvehgUbU2gqNlelSqqjCQtlaq5zJk13HD6FsE+EIMm2+/TcNp6LDkDVAwqqJxYpvwMqdQ9qJXvg==", "shasum": "b8e2a0a7441369a195d8af0b337333f9b9be893b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEuiXixvo89pw71ytEiCkSdlrd41fdR3+1eniZnKfsWJAiEAjfXgNu3DdtEeVi1bL1t161osfwFwLdkqg8Kr44sxcsI="}]}, "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.41_1521044756250_0.14848137823503404"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.42", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-R7ZRBkKKggfb3uJWuN0ZiDR37Ni7vERwjl6GPvRn5Lc3pCIlwe78lJBBy9oIFOXKU+WLDI5o+BgKlsas+UDSVQ==", "shasum": "3301d19d8175600c719d15ad5fd4c4398f2fdd0b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+ld4bqwdmBwqTB5UmClRTX6PXPiLmLxyM3cYlHprBVgIhAOkckcWcWMlm2yBNqKleFwrnhYAJ+QZExPqzZoZs3RDR"}]}, "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.42_1521147030803_0.8021886991578997"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.43", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QndtyoNIHDkVbeg4I/pO3+pQ+mlPb/rle3TV7StFo1yfZR8sC2xarrnrFnTi6C+8SwPbPdDd4luYttgMi9YxdQ==", "shasum": "306a1d239d0f4076f0498ab98bc707a82ace1d0d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1666, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFQYq9cC5aRlsy7FqmwTAgQKEkU3YtK5JvX5Dn7u2+zUAiEA8uwqFvDuedfsUZn0qM515megPDRXkM7r2Axp8z1Sb7E="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.43_1522687697025_0.3256157364819092"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.44", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1B3mg7JDk3bpFC+0uKdvi0xSa/4+cQ+7J8icsen0JnnXdqpVo6pnWYO2/1zOrgiJBbPlX9a+pynkDnOI99fVfg==", "shasum": "9886e994045494fc7f6c91a8dc86988bf98849e1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1717, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDyn0JLB+6sP6TsEH2gNg6j4ZauCDUvkWaE92k1RzwgvAiBIDy+JAjdyOek7iGH/LHvQqHGpQTFuVVdngI3OOTPXYw=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.44_1522707598949_0.19477830007363095"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.45", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-9ijy3GfMz5yGmlpjwL4QmRHw9rbEJoW2Ux30mCHVATcykHf2BODtiqRz4dpCEDZ9sVAF4B25KhIfQkKIp0MWgw==", "shasum": "fd0ca4ec4732c593b167b70dfe6fb1046db71141", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1ECRA9TVsSAnZWagAAae4P/3aJE6Z6jc+xO0b/gcSu\nSu70Y2GNPpQIODU2myupemhUTdmfvIbDZiVvBZsIrjIScjUTkfnQWYXKYNwF\ntKxWPw94Xabe4cqCHSgjdRrWDHIzUTOlNt92Tlsn0W7sWbPd3Tp7kci2Cr9u\nQfUyFQEn71cdShJBcnWBdV6uSXrXFk6d2YMH/JP25JGiU3HtdD0A2PprRRK0\nSS4ZhEt4bjvdKFiXOZlUzua73GcytdPxV0e1sIcEKwd1fMX+2PkbTTksgJ6N\nEz5AWmNxh2q3EQhCFMtHTFZi+Zf3XlnyDX57lP96uuX+Do4yD9JQ5l5F07X3\ne+PtU4ZFRL62vCFyJX7nWqOZNT2lPzV5wOmrrd7aCU5fUM2WTB3B77/9/I2w\n56FFikC2XliF+GxJRMlxYiG0TAXgb8khycyaHxLW36g6p3YUG9z4pzwOdBqA\ncX8mI11MPcJn4gv5PWljJwM4IFus/MVQP6f/xL1GMEVuxelCSZrmMFvX8glw\nq+mgTmuWBio9azXMORcPGkVutYF7KfdKohD3l73SGRsVPM2egaZCmIZBz5Nj\nZBVFGWnxf/2pM/1ZPxeUavrNavAJIRw9Kkfo1ibRT/5H3XTykyPQcSeNieb1\no84B8J+AeXZyMEGlfisLdc+Ja50/Tt9rJRrBFkb0sOAXBi+PWSlthxCQkwdL\nTSvs\r\n=dr8R\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2YBZSCXQmm1TgoBj3jly+CCFeLVV34GKkwB/ZTP020AIhALwjGiuvDYqZMx1RBnGyFfO4dh3Fg0rFSNBbk3Medbvz"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.45_1524448580211_0.0020784699769009762"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.46", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SUpbS+4YqC1F07oolmqwRacxPGYBki1asMCfNNZ2PIrtjKeToEf40q8K23aCmNHlq51LVNti4jbg8eIME55x7g==", "shasum": "86c03db039b100420a489e410df4b4377e098cbb", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WF3CRA9TVsSAnZWagAAVqcP/iJ4QWCCLDhiKDOrLhHG\nJRNEOIgAE5Z3OylVInjJFEbzmFfWUMxFvMzVJbQpfWc6b6IKjmcJalewPw4k\n+sMo169Nc+jCFOJn6gRYxEkYk7r0n66vPvnK5WTgskWRUQWsFNK4zsHS+Go3\nElMyw9JvX7VbPUAcD1cnNLGqOIyuJmJeRkJCgX2JPgye94vbxOGamzf9NCKJ\nvVLtE68DW3QNSSo+TfKcaAUUjQoSILOFUkO6XGBEpe6AqMX0GaFHSZMWgBzA\npv7NrYi0RnydFoURFk6/VWneCux4sTiPL+6/Tu7X809e7nBCuT/N2ImtkVw6\nvpvNoLgGdkhMwI58cL+xyqv5BPXZ31SYA9otis95NKndydaJQ+R5hj1c1QNb\nLPH3D0Z8sIyk5nHHIxQEcz0DNZMl13XF5oHNm253EFWBC/WNump78200Q9Gq\npfYuCb/mdO/9nc4fTC2SeY8M4G91E6eCy1JV0g0BZmmw82hPOuAkgnFNYqxg\n471NW9Nae+t2pKfkQgFtYxt8NUhCGROFNHial0aeFcWzEWgIA9RJLRM10QpR\nG4kdpolR0m/mdvCGvyMLIVErd8Vxi/Jc97uWr2oqSUA5TpyN9rM4hoqPQKky\neHnasRorBo6ikrH54m7GlvUneddrByS1MemEALCYXkkuiTaK4obkeiAT3ui3\nXYAJ\r\n=Miif\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGKKomzqrgds/8nJXB+Tj0OO9pBEwvgz4J3zdBbwg11XAiBiwXoN7K5KVd8LF/hNulZ3RhEL3XByT8JDHKvcQTypkw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.46_1524457847524_0.7851635200883236"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.47", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+Lolr0RFFABfsBXwk0EHvuDIxtu4ZpVH3bOGtX3WH3kvoUtC0MmNtXxR4pyAWl2LyN30RQlOBKmEn2Hp7ijnHg==", "shasum": "965c94a99da71e391676514fd5b0abece1f0275f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iT4CRA9TVsSAnZWagAA7uAP/2tOsageGnBoUauOv7b8\nztYbGIpxPxvX2sGtUCjj3NxerOhftZ1XWQgTwcq9jmC3evw2M9jIVpt1c6KQ\naQ5cXU/fgvTpITJOvHHII4cGh7FBKYCBTUYaEF6ZI8EzPfPJiEWYFFpmfxmp\n+0oSbamv3JhTHqKjmcaE/35INI+ddJtaqYZZyjuDMg3sChUQlBMlaHwbnp3y\nZwm8cq2o0FUFcXSnhbX1gQCO99YX6OPDNIufmOfH6fsIBnPZiWN5ENAV6rGD\nP0LAb/TgvnTb/3jqblPEcc/V5kDiJXGhhMOTXhdQHu3U91cOHpcbCh3UZDsb\nvkJvccD7M3war4dg/JZ42lJ3VC4TNu0uV87QhtviSBTo6fEntvkAjnaE4aw7\nT2it2lH8YNOFMN0SJLVygdSRYph85LAZdu2liFwaOLoFiBzCI1GiEbKt0/rw\nKHNMKokUx10BA7xMvrqtavRaDFHFAtuEkrz1ZmQBvR/Y1ja8u7m77jRmRun9\nJ1GKvYTCpeizz7pJ8kZ9DPMI69OqUOvi1cfRzBp902hBc2D8OYOsp3+YD4aT\nh1a/5Fy5I28FlHQLySKJ2oTzpeBfe40uYpmNBYbxABTKrW2WGNey9t6PSD1+\n1YxxweIKfZVkx/mTtOpYi6IjicXte+aKdUGCWlVOfh8VskzPECGEPBtah5lT\nwp7Z\r\n=uaZa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGnmjSC6Sqptpsj46+b3MvtSyWdYQ5afG/HfqOMYnUKiAiEAi+We/0pijTlsUbUPqlXN38WKlxs6HKJE97Su56gojvc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.47_1526342904263_0.7740713451788075"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.48", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FfB/FV9lK6mXpdUuos5MGrPluWc7m7WE0RGfAFd57CCynAZK3BloiMke9ydmOrj6FJIVCTDu7zujAQIHi5DZTA==", "shasum": "338b8a5b8d988bb480c6ad1e70a9bcb9cca4cc75", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDKCRA9TVsSAnZWagAAZIwQAJCG58yFVNBE7XSgd7By\nbsFI2G/xyqMNvWaA5VqzJtSbguzpglN2qYyMLoQtNPBXtRu8eQhJlOiMHt2n\nsNHwr/J/A5WKNnGBNKQALEjpXb/3doLQDt3e7YsEHQiW5fy2/s2ZQAYlT42M\nYlG8b4MalAvG1zO4Rpgud/5P+E4rVntkua1Hz+eQB4BtXCeE9t8q893qNR5L\nOd0D+DZ0B1OGgWwf7MaH8hFgUZbUC3kqX3ggKz9MufEBcNG5NWTsFqDUr9M3\nOdAgJH4fD7xCGhNW4RS01nGVWGl/1Ssn9IGTeewT4vlRrgOADqjaOorW6Lm2\nyKOUbur33F/tq8rvSQ2ppT5Ae52U4M2LW42pUyQsP+HS8ip949pD+4cPohm0\nYOzqo13IoScaDQAeQSZ/PL/qtaLRQVKM3xnoRZc98TtUt8/pOmpdK7dhKhKz\nZ7KLBZFJV/FpS8ZwfRA94Q+S7uP/G20eTKsGCyA2qXtkElpLsaVQBFvtFTVS\nl+XqgRb5VbTd0uT6pb0+bGkB/314YgLTw4bkf9tXByVNVJyHxar9/YgqeHOU\naWgS7WsOC6S0FPZqvJr5zEp8CVxqBVQkPCXnmDqu0oLPIIjYX0/RtLD0TFtw\ngH17oOPfW89xNoBmczmM5yYGp0zTdyagrbmEfjQJc4UH+r4nGZq/cQYuZUzy\ns0qy\r\n=xP6s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZVp88iuPMRvfIVfETkjURYTN95BlCAu5d5RuWZBF1vQIgbrH8JxKUuZPpMdPSkLlYJeUTJ2i6f4ZZqb0jnF7n0tw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.48_1527189705764_0.7667140920110733"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.49", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.49", "scripts": {}, "_shasum": "3cb9029a0769948e8118407b38b20324b64ced21", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3cb9029a0769948e8118407b38b20324b64ced21", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNVCRA9TVsSAnZWagAAaGwP/ik2PdVe8nSTcq8msEm1\n8/OMRZIiJ2exHlB/PQVD0/nBo044+mW90VVqJebB7UWFojV5zYOddTkXx4w6\nILm+hJ9w+3UOrpbGhf3EY7eiyrIx1R9Ee5XOBF3bSRk5+SU8YSnvtiOd+y2E\nyY/z36jvqnupG0R4xO23RH+VCxiYj4N5oCIVg+Ot6n0wz38UpKzb0ndXRkqf\n2POQHclAx+5zW9tF5puRTZ7wmUKTWBXWZIGlB7VWcEWO4FU9s2OotAmnV24b\nKZFseSoVbg1F1kMfdin+xq/bO9xDKlu2D9Qgt2LJRkSJg5TYiJE681tlRqfi\nbAJmcaSvvn4LAlvC20Y6kJ19V1hau1L/zztciwuowz8GcIM7ACWMRkfYX8Lv\njCxhgN+lgKmruoQqJ35SkoGbp3p7GMzKlY1vECizvF5fd4O86aIdkozmywWB\nHBqQae6OUck9a5zMBHJU4hr74VAWdvV3RL9vgX5mSjqfQNvzw0cp7q21KNy/\nEslgIC8HwYGLslURy0qTRpb6WG3KA2ht09/fLgFEu3bzF9pdAhfiM3ArXsKp\nUzn2ovE8B87eSviUcK3m9NM9hlRPxAt6sr4cfsEsxa2Vryf9S09RqeLiUgvz\n8YLrjsj2eFmlnUa/0rc1BddW/vJywX6hraUnOX37362u36dKdm+VQ/hmAv1U\nsXaC\r\n=Eoyw\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-PCRadW96M7upjjbwUg8zIQJ+KDtfemu2j85vfrawTmaiMT7OrAJ/z3ZG5T8liNJjGir81nkMuz+FLXtudNxGNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBw9VFba/ZiTllogPmH3CDR9qE55ukNY5IaijLykc02qAiBZs7NPXlb4kvyE0Sdo/SiAQh7gFmi4i6u+SiZm2uZMcQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.49_1527264084765_0.3261924052144234"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.50", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.50", "dist": {"shasum": "1bbaa4b2eb51a71e873dcc2185f71948fb413b26", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1561, "integrity": "sha512-y6B/Z8O1JRAuaU0VPXG8VRcSYb3BiaKUm2g7LkIdhnOaiRHgZCsnyauvpyh4f/bjxxqJqc+MUDzjNqdAZ2TcBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHbSbT9qOhGacMff/MhMU6BnweUAEPwtrrGlMpQAJc/jAiBx+Fzp59AwICmdjjfp9vtvoBjJTGIpz92IHpgyBP8K8g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.50_1528832826349_0.02687490529814096"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.51", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.51", "dist": {"shasum": "0cdf4fccae82e41dd84e39a3e6de894296822d76", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1575, "integrity": "sha512-fiDpvsPgnGRNL/p0Vrm8hifT2g6utbHf4yWWCG39HoqKjoH0kbmz2zbCccsw0AtN+TWTAO9IqaCVNHMPTBax3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMQTApXC87EOgFJUr0Fw58OECSGBYXKx3mz7ZWwzpkJQIgL7mVtVAi7e8HrUhk5tESModWI1Gpz7JfSo18I7llei8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.51_1528838374883_0.6190486604017502"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.52", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.52", "dist": {"shasum": "dbd5e24999fdcd9c071a960cc71b8ac2451ab5c8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1574, "integrity": "sha512-7MOtGECUPJMmBvFpZ9UuIgFbn2q8LzOev/goEXSPiAz/TswzYvUG36bcqmVnpRlAdfIEghyWWOsSs12eB9vX8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHP/cXVMb9KhAlCZPA0MuxD2gh/crLA0Ubo4v4LtVbiRAiBeHhb/xqk9w0wsTssOOsbamZcU8WK5P/M81OIcYnRf7g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.52_1530838759692_0.16084887341316434"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.53", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.53", "dist": {"shasum": "c841fdffad63c07e476081ed1198a08104ebfd12", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1574, "integrity": "sha512-bSDdoyRg6tRP9pIwhv9I884nlD9Kiv/whmYxC1Wc3WwiMkWRpdLSK8p9sjj7cnOm0epQ58rpoZbwzK1XplRu8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB6LfmamshZspq/sDVpVeH0D/fM7yfG8RejwJ0r7yM9MAiBEmF9d9a0lrvYqjB3oDZYJiBv2W3+GaA8FWd3KtkTCVg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.53_1531316409776_0.8257775212820286"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.54", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.54", "dist": {"shasum": "82adc9cb5692a51c5a49a266e9e27f43ebf88ab9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1574, "integrity": "sha512-vdMj6JL2wgv8RSfAimkG+sn6EurKfE/pcrEMqAFkvKly+6W2kM58D3U9u5phwzhCdi2jDFiTFJ0Q8vecB1mnHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG5kgoaZwQ2h1fqZQWL0EYKOl3cwrvdu6Ojozur/Sm68AiEAonPd1kwLmRkFAKq8rNz0giErQDNfk4WOWK8geBFXszE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.54_1531764000175_0.5772707102889503"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.55", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.55", "dist": {"shasum": "a94836a48e765a2e98f55eba0d79b7f7bf0072c4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1574, "integrity": "sha512-Lm78dWbxGWqVSGHr4cz8Tlo2/0KABKihqWBQw8KIoBAAOsZ2EzL5Flj9vwvD6V/w9nJEtdUHVFqLzpPfQIpLXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFflUFsa+I1lGWjbV2h+ySUjTxh66r1G3PeAjM+MqElCAiBlDduXMWEprzNSolbyraxwJdkPANGBnK6+5U8ULfVxEQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.55_1532815625632_0.16472240346566958"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-beta.56", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-beta.56", "dist": {"shasum": "a455188fb559d9954e0bb814719e8f7021c27ff7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-beta.56.tgz", "integrity": "sha512-mq/OJNL3LBB45bcOwEwHrs6RTu0PqGfHHkL0Sad9YuZ7wyHGKir3vDVWAhvh8AdbKUiSjkHkI2aftkxj3Y2NTA==", "fileCount": 5, "unpackedSize": 1574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPuoCRA9TVsSAnZWagAAxgQP/ijVwQ5uBhdHfErd729u\nccKsmOzJ5rq/A2Xg+fbq3bqg1edpmlnDuMYBAk70BDTpZCFe+/TCSWvX+C3S\nRPw41aTNByg/UE8RxhJ7AptJjHs/NzX50LC6//+tKNRFmBetHG3F2dhTQHu/\nm7jaaZkzD3dg6JRJdWOPLT1wI6EW225vuIai3ZyrOaPpW9gcYtYc/Q0hASG0\nYTjsFgjNwtuegHyEsV8fq0db4uUb8h+DbbSxWioEe7rSAl6kcnw8CG8vVNJX\n1PFRLBQGOZSQGADOIYl+YYzyz5RtbhCYY97UGoD5ECjckd64hr2Uvt5/KOap\ncSxSRtFj9byax9PE1FinForyV+oA0fg+B6bk8zOSeFNkEdpFPC8oRpwYfv3r\n8ChEcWm0sLu7CKVN6Npc3u7+7yY2up6VLfUO8zn6DE2J2SRMAPMlUM66NevF\nbBmFyquNIZkv0PNFZfyC3n68I5gq1NHoFEh1DSMb+oHRvhAkHjV4+3SEyIvf\nUeR+z5CmONYkWK+fiSUXId9xYzHuBHtq020cUYGj6esLo4VpgLqG+fqL1lEF\nGZ3grsDCPZ6zrabOWkW4n73cyKBpn87sGRfku0u1+8J6zQ+LUNDJKhIYO9ea\nts/SofoaSY50IGkoTd4wIO7HXaFOD1ydVWscdQIQkwsoy3WUSyiQfGMAlaKq\nA/tR\r\n=otl4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGt2QnooKdqmXhIXXpAsWwWR+Oyb0mq/aMAyrzSSizRNAiEAxtMqb7+ycOu0Tw9BPLNRapx4XsBMZ2r4+C+R6cDLvmM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-beta.56_1533344671535_0.19325989459671566"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-rc.0", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-rc.0", "dist": {"shasum": "75a6d3893510c261f4d187b8af187b14b6bd688d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-rc.0.tgz", "integrity": "sha512-/4GzFp3mOnfpnlGIi9WChAm1btRUyc20feofnVq2EPlkMyvF0JUBD9Z1OViFsXrduaQjbfxNqSyVRcG70em3vw==", "fileCount": 5, "unpackedSize": 1565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGR5CRA9TVsSAnZWagAAqcgP+wdIuxp70QD+8t/14PDt\n9dyzvjWrV43+bRc3etUev63766d/0J0ODXim6xqlYUWHYfsgGHztK4v23XaF\n6UCifM2GINlujAiG52XFE4qnZDDxvklWoI2W5faM7/Dp4Wlg4bjbYQhAdkoy\noWKMhRLb0o45IJWSBMMJ4+IIs3PB3yUcI4J7JHpS2NPgW2dshv+Sig/MzygB\n8hJ5Mw4ApJ1SeHkPehmNqisAIXHtyFi0QMDXWlLXkHJaGCqJ9XwYuZ10n0UB\njj2fH+KH4CHRvgeJaIEe5bcSeRAxKM0yDJq1X5yBK7iBgcRBocC/8IZ25Vdq\ntMTFTumqdJm5wp8eg1kMUqIqZm8dScrxlyvYr2bKlnzrlmYpOv8l0dZ8daCi\nc//TZY+mOB/m6NpOKlav0FzigdywOVhFH4K6uU2s9rJrOhd2JCyQGjyAf25k\nhde84fWjDSF4VGAqkIyhHIpnF2+dWXfTbymr75nHBYZQEzYWIDx4OFNmijrU\nqK8qHepbENcQ+xDjxEyViXeLNnTmtv/LDHoN2wUhxDE/R+WYvx2PfJCtI+7k\nuWsbZ14WfmsGrKMZr0V4KTHZe8eNdBUWHvsguit3FTpj5GAOy5vH6WtZObsb\n3jPzUh5OW/NDPpTFI6IObwFYimXcR2egSRA4H5QS8MAkuccJewUrb3Q/H3Ar\nnGgt\r\n=/438\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZ2YaQj/0Uf7Zdb62/bvSGI+hJjKTMcUjfhTAVWdv6XwIhAIldvZ6WJAQq/JYjrAmVNLPJSYFxe9Cui0MCf/EQsm60"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-rc.0_1533830264958_0.4382488261841806"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-rc.1", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-rc.1", "dist": {"shasum": "4cb2a7d745458b6155340872087d0a53c67709d2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-rc.1.tgz", "integrity": "sha512-ZWc3zQ/9J3yV/so+XdxVw/yJcgk83407mAt6mRJUCN1BLNaskqEGA5PPeaaApoL8+9gP/Xi5GaC3wVepPfQtHQ==", "fileCount": 5, "unpackedSize": 1546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8BCRA9TVsSAnZWagAAdycP/2EhhTJeheOmAvAcbBxv\n2svr2dkgsSLs33yBlBQ2d9UA/6Ougx96mEI3ijzR0tJVJ9Tl1lKZwfXGIXZI\nCZHZKjEgmmZwpxssAMkrflvg7TMGwHPfOILd4eD3e5zKDwsJM01gBU8X7BI0\n+nsfQy9RdLwthBz1RlmNywpzhzRHcMLUAMFTPsR8RTb/ONlIYQ4tgteIHrBM\nvaNpLHvS2o0/RTfjKWKb4hiRCFiImuch7AwaDYCL78WsNq+xEGOUc0rzTpDX\nH1a3+s3mYmhZphYdH9D3IambP7O3tWt5w/DiO8fjlzMLQImQttYAQsyVeO6A\nTW+gw4BucJtTcM8yFeehjqjOJE6BD4qjQRWipUfosshL02Umgz1i2bni03u+\nBmXv+8XVR1CsWxpAqyGmmnz6iBAyU1hwv/U9amLRqiszk5vVJg+ZXrpLYr7b\nwAXrZDzSa8UV4nqzEeynrRaDRsDuSBd4QhuiP1S6EOPb97dUeqAWMMJkuLvL\nrgvxPoFQcqgS2zIeoK9+zg1GmCCLDL8HxPhmsLlxP/WlXYRaynP+K+mdLD06\nUDif9nrGTP5PBtMJdHA+0SvgAknbaxWgwufBWGafZlLJaJJ41DRenYzZAKIe\nV1utZd39uoWiWmY36HfZ1OfkExAM+y+KWUO6O9uUYNooxKz8ddPjHuPPwMmK\ntPrr\r\n=SD82\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGaUzomoNQHR+tMNuJ5lw7P1WBvW3woLAxSvXYSr4booAiEA/3uvh9Uj0KNeSwUxXmwZ7vVcDwCLyd5wCKu/4jNr3SQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-rc.1_1533845248897_0.4809762046806785"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-rc.2", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-rc.2", "dist": {"shasum": "2da79e1152d799616b66d8707f75ec45ba45e9d2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-rc.2.tgz", "integrity": "sha512-KT+2q1f4EYvMD9MIWAqpWGjBPBLM1XljXyrNvQoBI6eMqc5iFSnXTZqeF4UwlHvaBzypPrINz0ue5VMHBiXCvg==", "fileCount": 5, "unpackedSize": 1546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGarCRA9TVsSAnZWagAA4rQP/2SoV0z5Rhr9HUTnJR30\nR4N+cO32fVuxWgLbRn509rwIx6yydgaZBuGNFdNjEDTV7cLI98WKj4pZ44YJ\ndzKna6QO8s8WKjMY9+CmXNIzLyicpvdYK4Lioqhs3NVMk23k/W8iH0CXVp84\n7rhqoLcdsvBq8AWHSrMWLWdOIYAhSyYiPtFuM66rYa3/mILWBNfnlsBln0lR\nSenA7ayW1GUylFVSXa5wYLsxj4yM57G5gNW2kj883Yl98t2I7uDHzfbnCHjN\nkzQGSOBgHi8QIQHYnGrG2K57IRpa+etf3qB3LlIMa8fH2O7yhfHHe/FyL6a3\nDOpy+Sj/JfAyULzv6aSgZLnkKv+yO97tTN5ffNGy8P1VSVSmU4L6PL1T4Lyn\ncjY/oXMCmQplJc7Uzv/RnuxELq2+nk8pb3tMKtc4pNkDA0iveHgdZhy1N9DE\nPu2IokkRo2oct/A8TjS8dFPfCINunT3Ia7anczB7gYiyF6ZmmSn7BI3r17oo\nMW/UThL4HmSVoylb2LrtQIRfFWA69qvf+y5kjY0mXWSeVq+RM0uVFDbFlTl/\nTgxFsLonPlAK5b8a7RiAlrArUB1ugyAkLVc7iku9wbpDIaolhimqLvg7yqUc\n4+5qg+tdudhB86XN2oHJcLLpr9LKwOJ/zDWQwfzlehECszhW9ctY4DZvvaPy\n01T7\r\n=Vv/B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9PhOndRO6dXTrLAp8llwygQsMHMVwx3iHU2o+P5ik+AIgJ2AMSj1QAGIyMPY/WEfnfLM6Vwe2tJ49e6x1oECGaNU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-rc.2_1534879403062_0.02090355347460915"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-rc.3", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-rc.3", "dist": {"shasum": "894744db070ae27691dd38aa7fad920073bd94fa", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-rc.3.tgz", "integrity": "sha512-BbFdHHV6tr/hGVv6djfsv6DdDQhgwV0Z/sKWNy3x3Vod2+blgben2/MepPbqBAUxIhUu3YFLk1uosW4bS6pYxA==", "fileCount": 6, "unpackedSize": 2645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElfCRA9TVsSAnZWagAAgRIP/3V2ZPhHV1hNypjJ4Pjp\n5opygsog2OOUxRehZAifUcq6MQzz8rjtfwF0bHefjgpU+oiAGLQnBb5bRiqy\nj/T4CzfLVIZXGojHMovR2UovVavhQ7xOcVln2RCWpHv2puIbR/uQC9iyDLBa\nOYLAFr+grI54XSg76H4p39e/F+++CdL1H6bJIWkzrNuKg2j1RiwUqAOsun7I\nqBdhMF4tKfQZj320DPGSsPSF8p/vraB05SaZVYYvy8WphuevucdchADe/SAt\nfzb0qJimZEDABDjoahD7JMnKrEdQcQwH8IUgfnOfmkuBENZQ1rEpZrKS3pQg\n4bP2vrEKZ1C8HPlKsZ5eVag00C3uRzWQ5oqxsPMFKJNEcsdV6MppjspoS/mL\nuJTY1zGBEDzswV80OH8YPYqOyxgT2M11HHxPHz7lNnrmtvQLk2m9W/32LCEU\n9sB73iZjUgbPwnqQE4Tlsstp/cnm8mO3uXAByAM/aXBEPHwLWOU4Byx5xYmR\njIc4sHyikreGknesRJ5cjuFFJd6YZ/rauCBZQzM3JXmgmV0rKMymiIkiJVpl\nS4rJzfvCwDp11AvWp5A3/6WoRWkup0XEoJgioEox0uMVydq+/QVIEdp2mfWf\nfya98FYV/UsktSVRHyNCSt9AwDBpisSogpZlLbCqtyXzyeoDbztQ9ZvpCiSm\n7MPK\r\n=/7UV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3zZLIbz/3djHAx5EXLFMKrhsnAJpY+bFVrcu55xOYMgIhAPt8acoK0rLzILFoSBIPYyimUor7taEJXdJFXBo0hGAo"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-rc.3_1535134046501_0.7500643928412181"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0-rc.4", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0-rc.4", "dist": {"shasum": "b513db459792f59b79689a8d8e44c2ce2385e6cf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0-rc.4.tgz", "integrity": "sha512-3VmVyotUM450rVfYUITKOKIqOyA0PYN9spdz7IWEUbxA1AaS4pCnfh8AyYRukmIWTk2ko3kUM+J6HBJj+QwWtA==", "fileCount": 6, "unpackedSize": 2647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCo3CRA9TVsSAnZWagAAjYwQAJfSSIekE1bHRGgJqhP6\nRtzMxN4eVjUilpTm3e6mxjR00Q3RiYag+oIe9yy5qisiTSwMaAy0zLrMpXYy\n5iFXnAk8g65nYdzoS/Xmn7HcSeqnxJ2VS8Uwn9dF/kgGqPDci45I9I5Fw/sq\nSRlrMuScxCiiJ3wCb2Pu7hm6g4dJJs2pqpf63e3J5PF/fnkkKbeyMTs0nNda\nQTkf0kkx+GUIGURPaCq7wuOi8wGjrIY2UX/EoGynmqumfat28IFYrNQ5jjfw\nmTLv4ONool2o2IjYFyFNkiHrQPHt2twvv+cvfNYYoBWBSu4ILS3omseNoCbv\nyfmA5jNrhQQUsmX48isOg7dfWxh2VhYpGuOtMt226/iblaREwOnCadzbkSKZ\nVreu/FAOLpyM8QCjJ/Rhdx9Kpc4RxCSqZzzHgqDI2mu06dAnRLn/JFGW1pbw\nxq1wgncT/UjGEVb4eZbekqPyB55fZIoVnopmCxLb4Z4BQHmvYH2WVT7zRozw\n+EjK4+ZzwPlRfE8G25UZ3SvUgohfttwXtMOza2LyPGrEChUI7sbCdC559RMB\nvswSJQhPIyB0/Rf+YfNyTySorRMjP4COX0l7AHYu5LZdd1MZKSwDgIqU/0sg\nB0cTazObtbD+jl3C+QlfOfIwPHyBTlXCGHl+k/yqoqGahIwiZC2JZBOopXP9\nWr4b\r\n=lsx8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEbKYoY5GrtvMf0W8GX4qUyLS3cuxOSozsVfTHZC5e4kAiBVPrMad8vKrDVDhUN8qjLu7ZbqgJKWeoBYyfWEwgS+WA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0-rc.4_1535388214382_0.0034028080479517886"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.0.0", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-logical-assignment-operators\n\n> Allow parsing of the logical assignment operators\n\nSee our website [@babel/plugin-syntax-logical-assignment-operators](https://babeljs.io/docs/en/next/babel-plugin-syntax-logical-assignment-operators.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-logical-assignment-operators\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-logical-assignment-operators --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.0.0", "dist": {"shasum": "8c567dcc4caea33d2743307758684656184d20cc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.0.0.tgz", "integrity": "sha512-eOcVPYWpdReMfxHZIBRjC5wlB8iU7kM6eQyst0kK6SwUPmpYNKyB4rJdf0HTeUEOSRqdlH6uMiLAzReA0qDGLQ==", "fileCount": 6, "unpackedSize": 2632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBTCRA9TVsSAnZWagAA6wIP/jp+uqoTEY3VwhTAEwaL\n4H7fXK/oGksYKjfSf8KuaklMsMs/guq8zNAF6LB7zi9hRCIixbwMy+loMyPn\nc7GbRI7rxFwL89JQ1xdopR3IbMFcpXGVCXXAwuDrFYmVyOxIDldYBTQ7A/1D\nYMyvjBqxWr3Am310p9CO94/pHWKk7Urz1fyJaeKiaLg5jhnfxYsu9GhAktCr\n1opBmi48sZdX3QULCEE2EoCzW0J6pRxSCsb5ux+LEzqLhRnuvYv6jup+daiR\nnE1LFsEfKZew5FWotTX4TiA3a84ajz6mQZenUtQ21HThL7U1MACMw1LFUK0A\nj7g3CQKE7FMEuJgY9MrGWeNVEiUqZT4ns/ZCW68uVBkoT+50HfKmSnPfB2wX\nveruxr2GYLBpebpJR7xBF4cKvWVo3XQZkwkKmTbFW/7zi/k8zJudMiV0IZwk\n5i8uVoKoyRutLBiERWX7pAr5PaZggp5SY9MIwPjHXVp6uSC3FbA9/0L3DxDg\nEu6WIZ3O+uwAVr6iMziKg4QtvL5C7C1UMPEbxu66q8DnKRf22nKcV+aBlOTM\n6yX/6r7WVOROX99510j4lazCCB+ZeUWq3BnJ0eRVS5RZOSwo0fzTZkhvMaWY\nqUzCI4WMe3loECrLkz6SeqkOsoIPt27HtL3yopFOsjH3GSruRd2JhXfEUrL4\nzO8k\r\n=dhhD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWKvIEWNXXHJoWHeb9Ok/AYgswljrnsivM7b7LkRV4xAIhAJmxeG3yaqAOOz0cJroILNCY6KKWkiN/EcVpZizlkreB"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.0.0_1535406162449_0.2714796146085907"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.2.0", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.2.0", "dist": {"shasum": "fcab7388530e96c6f277ce494c55caa6c141fcfb", "integrity": "sha512-l/NKSlrnvd73/EL540t9hZhcSo4TULBrIPs9Palju8Oc/A8DXDO+xQf04whfeuZLpi8AuIvCAdpKmmubLN4EfQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1oCRA9TVsSAnZWagAAZy8P+gOMZk/jDEZlInJhloET\npOCLCKEQTpTKJYCZfR3XVHuV5Br06vyQIHH8epNhYEjnWa9xU6Mns5jE51Re\nZAT2pkNzCC+pJItezHuxIw+vRnf9xqZtLVpIiu6trTx9aRwH1mtN3z7kwre3\nz0Efn00idKzeJWf5D/yV0Rh0dZpm20L+qa367leSHHtf5vJxraeZrHGtOMwk\nNcCVAD1+DlMMd8jXwvO0QBsRgx/E5q7wttTkIkIvtiviN+Y5jV0ey23hl6cO\nC8ysIgdjm6bxOJPoPvRLolinoujUOqN0UWkgCwk2QIXuD2VshpQKppZLn1Aj\nP+BjVyt3rZaDwEPU8M6dXfS1WRwzN499smry+xGPaiXnUH4hQYh0NOPbAmsT\nW1+prhrEXb6A7jy+ySmoAP8nuAYTlgvmJgFiybabXYbSosYu9CFYIWfmaGW5\nI7KMQLzuqlVqA5qJ/3GmzUIHIEmUpPilA8QsR+Y/xmR+2GTjTk4/GuPgmSMP\naHbYzv6DRtFIQDiVrZDzTyWeT6Fy9UEBySaapPyl6rfp6jzjSOGsXmJMBYX7\n7hYLrBolZH1q/WYwbcxhJpkxvXp96vhp8rjS7uP13fnwnnWRheeohi+UDRxN\nhMr46y33nSYzKQD13yFkoJ9QKeLO0L0swD20dZcKK4oQBxxHF9QAdvTvmmo0\nIJ7H\r\n=hZfW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDrE3T0IO403SAEwkQvGwiB5OSP/ri37+vqSCcC4vrSGAiEA6tCV0YRCR4xzedF71cFf14V+riNELNHIJ7gRzV45vYo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.2.0_1543863656266_0.43861445373480556"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.7.4", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-gNtCBpD7ENcpBz8v/ijzs2XsMrw+1eruYvg4/NVIohhDmL/qS1dIs22pcbKe0+DQWoKFuC0rVWo03VFjueafsQ==", "shasum": "630f17d39b2efccbd7ddebb3844e389d606d884d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/sCRA9TVsSAnZWagAA7bwP/341uQ/6ARRBOrwAZVxE\n/p814f7dtbpBQmKE31qXUTslSkOFKUB2Mz0XaeXUvkPdPydm11E2rQrncf5K\nijCrcCA6auh0PZBlemkWgCp+gINZqLCeeysZ4+gkfYh9LlVeybGVrpX90DlD\noWDIxUfyjlJ0NRNBEJCLwXZHUIEsUxQtshFbJhQ1gte2UAx7fYMI+rBf8xEK\nYTNb6JVrenM82OfU+SWoEDi3Zc+lafFhfWt7f3UMhaBNscFeEQoYPy+cqk0M\nlYwb6fsAzKpa1ni2mICiQmdRhKG5XBH+2R3ysMoIE99Hza6iRw1YA7qSD/Tl\nf2YvLrjlI8sT8ERycdrD2Goo2kDWZP3ssUJ/B/f6JANbNB7AGKvzZ7K5hkLh\nbhlNTg1yOmFZSn761zSUZozg3Fh+zHvYZeMISTLiFUX/Hyu3gfiIJ0o9UZI1\nF+ha4QgKJ6TdlWjCGjP6Yj6wv0yhc6LdVIZu2Jpn0UgQcsQh52Stwb0E1NT5\nrQ02JhLuYJ9iNxrj6ZqpvwaR5AxLf0GpS6dZeO+ntx1BJoWkNb0RO7VcAR17\nD1XGPd5ydq//7lwy4NTtQ3kZcks8JfSMzkGJNKE/QkxM4czbqkj9Tindg7EH\nuJZ1dbNKXc2RkSSKlhzgCxgoSPY3m3qztthZIGG1CBaEySPB4YbLbU4Bc/KU\nuWc7\r\n=FDAP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCt1Yt0ptJgYgb1FEqCb0TBJAUHuVf30F+2GyxMROgUeQIgb3etZZX747inllG4jWus+UMPvBFGQSa0XTgf1RcrTdI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.7.4_1574465516069_0.9818891001365289"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.8.0", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-yKAk7AZBu+0QiifFaAMWdzj0QD87Bl1j0FatzzqMa9yojiLcgfNsKRotwcVdFoBoWvD4923EqV5q81JMJMXQFA==", "shasum": "4b22156782266ce8f91f2686d370a6281217e4f1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVSCRA9TVsSAnZWagAAlaMP/0w1J1GRTIeqxcuqAtF8\nhtZ2Ac9nmi0JK7rShPWKebK4I9UnksWVxfeYj8lu5xB1AD4e/Pz5PnnnT8TQ\nRKtzcXLGS2tboHK1lHF7CY4z1FyDHC5xEkl8k4rP2Eg6EQ/yanQuLdT/PC4F\npL+nxk5rMe1wTdZ8QLbfKf0VbPEJH64y84x+voLktQIh/G9ZKbXQXH8N7UUM\nVYih5acEl39owMKKvvrcH77uFUgT+zYvh5M9k4hJcqrb6uciZZUCISTm9gli\nmRO//SAKdjnSJFf/jNG5jVYp348aj00waxCboX7WVO4DUBl88aIohQQFsPN5\nNs++a3tbXWrUwj9knp4o+0x94qOftjcrJDLyoujhLw7pj9BahiufhOc28gV4\naoLU37l0ZQoOokn/GR2iOgmIee6YUpTnqL0X9BnXT/e8L7krsgXhL7pbuxEX\nYt+YE6pnqwg19xtFGUkhQ0xdjnUNVAn65TVPe29Vf//mBj061cfftpsG+lnf\n4wH9qXt+XpoRPLIPYxYYiCEQ0XhgmCBBRl21a0PqRQ5WZC2vlDTUCC+jtSyG\nZslYstpHwi6Cj/yrVdWGm+MlBnMqWcm1HYaHKbiqyn0L5WOl1Dbl0RcBR7UH\nrbcOH3T1PNgQ3dk9ULBGC2RW8YEgfFYIbp4zDDHltJ184oUxDo8EbPNlrjYx\nfnJW\r\n=Rhvx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCesKGWZJ7SsjqjjTuaSHUXm4rRvE5F39zARpoFWWlnlAIgGGwZGVnQseJrkNpXgF657D2CitjblsEdDtJUT76CqSY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.8.0_1578788178208_0.8620663635246177"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.8.3", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-Zpg2Sgc++37kuFl6ppq2Q7Awc6E6AIW671x5PY8E/f7MCIyPPGK/EoeZXvvY3P42exZ3Q4/t3YOzP/HiN79jDg==", "shasum": "3995d7d7ffff432f6ddc742b47e730c054599897", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQGCRA9TVsSAnZWagAAhG8P/0dcmlUFfr8Rz7vwj8xh\n8ryAZ6RrzqyMucBb3Pmfu3vUZJln+ix/wY82rKPqFHmPEFH3bLa/HeudGrhw\n+gOyt2DvytqIQnJYjL2zcFBTCk9flMWyGGd3Ly3hZZaKfPBNCEg/wDtYi0hf\nXbzgvVgL5GFLHHuXJOOloUfXzr0qlZK4I5YgNHSn9pSuZ3xoZIjtgvf61pZ0\no1q9c9+8/niUiU++U9LMGYaxDy7EZ/mCog2VHwnvPwzuOhWGUKnsuGk+IO8D\nIfhb/Wv4eFUroMf6a7a4YBoDvMpKNgqv+fiEFWYJqgCfyjxOuo6JSehdZi2M\nnKjeZZcEpozIhX97wt4X4EI1yzrdSdPkzmdXuyCCCKjc1vr8CRjTPkMEDRG8\nBXoOMlkL//MjpRMcEQpyW7xFjA419Sj2cwzZW746vTEg5dNmyvZKyQlq5yLv\nrePiX5S0GMQ/FvO8vKf5/f98XUof4Pk12XYTsA8OxYv4miqj9iXAjAXDoHCb\nvOncTseznYLBSWf7RwxB9NYZo76TPYA4SzVak8shBLJZrHfM/NLgtZzyws+J\nDw15qIlP+53OK/9f6+l7Ncx5aciQ/D4HtMKhYPKI+J4SAmsujK9ajXQRvGPg\nbmco5uAKOfXRMVH6pL/fYzHaxI3MiDzOQWDQ8+/odGTgm9rPwlkyM6fHUTyF\nFn4l\r\n=yTaW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRh92tH43ppjR/OgE90EzD/8k/ilSB3J6hgjxoh6NGAQIgbQ9D5HLjVEmSecrq1+rW8oNno6IZRFZlMfDpY3BfGBo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.8.3_1578951686379_0.4290664333803378"}, "_hasShrinkwrap": false}, "7.10.1": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.10.1", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"integrity": "sha512-XyHIFa9kdrgJS91CUH+ccPVTnJShr8nLGc5bG2IhGXv5p1Rd+8BleGE5yzIg2Nc1QZAdHDa0Qp4m6066OL96Iw==", "shasum": "fffee77b4934ce77f3b427649ecdddbec1958550", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.1.tgz", "fileCount": 4, "unpackedSize": 2744, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSPCRA9TVsSAnZWagAAGZgP/2uMqX8o1jHbjFfODxGP\n0n6FqmrXOOYPEtlViSL3eR23D4F6U1UVAaUm7OQORHTF3h7u05eFC867gUYh\nrg3LrFneLl09pW31kCp6jIxLoUiuYICFdn6bVL0hv7H7LYnMWT/DctAIL97S\nhDhXVe3pX4fLDLcm/5PDVc7qRYDny0G0YzUyOoWQnvTACqTlReKEYIo/4qBj\neiTPy9b/fz41TWHKsZShZoFN1Zym6AonoS3yCnZZXDccw0RHT6MuCyz/g2Gu\nzgOODZp8q7lN65K5btLuH/nE7ipCrzZe/9ASUyBoPwVxqveN0OaIv0Wfa5/+\nrRSlSIsLzI9l9ffGFVFcRleG0RjB0n2E3x+Iu4N6Jj2vLi2pY5zD7cIw4YmB\nSJUflOOEwAUm+IaBg0UZv1zeakdjToBVNm9w/9VOdHgUhY5gPr8co9bgnZwi\n6fjyGw5uokhG6O00pESD8c9X/jb4KfA3oB4t2FGDD/G/TktE5vQtb4HFTdte\nOqZ+AnU1xvf+4ioi4kp81/JU6VzckXim9xdNCACmRDnc8zIKM6J5mUi4lTG0\numI962Hpfgk2QFW3/GhR6NN9oDNnyYd+EsC/X7piiWT3Rnq+Pyqz+z5cYn4c\nVSyWmDPcCbt5t9MJVeN49cVAJ3Cj3eeJ1dAGufWRtbpJIh3IHRGrQYsf4UDY\n5XYL\r\n=tKlH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXO0gzs90+YJX9DxyCN6s7ELz4Lqllg+PSB9gwbX46rQIgRyXQcQgkiWwlQlsKBsZfp+c9xXK4GKqO5f8SiweJrk8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.10.1_1590617231015_0.2352158751941298"}, "_hasShrinkwrap": false}, "7.10.4": {"name": "@babel/plugin-syntax-logical-assignment-operators", "version": "7.10.4", "description": "Allow parsing of the logical assignment operators", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-logical-assignment-operators@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"integrity": "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==", "shasum": "ca91ef46303530448b906652bac2e9fe9941f699", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "fileCount": 4, "unpackedSize": 2744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoTCRA9TVsSAnZWagAAFZ4P/0rBvUZZj5UMc0HrtKRE\ncDCBtgW4KFAEV/TczTzhxQQxnAb5oHC44QU+4p2xm7WZGYwA2b3nqHHaeQn2\n6XGU8Ysx/VfuRK0BBXRu1g49DF+3+aOC2xo5Qp2e+GdN6I0XvqUtzkYkrIhz\n27F07di70Lty/qBe3QVucTPSW8wwi2H8Cvo2VXASKq/sjM1OYvO2kRN+w/c5\n/Zxy5476TTUHO8QIvuzujyhuKBzEbIlJN3EUZdpRogNm9wML2SDwUaBBB/HI\nvHxzn11ZBsXuw9Oq9+4NwqwjunG4yPzcKP/PiCqJahDkv8dadLqHtLdtFEPq\nagTgKipDhfB2GND8c3Tke+jQL5OJdAbfzBDn7nPkEdOgm/V+aoL1ZZeFezuz\n04i/0E/SiDDmrzKOzan5xPFTE0FhPQ+R7PVSMalYJaqa5j8SmfxB2kY73R9P\nbZTBgA64Zmrm7hcmtkOxK7uEQcjkzc3f3m2CKfI65A0vvg/btSMNZzBVQQ7c\nzfqdPWaCLsSwoP9eY+dlQvMoUcq0P13iHTDG6k/e6lQqo4FEEsHtn6mwUlgN\n+uCl/82+uhwE/SHEfgZFJNBUND/C5aGGdoT8RSdTiREcZa8gAnM5K91DMQsc\noyQRtkPUfRkhIHm5NIbMygDCqD6BhJ0UM/okUla6qi9z3jwmy2UosPFPFEHV\n1TOp\r\n=auDJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICqPGzm4CX3v5hQDwcOVXNeY7a/5ZTK8YMI1f5cC7i9KAiEA4yubJyhWQA17t6OG1PA5+MgmRw7sIgCw3CWr5rC95A8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "jlhwung"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-logical-assignment-operators_7.10.4_1593522707095_0.27153055194870257"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-14T16:25:56.203Z", "7.0.0-beta.41": "2018-03-14T16:25:56.436Z", "modified": "2022-06-12T15:01:03.308Z", "7.0.0-beta.42": "2018-03-15T20:50:30.920Z", "7.0.0-beta.43": "2018-04-02T16:48:17.108Z", "7.0.0-beta.44": "2018-04-02T22:19:59.059Z", "7.0.0-beta.45": "2018-04-23T01:56:20.325Z", "7.0.0-beta.46": "2018-04-23T04:30:47.580Z", "7.0.0-beta.47": "2018-05-15T00:08:24.329Z", "7.0.0-beta.48": "2018-05-24T19:21:46.056Z", "7.0.0-beta.49": "2018-05-25T16:01:24.848Z", "7.0.0-beta.50": "2018-06-12T19:47:06.417Z", "7.0.0-beta.51": "2018-06-12T21:19:34.950Z", "7.0.0-beta.52": "2018-07-06T00:59:19.755Z", "7.0.0-beta.53": "2018-07-11T13:40:09.824Z", "7.0.0-beta.54": "2018-07-16T18:00:00.299Z", "7.0.0-beta.55": "2018-07-28T22:07:05.670Z", "7.0.0-beta.56": "2018-08-04T01:04:40.427Z", "7.0.0-rc.0": "2018-08-09T15:57:45.030Z", "7.0.0-rc.1": "2018-08-09T20:07:28.973Z", "7.0.0-rc.2": "2018-08-21T19:23:23.201Z", "7.0.0-rc.3": "2018-08-24T18:07:26.604Z", "7.0.0-rc.4": "2018-08-27T16:43:34.503Z", "7.0.0": "2018-08-27T21:42:42.552Z", "7.2.0": "2018-12-03T19:00:56.372Z", "7.7.4": "2019-11-22T23:31:56.178Z", "7.8.0": "2020-01-12T00:16:18.348Z", "7.8.3": "2020-01-13T21:41:26.530Z", "7.10.1": "2020-05-27T22:07:11.182Z", "7.10.4": "2020-06-30T13:11:47.198Z"}, "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "description": "Allow parsing of the logical assignment operators", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-logical-assignment-operators"}, "license": "MIT", "readme": "# @babel/plugin-syntax-logical-assignment-operators\n\n> Allow parsing of the logical assignment operators\n\nSee our website [@babel/plugin-syntax-logical-assignment-operators](https://babeljs.io/docs/en/next/babel-plugin-syntax-logical-assignment-operators.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-logical-assignment-operators\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-logical-assignment-operators --dev\n```\n", "readmeFilename": "README.md", "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}}