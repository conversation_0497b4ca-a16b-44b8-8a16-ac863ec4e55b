{"_id": "mkdirp-classic", "_rev": "2-18e1c2eb48265f748b36718925782a6a", "name": "mkdirp-classic", "dist-tags": {"latest": "0.5.3"}, "versions": {"0.5.2": {"name": "mkdirp-classic", "version": "0.5.2", "description": "Mirror of mkdirp 0.5.2", "main": "index.js", "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/mkdirp-classic.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/mkdirp-classic/issues"}, "homepage": "https://github.com/mafintosh/mkdirp-classic", "gitHead": "17dcb18ef483828884f6c9d7dfd82659c258ab3c", "_id": "mkdirp-classic@0.5.2", "_nodeVersion": "13.9.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-ejdnDQcR75gwknmMw/tx02AuRs8jCtqFoFqDZMjiNxsu85sRIJVXDKHuLYvUUPRBUtV2FpSZa9bL1BUa3BdR2g==", "shasum": "54c441ce4c96cd7790e10b41a87aa51068ecab2b", "tarball": "https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.2.tgz", "fileCount": 4, "unpackedSize": 4459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeenIXCRA9TVsSAnZWagAAPLEP/1z9pD7/Z3F59Hk6SFAA\nBp+MubD2Dg0L+hWRmqu7n2k5z8DeQ5YhNwq1dtmEC7JyggwfjzI3FKZy/WcI\nVmtB0kbq/aWL5RxOfB4S1VSWm/V3IDYBr1raPkRNEVETR1+VMGrYck7bwsnk\n9nBd61it9SEQ0b5vW53g9RpH817SG74N/x0gBjGdy3Np8rxHO3pULLFasEn+\nBrNdiFpHnK7NwJ0Yrx8+AYx2MdOBJf8PyfnaGqzK8qSx/KldCYCPVOEpll+m\nfB5FKDyzOPMmKqLPA84sE4+K4CfRH5K5oPR+XuvvFu2VDfrhG50/2m9w9UAB\nylJmK1LQROuXBaSXwsr+DavsollTXCykNpl6hQjM50bUhoi8eKwr6EvUj3Yu\nZEm5/+YNNChrPhOL0rOK6D6hdi4RmUv9aVvJfc1mmhb7dHIxvsRYu3lZcfd/\nfpWrGUdqOEB2bHj2u/oNJ8amHJMACYMLNKJAS+623JUX/9AQ+HdQohdbJwNj\n9/OrsAaiOUHiW1lWSi9ihc8lZHaVqXxX+1HgY3uod904DQoEjXkoY0UoN4TM\nnWngC/82AttQ+v5Pdql8odc7qLnDYzbNr5QLToN4kvgedzl2eMuBmTF3+Es8\nGEBHCY6aOrfKvJ4k2SM+4UBB9ByvS4rZXnXaBuB0KKK1UMdAX2R4nnS9rP9e\nG/qr\r\n=Vl+n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCizGzcYmoXRMdQDt1UbbSZpsAUu/SswJqE15GGizBpDwIhAMTa8XW0p7NmyT+gvD4W7ol+2Q8GB4o8Cy4xhUDB4+OS"}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp-classic_0.5.2_1585082903443_0.12992222607743975"}, "_hasShrinkwrap": false}, "0.5.3": {"name": "mkdirp-classic", "version": "0.5.3", "description": "Mirror of mkdirp 0.5.2", "main": "index.js", "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/mkdirp-classic.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/mkdirp-classic/issues"}, "homepage": "https://github.com/mafintosh/mkdirp-classic", "gitHead": "3b50bec17ad8d97f1490196ddb1245e441510978", "_id": "mkdirp-classic@0.5.3", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==", "shasum": "fa10c9115cc6d8865be221ba47ee9bed78601113", "tarball": "https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz", "fileCount": 4, "unpackedSize": 4498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJer+azCRA9TVsSAnZWagAACOcP/36rUG2+xmUufwXR92xi\nN+2pI3zZAea9JOvico0wfMKMTGPb7Y1sx3GEeRL0YiixwffoZyGPflhvpaFo\nO/qozVqlgIYd7Tp2UqYUB48MGuHlb8HYUPfXgBkk2y3PRWir7CmLb3WtkbJO\nTAXXTpulkrCJl6We+XHkSRll9gejF0w8ZEIthUQdz/iTD7Apjmv5pdb3GsVR\nXQVfwZiDNGDVOg1qkZ7nBPbdmLNhX1hzX9JyLj0tLTHbKpfkS/j6zld+8j0v\nZPn/ToXvdmQe1SHybG8nYKSorfQDgf5MbFxk3nSLYIZvLL5quJnyLvmHIofi\ndzMTImEXlxmoCbAgpwx1H2BHWTYCB2lh/hTlVrCb82wHrdphgTxNC5OpH10Y\n6hIHi1ihYHg13bllCxWrY3+qhVcURh2tWM8PfJED2O2p2RzlM0kZoSoPd/Pt\nIBiRE3TEf40PO/zasxfNEoBf6efC+hjv6sQi6d8m+x9PjTu+Ps69Hl82F5QO\nh7/wk0YHQhxOZWLFZGbSKG45LFqIL/UkKgfaL1f+I6PtNTEZJzu31FrN+zKd\naJqaqyvWA741vG3cjJ5/6H4OYWqqe5arJW/dRtSzqZmXikIRNzMxoho6tJYU\nll6iJU+TaCnH0Wf9rGMeN1ZDAI+RJ1MoKmwBuNbjH8pt33Pd9nXgXPt6tFHB\nPJko\r\n=OyGQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6VWjoIF+jTwHSAe49AFC6IF0okuWurkDRsQZjShfXoAIhAJRt+MJNKS42qlAKeq0pQ+71cccBAN9Sc4h8Os/ZhKFV"}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mkdirp-classic_0.5.3_1588586163280_0.5437008688978779"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-03-24T20:48:23.443Z", "0.5.2": "2020-03-24T20:48:23.594Z", "modified": "2022-05-09T12:18:51.232Z", "0.5.3": "2020-05-04T09:56:03.432Z"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Mirror of mkdirp 0.5.2", "homepage": "https://github.com/mafintosh/mkdirp-classic", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/mkdirp-classic.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "bugs": {"url": "https://github.com/mafintosh/mkdirp-classic/issues"}, "license": "MIT", "readme": "# mkdirp-classic\n\nJust a non-deprecated mirror of [mkdirp 0.5.2](https://github.com/substack/node-mkdirp/tree/0.5.1)\nfor use in modules where we depend on the non promise interface.\n\n```\nnpm install mkdirp-classic\n```\n\n## Usage\n\n``` js\n// See the above link\n```\n\n## License\n\nMIT\n", "readmeFilename": "README.md"}