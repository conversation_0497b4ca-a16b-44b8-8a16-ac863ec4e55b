{"_id": "jest-doc<PERSON>", "_rev": "164-2198061345b503fb42fc6e6f0c39c99a", "name": "jest-doc<PERSON>", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.1"}, "versions": {"0.0.0": {"name": "jest-doc<PERSON>", "version": "0.0.0", "_id": "jest-doc<PERSON>@0.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "cf51e191d00ae5f8a62e61cd326bd0493e9f6a26", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-0.0.0.tgz", "integrity": "sha512-uvVsUe8G8cGOXpxBi6XU1DhWmw2FdRrdgpvF+AwvUJ0WQ7HakgSDuSpuoUcGcc6dh2yx1ZngFGQpM3VUJl93lg==", "signatures": [{"sig": "MEYCIQC8gOnQM2VP0qXnxwjFzk+U3TyNcITG9utzwwJirXXKdgIhAOO0YQMp/G8Fp505PUwmf0T90lj3k4hEe5XxLZsRXPvd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "cf51e191d00ae5f8a62e61cd326bd0493e9f6a26", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "_npmVersion": "4.1.2", "directories": {}, "_nodeVersion": "7.7.1", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-0.0.0.tgz_1488553847307_0.10164580773562193", "host": "packages-18-east.internal.npmjs.com"}}, "19.1.0-alpha.eed82034": {"name": "jest-doc<PERSON>", "version": "19.1.0-alpha.eed82034", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@19.1.0-alpha.eed82034", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bf8718e1df92865c90d4ed33fd0546ccb8159986", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-19.1.0-alpha.eed82034.tgz", "integrity": "sha512-tlG6LwgKn0aWO2QdYk/jU9jksKCt9alH7FiqOh9YnQ3PsJLda7D3PZ6uJ4tXMiOrvLmjMvMVfLAAkE/r/K/MVw==", "signatures": [{"sig": "MEUCIQCVEG82WqX6QMVy0YBlIrjhd3PcBbobV98pmEiEAmvxYgIgWr2qfYtmKJYPtKJpjk1Enm1pLrjfscelzTt6MO+ZAZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "bf8718e1df92865c90d4ed33fd0546ccb8159986", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "directories": {}, "_nodeVersion": "7.7.2", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-19.1.0-alpha.eed82034.tgz_1489711276532_0.8164251870475709", "host": "packages-12-west.internal.npmjs.com"}}, "19.2.0-alpha.993e64af": {"name": "jest-doc<PERSON>", "version": "19.2.0-alpha.993e64af", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@19.2.0-alpha.993e64af", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "da45e7c552578bc39d00083d9d5ae8d7eeb13f78", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-19.2.0-alpha.993e64af.tgz", "integrity": "sha512-nK59vZBaFxjbzrfIAe3Q8o5vmaPu/ISOGKKBvCHYFbvQ4kObcSUnmXgVCDPCPdWIleRTX1VbOrOF9C+hBQqw3A==", "signatures": [{"sig": "MEUCID6UjXWjB5peT7YDAGqlJCviFCoi6URVGQFcsKv1c6wWAiEA0Ans5qdQpvaLRdH+0ksD1aAHbJqjiJDrxivWgo1znW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "da45e7c552578bc39d00083d9d5ae8d7eeb13f78", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-19.2.0-alpha.993e64af.tgz_1493912252477_0.9218532545492053", "host": "packages-18-east.internal.npmjs.com"}}, "19.3.0-alpha.85402254": {"name": "jest-doc<PERSON>", "version": "19.3.0-alpha.85402254", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@19.3.0-alpha.85402254", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4e585a2f9a2f40e94c898a0e746e8c72b098cb9d", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-19.3.0-alpha.85402254.tgz", "integrity": "sha512-IQO3yOvuLlstOt/lVQVzyJbrYEWBRIR6/yqGEnsR6yO+BXhjWVDHvDXxceP98biAa3UhV55VD/5dSFm2MkQHyQ==", "signatures": [{"sig": "MEUCIQCZ+e+V6rcBTtG5Dm2x/AI0dg5EKgb+8aSW9PDWbuSisgIgXCkbQEYCKqq4erEQRPEe7LRZTnOHa8UP0Dsk2xfS+1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "4e585a2f9a2f40e94c898a0e746e8c72b098cb9d", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-19.3.0-alpha.85402254.tgz_1493984896480_0.8813271548133343", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.0": {"name": "jest-doc<PERSON>", "version": "20.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-doc<PERSON>@20.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b647c4af36f52dae74df1949a8cb418d146ad3a", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.0.0.tgz", "integrity": "sha512-aR/8YDn/yDeGS6W/Q4YavaBrgB+7A3UO8O63KIfHgxnfqU+/JaVLPZBSJ2QPtsPsu8zHQ8pyaJMe0x1Hefou3g==", "signatures": [{"sig": "MEQCIBaD9X9FmcP1vd9rCFWaaySY9/pe3YpMMnwpRgqgtVZmAiAFUu05k3LfhlnARn5NAadOLGug6buMkKO0dHhV91d8hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "5b647c4af36f52dae74df1949a8cb418d146ad3a", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.0.0.tgz_1494073947148_0.10115733626298606", "host": "packages-12-west.internal.npmjs.com"}}, "20.0.1": {"name": "jest-doc<PERSON>", "version": "20.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "055e0bbcb76798198479901f92d2733bf619f854", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.0.1.tgz", "integrity": "sha512-dobRp+1dT1Iywi+p7/zF9uUczUKJnwwYyJNFhgWVggFLGB4vR7R+LDZLikdQ6lkM9zXZmh9xZaaCL6M1mNwMcg==", "signatures": [{"sig": "MEQCIDZkwLC0sKNW7k8szn2QL+rpCkcUCwfCfJ+fFJxJwCnpAiBwLlBAsFhnNAAcdrDYVMItledD8ba/o0vvzTDjHjq3+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "055e0bbcb76798198479901f92d2733bf619f854", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.0.1.tgz_1494499800519_0.6817271162290126", "host": "packages-12-west.internal.npmjs.com"}}, "20.0.2": {"name": "jest-doc<PERSON>", "version": "20.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "82d1e79b79bab0b92c40dd5033e3ce9bbdcceeea", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.0.2.tgz", "integrity": "sha512-ZnNQm7sxYnLcAHGnRtKq1wLa9OENsUpbrkm5pUPQFTUpgE0AkZGbFUs5zADxOcuwuCf3so8igdsOEfzpYXPnCw==", "signatures": [{"sig": "MEQCIAgUgQJUmRlydSMCFV2Avd/7sEXHjf9GMjOaaFKULa1HAiABPgodikWatgNmEWpXfdfhm3oVR9+5o8t6dwf0XqMYxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "82d1e79b79bab0b92c40dd5033e3ce9bbdcceeea", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.0.2.tgz_1495018213993_0.17744637560099363", "host": "packages-12-west.internal.npmjs.com"}}, "20.0.3": {"name": "jest-doc<PERSON>", "version": "20.0.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.0.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "17bea984342cc33d83c50fbe1545ea0efaa44712", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.0.3.tgz", "integrity": "sha512-lHwefzd+CB38Awy4yiWb8he+ovUXjNeuN4tNQ1sa6/vBh6P7XwpiuSDIs4GH6/yE1uDBUaYiaf/msD7cWMTL7g==", "signatures": [{"sig": "MEUCIQCO4WVylKLUF8j2tvq84KgMYzDOPMShQzOg2qhErWEr7gIgevp9emvNK1wdTINdmFG7lKcWF2ZLpBVhus32Em4Ytog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "17bea984342cc33d83c50fbe1545ea0efaa44712", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.0.3.tgz_1495018624029_0.17387354606762528", "host": "packages-18-east.internal.npmjs.com"}}, "20.1.0-alpha.1": {"name": "jest-doc<PERSON>", "version": "20.1.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f486a065478325b4cbf55bd0bf9504b19e7727d1", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-alpha.1.tgz", "integrity": "sha512-89rXSFQiz0kjSWWc++h4qbboeu+vsGbl0cyCLgtoqynOogdazcHgo19xVZdj/Y23QUw0wXJfhDVxh6HZYZ8wSA==", "signatures": [{"sig": "MEQCIAe67r8+mWSWkIQBCFTyox5gWfyPa+aH5k8N8kk7TFRyAiBzfjkipIbIbsvHDKeyQ0QJvYwS3de/qF72DKI9M9W48w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-alpha.1.tgz_1498644973885_0.9771023667417467", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.2": {"name": "jest-doc<PERSON>", "version": "20.1.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a4678fc3b6545d4b589053275ff90ae5f0e99c4a", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-alpha.2.tgz", "integrity": "sha512-jRKNNCq95LTmDJwMZK2QHydDfFzv1hVWVmWJbWzyeXdf52/cAT08P5R1IWpBOSsNcJJL46nyJCaAn/FSIr6XfQ==", "signatures": [{"sig": "MEUCIA0RQ7xqjVu80Mn1oVOGG80FcCRFplQ9kCTRIx8AgjeuAiEAnLK6GjW+G0zd6RaDjbLirvxrCN4DzSxOSotiGqwtWqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-alpha.2.tgz_1498754200435_0.5517149523366243", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.3": {"name": "jest-doc<PERSON>", "version": "20.1.0-alpha.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d35995bb58fd7603fc04551bb2a5f3220a92e98b", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-alpha.3.tgz", "integrity": "sha512-XfSciPyGvTRzFwqXshIO5Zhw2P3u5YCTVInrqihzTGF9J5qNahWLPs5uIl6U1LHO1LPvuVz7wkavfP+EbwpGSw==", "signatures": [{"sig": "MEUCIQChQfWjFayaFwHnMQ3iYUrFMWR/bKeHiZHqIKa1dc+32AIgNSF3r3A7fBUDZEIJevLZ1rgc9fq5FZs8kh29Z1NoNjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-alpha.3.tgz_1498832446559_0.9763716456945986", "host": "s3://npm-registry-packages"}}, "20.1.0-beta.1": {"name": "jest-doc<PERSON>", "version": "20.1.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "157ee768b42f6e2531b5988bea450d27df1dab4f", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-beta.1.tgz", "integrity": "sha512-DMNDSODFsMb6uGAlQyGuqb8BifBNciLe1HK1ItaZWbtf0/Hf2YqL/owdmb+o2M+hMjNJGhcRiqWKCOH2eM8LIg==", "signatures": [{"sig": "MEUCIQCjjseAgtEMoOTk8/swdVqtrgR2i1cCl56+2GUbRt8t2QIgeGL4xfbbzyZUbX8j+iwo8NtxbZ+8uK3na8yKtnb80PU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-beta.1.tgz_1499942013781_0.5235937121324241", "host": "s3://npm-registry-packages"}}, "20.1.0-chi.1": {"name": "jest-doc<PERSON>", "version": "20.1.0-chi.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-chi.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "06981ab0e59498a2492333b0c5502a82e4603207", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-chi.1.tgz", "integrity": "sha512-zX64LtNgTgphO06akqDxKQhVET3uwjZTRalqG2jJoWyEdSUYGp8yjjqicJ7rRu5ZbwnuaB8yqqP9Exu59oDNaA==", "signatures": [{"sig": "MEQCIDFhi+B/Ltm4VbpZkr4QvB2xqFXRhAL3MigTZnoCk5JiAiBELU1MXrdgmOR2YSRi+XHtTVRg7yZ3sgEYxYptPE9Q9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-chi.1.tgz_1500027895912_0.9102754853665829", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.1": {"name": "jest-doc<PERSON>", "version": "20.1.0-delta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-delta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6789873ddf3f7e90480dbcaab18386a5f17bde6c", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-delta.1.tgz", "integrity": "sha512-GKuU9CEEkBFiNhPm3rr0feEF7AmMJhj/cHih1NeKbyF/Ro1KrVyY/BbUopT4HYG0blTkdjnpTyNUBDWjPH869A==", "signatures": [{"sig": "MEUCIQCkDdX5hl6xwvP+pwxV1xLAWfSgjELVVMRNqAcKNtYLMAIgY0COK0DEsmWktcl3dNyXSDUmcoZ+jkkdUagYWRjE+Ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-delta.1.tgz_1500367606830_0.2219448215328157", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.2": {"name": "jest-doc<PERSON>", "version": "20.1.0-delta.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-delta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3987e9bcfd53a8436054d85ca321b599ceb89060", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-delta.2.tgz", "integrity": "sha512-BkzO7u9GM1Ray4Ud4SQ9v8muSL9kt3UJTqdXySGnkukSdMxTdLL74JpHkrAJ87YBNG2dsfMz/8M0+dHWFQlVuw==", "signatures": [{"sig": "MEUCIQDubfh1o+T3bzC1bw1CK2h/vKfSVx7HIRl43nsXOHjdaAIgAoh5UH9dIn5GohL54mOHIoyN98m5zRsLGAc4oZbEW58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-delta.2.tgz_1500468996258_0.12748813279904425", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.3": {"name": "jest-doc<PERSON>", "version": "20.1.0-delta.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-delta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0eb622ecf1f40b355df17b3a42c5864d9667f2e6", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-delta.3.tgz", "integrity": "sha512-z3NrirsqM6NvvQodYO3lTz2ldK3sKtyj/EOHQBpcOSHbhNtJdqFuOIF12xeRbNr1CUTxgsQEUfW7qlx+Aq0czg==", "signatures": [{"sig": "MEYCIQDO9FnOj5Yf8/Z6wrat9O6tkBtdWtfYZuqiTFbiM5ch+AIhANCYowdyl01C9hy1yHoGdZk2ed8AHdN1Mn4Xl6fFqlEE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-delta.3.tgz_1501020742207_0.43313767039217055", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.4": {"name": "jest-doc<PERSON>", "version": "20.1.0-delta.4", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-delta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "360d4f5fb702730c4136c4e71e5706188a694682", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-delta.4.tgz", "integrity": "sha512-apbBKyQ8ET8XQbOnsjIYTvmo2+FRZIsxUkfy75IbG+SH9ILQLjEfPXq3B/iMRCGoTAeI8lwxUQoLKGbw0mrTCQ==", "signatures": [{"sig": "MEYCIQC8hWn7CWlr0E4j3q2NuVDIMDWMI1lBcXOIIDKGw9XYUAIhANbJYuPIjl6idkvBC9aJBT+lB3Yf42pCQHenpz3MCcCF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-delta.4.tgz_1501175946128_0.7562121015507728", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.5": {"name": "jest-doc<PERSON>", "version": "20.1.0-delta.5", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-delta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a3699e999a4be26beee576a41988bd9c782cb469", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-delta.5.tgz", "integrity": "sha512-0jtoJ+5CN8EJ/0+LHNEwEFYiwW2C1HtkMF/zfb5TKA6pj8QD5UuD3cr4x0o7bXl6n9Wly/RavOeIdhOYIquyPg==", "signatures": [{"sig": "MEQCIHotS7hxX689uVp//ajSt2pWLMVNJxD09DR9A98GAoHjAiAWXG2u6gNylvC8mLcpwh5C7/D0hEvCAIyZyZce0pbhug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-delta.5.tgz_1501605215134_0.7177239707671106", "host": "s3://npm-registry-packages"}}, "20.1.0-echo.1": {"name": "jest-doc<PERSON>", "version": "20.1.0-echo.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@20.1.0-echo.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "be02f43ee019f97e6b83267c746ac7b40d290fe8", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-20.1.0-echo.1.tgz", "integrity": "sha512-zJPqHgxSlu5AYjyFLoXzwSqTZGeRAbtW9lTrWfjfDWyQCQjPlt9j9s7t3UBoDwUocM7qVNdlrcXPPtBkQR1dJw==", "signatures": [{"sig": "MEQCIF48RLTBVNr0GILwF7wG/ZzRBjPux+0Xj/SqsGkZx+prAiAvo7BzF0OPwaMR8wAPBudG8tAEXF46EYMQkVUXYqCdHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-20.1.0-echo.1.tgz_1502210988609_0.5525328819639981", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.1": {"name": "jest-doc<PERSON>", "version": "21.0.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@21.0.0-alpha.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1adeadf2a70a0bdee1cf78544903d15c7ae3bd30", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.0.0-alpha.1.tgz", "integrity": "sha512-G9JoroxFBvSjbZSkRifLARcheBI0z6ugcmDY+CIzoEJmFo5pi/JMa8bjzBP7c/OJ1+eJjZRrzV3DCyYjYkDFJQ==", "signatures": [{"sig": "MEUCIBhOulcrQ8s9bEHrAaV0NpPvBf5nYYkdT4fLDvFD2ucqAiEA2FsgVU67xtmizixbp+YZxTI3/R5M8/dUFP6sox9suls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.0.0-alpha.1.tgz_1502446440620_0.01505434955470264", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.2": {"name": "jest-doc<PERSON>", "version": "21.0.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-doc<PERSON>@21.0.0-alpha.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d00743f146ce86766eb5c80a62527d8698416524", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.0.0-alpha.2.tgz", "integrity": "sha512-VCV+Dplfnc0iB3YjiJWY7lVWrLbe65xvkBDekViIQ5fpwKuhKSKkAQOMd37YvdgEzBXVl4X9oJAKzjAuzx/EdQ==", "signatures": [{"sig": "MEUCIG3ZdQimIZiYUG4rfaq9GcPzZ/UepkaIGSa3PC0XqK+fAiEAnQhbfhrizw63LEUJwRv9IpcLaxMN/NEh/fMIIcYZCPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.0.0-alpha.2.tgz_1503353207309_0.011611000169068575", "host": "s3://npm-registry-packages"}}, "21.0.0-beta.1": {"name": "jest-doc<PERSON>", "version": "21.0.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-docblock@21.0.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "320297db527fddb1824495632b0f6c8181ac864d", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.0.0-beta.1.tgz", "integrity": "sha512-p5dgPaWud2ueWQ01T9qMI214ozxTeuLpUzC0Rw4OE1iMHBRSRjVZBMVyXgQeTyUmn7ubl8psIPstOCJLmeiLbA==", "signatures": [{"sig": "MEUCIQCobeKnXeOKamOD+mfVcQaNayL8bD9H4nZZ2yY0iW+EbQIgPhL+PCe83MfSb2k8dz/MbVDJvblElagnLRapKQcQ2ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.0.0-beta.1.tgz_1503610002247_0.1043091716710478", "host": "s3://npm-registry-packages"}}, "21.0.0": {"name": "jest-doc<PERSON>", "version": "21.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-doc<PERSON>@21.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7dd57568543aec98910f749540afc15fab53a27f", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.0.0.tgz", "integrity": "sha512-nhcLynqskWevUQQ3ZJcIO/5v8bpshydwMOgN9Wsszc6Ri8HVaegGIpbrz/ALzMrSeQ4njAUfXVeY7WDMOPbm2w==", "signatures": [{"sig": "MEUCIQCUoK0U1JMRwOP4xBrG/ncFr+j+hicT6RlPlIdYrc/DLwIgQaqAIW9DgE8Owoqz6ya0yRtk/xaB9FZQwbVWPWbv1BY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.0.0.tgz_1504537305612_0.6095965071581304", "host": "s3://npm-registry-packages"}}, "21.0.2": {"name": "jest-doc<PERSON>", "version": "21.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-doc<PERSON>@21.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "66f69ddb440799fc32f91d0ac3d8d35e99e2032f", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.0.2.tgz", "integrity": "sha512-3wPN1f5W0jh9bTZ7ygWFnzjB1CAIAWNDZ/rda+wKR/J1a1zF6ifQ7XkM1k0aS4mknN0W+3+FP2TSnMBJbgQJag==", "signatures": [{"sig": "MEUCIDY1JZkRQPybRheI+91e3RUL7SFdqswHOHTx+G0ks7KjAiEAjfyMf42uixLo7X+eLYa3oh+TceUn5u0wuZ7W6wrePsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.0.2.tgz_1504880346628_0.9147820915095508", "host": "s3://npm-registry-packages"}}, "21.1.0": {"name": "jest-doc<PERSON>", "version": "21.1.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "jest-doc<PERSON>@21.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "43154be2441fb91403e36bb35cb791a5017cea81", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.1.0.tgz", "integrity": "sha512-ai3olFJ/3cSd60klaRIcC/Cb44/RsJ69qS8uXxfWXEbnbDqjcbl5K8Z5ekfY15hgVZGSAiLz7pOwfjIBE/yJVw==", "signatures": [{"sig": "MEQCIGWnxRrshagJrR7JgcH82wI99oWq58f29CdNRsXMyD2oAiBPZPV4BHV3JF+NKD7yK8WKEgo8+JQBx6guHN/az1KocA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.1.0.tgz_1505353805417_0.8864955636672676", "host": "s3://npm-registry-packages"}}, "21.2.0": {"name": "jest-doc<PERSON>", "version": "21.2.0", "license": "MIT", "_id": "jest-doc<PERSON>@21.2.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "51529c3b30d5fd159da60c27ceedc195faf8d414", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.2.0.tgz", "integrity": "sha512-5IZ7sY9dBAYSV+YjQ0Ovb540Ku7AO9Z5o2Cg789xj167iQuZ2cG+z0f3Uct6WeYLbU6aQiM2pCs7sZ+4dotydw==", "signatures": [{"sig": "MEYCIQChW8eMcpjkPZw6gnfIfsnD8ppidZh3cTbcrn3mE8ezBgIhAPyqBQmgw7ofDPlXH3IYhEE4/ZRmGsuRzukbc5R2+56J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.2.0.tgz_1506457327910_0.6499524489045143", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.1e3ee68e": {"name": "jest-doc<PERSON>", "version": "21.3.0-alpha.1e3ee68e", "license": "MIT", "_id": "jest-doc<PERSON>@21.3.0-alpha.1e3ee68e", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d0c685e39c270579ae85dd78a184a1b6ea248e90", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-alpha.1e3ee68e.tgz", "integrity": "sha512-ZOu5JFpz4GaoCNFLO11DMxu8ACodDIxpOYyAhiEIpw05EWCwKIWfmE5qZ7FN4nloWLGItoLyPJoDFX/biolQdw==", "signatures": [{"sig": "MEQCIF7dRxjT2ggdji3cXh6jTdvxq/P2H/k4XMnMnmAdFXbxAiBYwKcECHYOctSnCu4j/qkcR4+dpdTdImvjX0qAmKe6RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-alpha.1e3ee68e.tgz_1506608430580_0.412857647286728", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.eff7a1cf": {"name": "jest-doc<PERSON>", "version": "21.3.0-alpha.eff7a1cf", "license": "MIT", "_id": "jest-docblock@21.3.0-alpha.eff7a1cf", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "53a614b3ed53ad2704dfc92598cb1d4b90cd8520", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-alpha.eff7a1cf.tgz", "integrity": "sha512-OwlKzNOtbUu6HNwtOKQcM6VhQRXFfq4WJal0QswwpepgNKtRq0vnxJ0xEM69o4snK8RhdV4dr7/CftWBdlXdjA==", "signatures": [{"sig": "MEUCIHVsR9mB5zVJH+k+0PXHktqfKdVUk88nxYrP65WfpdrOAiEAmk6jxyiYogzycaSRagD3sv5XDTBNz2Ns6/CXLQyx8mM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-alpha.eff7a1cf.tgz_1506876403785_0.7188720540143549", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.1": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.1", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f264d50ba1fc93cda8ef3ffcd530cef86b332c3d", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.1.tgz", "integrity": "sha512-7d5emoOoIAdt9+pf244zeQVMJnalypT31QbUUSPOUea6khV+IKYHE5gabWrnQ72ScDRrVVXhi7md01clG7aXdg==", "signatures": [{"sig": "MEUCIDvURD6UdiPIUn/trYxzqfdtdWNFdzlIVgrFE+9lf/rcAiEAgyA1xWj1adxjHlX4eE53i1grPaHy1pfBdG8dmyNnZZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.1.tgz_1507114114474_0.45631175907328725", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.2": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.2", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac5b84090472b3270fcb2a3ac799c745d8d0fba2", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.2.tgz", "integrity": "sha512-Jd58w+qIaYIWJ82YoIGNvXuGbpbsgejqgkKNwpAgtEeAaMzk3dNuKIwdAhnjDYsSQW7YWqXXdhmwFmW22lGz+A==", "signatures": [{"sig": "MEQCIBlnwOu3NRJhySvQJmg5Vtu2is1k/caaWGVEwgjlf2vPAiBIIMXwix70CqlYUS+3fjtDi1eM0PU+ZIjN6cm4Y2K5wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.2.tgz_1507888440131_0.5182440597563982", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.3": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.3", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f4db6404ceceb82bdb165928344bcb95d8f45a52", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.3.tgz", "integrity": "sha512-ddaU7lPLb+qPBlJucSEO61/iwTKvYCW4oP2PJlvhc/bFTtYpZ/CAWYI73BDKJUuSTFLQmwwY5RwGPlzdFuymBw==", "signatures": [{"sig": "MEUCIQCnxMTf6Q/4d6Xsmlu3zxg4c/gf2H4AcG+fYEIdvkgveAIgYMAKHmvA7cLtN4fGhD0KvXvyrAbsThLToDvwDCaf5YM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.3.tgz_1508960035056_0.763864847831428", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.4": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.4", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.4", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "11c3121cccc39d826d484e0951cb2faad1fe1ebd", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.4.tgz", "integrity": "sha512-gH3b1E1CWUE4VtmavN5wXBqBJcV6dOV3zU2SoO7e+a9Ldss60K7pUwKfD/mWe5c6ggn/taFrSrI1yFFxLzWWdQ==", "signatures": [{"sig": "MEYCIQDFgNOeVq5Nn02ekTwyoqwbeEEfcYip6DPCbsOXRczIFgIhAJddkRZxKQkXU1icvq9L3BIczOatEsrkGMB9iECJm7uH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.4.tgz_1509024406826_0.259315715637058", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.5": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.5", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.5", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e5b532612bf849c9de490d4eb9cd95f85cfa6429", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.5.tgz", "integrity": "sha512-fAh5iYKz6VW056ZO8+4Ei5N0EMbkIrvL6Q66EPbASazOsBaXW8rivRhj1p2jPRHSZF34KWgBkGbn8UOfrxGZQA==", "signatures": [{"sig": "MEUCIHslcE7tSDyO6HcWbfxfh58SZXERZ4UpBwRHnNsPPYGHAiEAzNjhq0wZ45TJayZoBuJ/rKv5iVvaWkGuWzSnVagj5f4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.5.tgz_1509628641505_0.8513718999456614", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.6": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.6", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f76a01f0ba33456e98298858dddc5033cad3da36", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.6.tgz", "integrity": "sha512-Wi5rT3cRsPXcT3OCgoXRaNJZ0qGmS97gQuQ79h+7LrmDa7rUt5WGNQaT1jrqcJkYFK9RglWRmj09UH+c+wNPnA==", "signatures": [{"sig": "MEUCIQDS/dWXJxxOx0P5MtjcE4SrCDaUCz+NNKEuydFzQeUr0AIgB4zqrJhYMySDuUtVQyrXtL3V/jEpuFKLvaloqO+k2fk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.6.tgz_1509726081023_0.5937596587464213", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.7": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.7", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.7", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5a3cef8238fb3aedecac88a392dcfbd911ff540c", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.7.tgz", "integrity": "sha512-Qk4si4BSb79ZNUsxYHGBXHf+nIUD9fbgfkipow+WGL8HSwp1cDOU1i127MC5kIqjoU7h9HOZcJlzBKcip4uizQ==", "signatures": [{"sig": "MEYCIQC2yuheTnjfADVx8l5NcshuHi2qcHNN9EAd5qKZNLXzKwIhAPI2g6dtUvBNHtvL2qxXYCy0cphNuQ+ZWwJo6RV8CP+z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.7.tgz_1509961179283_0.55156319309026", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.8": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.8", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.8", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "60b533e42e95ae607704d7f2d131c76dc1ffbe2e", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.8.tgz", "integrity": "sha512-qd63DpJjctWfOt9eM9OkwmZ8RiL7uFRM+5Tq31T/4E3q8Z+b7U1HCgNXX5WXYij91Dg47DAxx+lruAYGEMto1w==", "signatures": [{"sig": "MEUCIE08PfwLKrR0NFNCyx4w9EMj47OX0algT/H+2+WYAXJ/AiEA24eLfBgVyUiExKtP1/vHOJvYzN1ltwFKmiTS6MLnNjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.8.tgz_1510076607902_0.626753281801939", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.9": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.9", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.9", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ee4880b58e15a39a1247b6c5c15e32ea8bf73abb", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.9.tgz", "integrity": "sha512-czI8xmQ23y/r/iuzfdXZo75CWtLwcJqUQbjRh35exODglkx2kftLoI6rc5+1eXvOPOgNqcBm83/8HtiELRwIsQ==", "signatures": [{"sig": "MEUCIE88Je9LdnbGaGfWi7Ka7Hhg86DAH7pPJNv2Dumf2znwAiEA/xO0lY24ua1G+t+1B/NhsLVaFwcUi2yUpNscpSVLrb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.9.tgz_1511356640732_0.3116088923998177", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.10": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.10", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.10", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f8e17eef12b584af9abea4271c070d29c54ddbe4", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.10.tgz", "integrity": "sha512-1qwBNlCSiI+SnB2+vkFoQ7Hal1sJR/9XF28nAHDVrSfLYbMWLs1d+ED2TmQNulsQRB9/Fyx0nOZmx1tTX+sWNA==", "signatures": [{"sig": "MEYCIQCYxpwaD2AAqLDbMp3XQWOdgEOGw60WXgtmtkJ6KZsdNgIhAL8HSWUE7Nhy5YIUvTifR0keWXeqQmdISEHuMEMPaAtf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.10.tgz_1511613557472_0.2681349983904511", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.11": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.11", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.11", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "feb4de87c55a2f563ac24c5f4861f61921550896", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.11.tgz", "integrity": "sha512-sxSwZUm7JyCO8dverup5g/OKJhjYRrBdgEdezIO1qAmMGWuza7ewovpfDmxp+JLvlm0i2WRFKUQNNIMGmPGTVg==", "signatures": [{"sig": "MEUCIQDkmvR/oLWxUuTrbEbZz5aveYt61y6v+7xSZNhqrSRYCgIgbuMtH/I2oidZgmkPtR+R+rbRj8+2hcuTQagV3iwIgbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.11.tgz_1511965873730_0.47444835444912314", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.12": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.12", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.12", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "37f3f57bcc59d0f24d6c7f61d16faff1e8460746", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.12.tgz", "integrity": "sha512-OtyKFTd7wlv7Qh63I+F49R0lHSTmrNmSZPpEyP4m55/tBuoScXtvUWOVQHqJDFYbjsL6Wdo+MS2m5hAVaJlHdQ==", "signatures": [{"sig": "MEQCIGMvgonbBFjWVgaL5Nmo1DBBITQXJ2smKQtjy7cHX4XqAiBRZYbiWwh7CRQittc9hkU1S7dYyYuvSJT2MOVP6spYkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.12.tgz_1512499707657_0.7274779328145087", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.13": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.13", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.13", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a6fc6534ee1cc73eb6c8693bc99b12135ab7cca4", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.13.tgz", "integrity": "sha512-h8I4dCAkDvQcJG0q3bj0IO+1vXGm0jJtfTDXyA5H5DyUj59L9ADx3WZUYMG6PdutddAcUTfZJdRljWE5TaE95g==", "signatures": [{"sig": "MEUCIQCWNTbqFV35+KhUVqiM0dio2ItAeNPYLtSSePkksRBqOwIgKMjRkjf2m/7mw71kUjcNb2Tlumbn8TkUM/5+k/LElDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.13.tgz_1512571020144_0.17039366182871163", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.14": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.14", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.14", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ef6bfa766effc2f35cf651197200d79a22700926", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.14.tgz", "integrity": "sha512-rKJrMcQEENAJSKUMlqdUzq4Hy5B0MkoV0GnogBlqBOrX6w+Hz8FAGNHCn4PYCyRIVXm4k2d1bscpD4Tw4c/Zww==", "signatures": [{"sig": "MEQCIBx+zudFMNr1+/qW37GyhmiAHeJQuDa7cxQijq0EX1WqAiAeZfztL6DMtM80PPag0CS6nTR0Hnd/yp1p5i8VOIgI4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.14.tgz_1513075944324_0.2790728679392487", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.15": {"name": "jest-doc<PERSON>", "version": "21.3.0-beta.15", "license": "MIT", "_id": "jest-docblock@21.3.0-beta.15", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9145c6768c520e9edc52abd64797de4001a9486e", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-21.3.0-beta.15.tgz", "integrity": "sha512-RlXssbcqMUrkTI5tNiupIyEhLkGfFSj8+8bvx/IG9ISQSmkaK0EPsEpCjUIvwIeBCHDPoiY73qIa/2csUcZo/Q==", "signatures": [{"sig": "MEQCIAYKoR0O8nDWCFYJWtz6vK/2F6l2cAQODgWnCtt2tQ0RAiBaaAUlxvIFXdRz/1uyhud5SOe2LcPG4R5Qws249gIa0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-21.3.0-beta.15.tgz_1513344448870_0.6184771347325295", "host": "s3://npm-registry-packages"}}, "22.0.0": {"name": "jest-doc<PERSON>", "version": "22.0.0", "license": "MIT", "_id": "jest-doc<PERSON>@22.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e6a79360172b90bd2cd235a4832e38388b3b658", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.0.0.tgz", "integrity": "sha512-1taER8u2m+d1esD+ekGCg/dZy/fryfu5HEpwRKzjFMNNjGicvaF9qg7DQqGIFd+4GNZR+zI7zt8grTky/FkLYQ==", "signatures": [{"sig": "MEQCIAv+GqoVh6m9E8ZTtfCCXEgrODiCZH1RM7axVD3y6C+tAiB+FbrKsfHZejAQAWBy+P8R0FWQkBwHYYTNDVuS7tq6Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-22.0.0.tgz_1513594997284_0.6416785083711147", "host": "s3://npm-registry-packages"}}, "22.0.1": {"name": "jest-doc<PERSON>", "version": "22.0.1", "license": "MIT", "_id": "jest-doc<PERSON>@22.0.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad1128eca5ff621b939001dfea4de3c2cca0af8c", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.0.1.tgz", "integrity": "sha512-7MPdavUJjHJTJjRjSf4wfV82t6Ig2bcK/VqU0kRHZPpdPkATU5tYj4yKp/RbC2pgu28xQpYax7AxztoKv3BL4g==", "signatures": [{"sig": "MEYCIQCvfgKaJkCPUp6araooxOzhtH16WnFIu6qPBjb+uW9ysQIhAP55RaxW+3yCEzWXbDoVznzEM6pynDR/ZvGknr2+Xqyh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-22.0.1.tgz_1513628956359_0.27760394220240414", "host": "s3://npm-registry-packages"}}, "22.0.2": {"name": "jest-doc<PERSON>", "version": "22.0.2", "license": "MIT", "_id": "jest-doc<PERSON>@22.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b72e0a826a36956b6e7bb241b10779a934cbdf7e", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.0.2.tgz", "integrity": "sha512-g/qOm1SYwXu42XnMYch/UPYwGUzf2j2j2vXdFA6l+Xy0LvZOGrl4iPk1ktf4HB3P/tJq1nbvi/PNB+UT1YQfvw==", "signatures": [{"sig": "MEUCIQClU+Axy9YrYJkYdWKPhXN6fwdaPLo9W3sj48gYxJ221AIgDAVBsDyavKfAC3ru1oK5Rv9nKGJGV+wneMmOHjBhUfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-22.0.2.tgz_1513691576397_0.7355734503362328", "host": "s3://npm-registry-packages"}}, "22.0.3": {"name": "jest-doc<PERSON>", "version": "22.0.3", "license": "MIT", "_id": "jest-doc<PERSON>@22.0.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c33aa22682b9fc68a5373f5f82994428a2ded601", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.0.3.tgz", "integrity": "sha512-LhviP2rqIg2IzS6m97W7T032oMrT699Tr6Njjhhl4FCLj+75BUy9CsSmGgfoVEql1uc+myBkssvcbn7T9xDR+A==", "signatures": [{"sig": "MEUCIDIk2XtTPuq5OhzV0K2o9nh+3MaYeeEQh/rE7+R2TJ4dAiEAho7fjQrSXESyM8cQsTh51oKq6fXqenrIiNWeF1jVfls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-22.0.3.tgz_1513695524178_0.6391702564433217", "host": "s3://npm-registry-packages"}}, "22.0.6": {"name": "jest-doc<PERSON>", "version": "22.0.6", "license": "MIT", "_id": "jest-docblock@22.0.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f187fb2c67eec0999e569d563092125283084f9e", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.0.6.tgz", "integrity": "sha512-evcMu0AdVy1jchCz9ngB+PHxQchvHckInpP0OSjwyIji6UEpRU1m9XULpeIWp2D3odnIbLM/egeLEmRbQNhvJA==", "signatures": [{"sig": "MEUCIQDihv2P17AlCHSqaHNkcf/FSmh+zN0398rwWvBtVLHdIQIgdSRYFAoN4CVW3lavR6Ka4Rn3C4iC64J9XX/puZeq18M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-22.0.6.tgz_1515663996232_0.8973526188638061", "host": "s3://npm-registry-packages"}}, "22.1.0": {"name": "jest-doc<PERSON>", "version": "22.1.0", "license": "MIT", "_id": "jest-doc<PERSON>@22.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3fe5986d5444cbcb149746eb4b07c57c5a464dfd", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.1.0.tgz", "integrity": "sha512-/+OGgBVRJb5wCbXrB1LQvibQBz2SdrvDdKRNzY1gL+OISQJZCR9MOewbygdT5rVzbbkfhC4AR2x+qWmNUdJfjw==", "signatures": [{"sig": "MEQCIDVswnjBz+wzX0OEVay5zHkUG4DGQJmWw9pl//M/bttnAiB98w/RGPcAR4Ohz1P0UwNp4GjOY+08o9ffD+tTJW3G/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock-22.1.0.tgz_1516017420961_0.029495719820261", "host": "s3://npm-registry-packages"}}, "22.2.0": {"name": "jest-doc<PERSON>", "version": "22.2.0", "license": "MIT", "_id": "jest-doc<PERSON>@22.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4d054eac354751e94a43a0ea2e2fe5c04cc61bbb", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.2.0.tgz", "fileCount": 3, "integrity": "sha512-Hh/JRuhIcKL+o4aOTE/kXsz3e6LCdaZoYmkSvuElp+WCt9hdhhRTErX8v6j8O4sNndhhMhYCOjNZgzHI4n51Hg==", "signatures": [{"sig": "MEYCIQCbPMkUxQ3rimMBBicQm8SMTdsvR27VwZ1MzVih3RKU1QIhAI388p1vjJpUZCQvklBeWKbHFfjOay8A9UuZ8bMk7ewz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8507}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_22.2.0_1517999152027_0.8231660513929444", "host": "s3://npm-registry-packages"}}, "22.2.2": {"name": "jest-doc<PERSON>", "version": "22.2.2", "license": "MIT", "_id": "jest-docblock@22.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "617f13edb16ec64202002b3c336cd14ae36c0631", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.2.2.tgz", "fileCount": 3, "integrity": "sha512-ymqH4vzNMA0BrRfbDfQ6+b/AI0FLwuEAjBa87HUcuIz0UN76zH+Qw7lpDvhELzNmxu1EfxP9cxMRnVgkHZ7vyA==", "signatures": [{"sig": "MEUCIDHWj4bIm7VyT7FZ2kQ6UCK1YnsBkvxQRbqwlMXou8X/AiEAnge1AR705P2S4QiurdYSPsD9LsJ5z6fPbe9Jmu29WJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8915}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_22.2.2_1518193689132_0.6380831148092194", "host": "s3://npm-registry-packages"}}, "22.4.0": {"name": "jest-doc<PERSON>", "version": "22.4.0", "license": "MIT", "_id": "jest-doc<PERSON>@22.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dbf1877e2550070cfc4d9b07a55775a0483159b8", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.4.0.tgz", "fileCount": 3, "integrity": "sha512-lDY7GZ+/CJb02oULYLBDj7Hs5shBhVpDYpIm8LUyqw9X2J22QRsM19gmGQwIFqGSJmpc/LRrSYudeSrG510xlQ==", "signatures": [{"sig": "MEUCIQCZCPSW0/9g+oVhBoTP2SC3HAcR6m65mQt45mfjTeVCYQIgfRa8TdrtDdB7XsVFT0TfyQtg1fwlR0b7f3KXy8jwVn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8833}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_22.4.0_1519128207301_0.8008459783765294", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "jest-doc<PERSON>", "version": "22.4.3", "license": "MIT", "_id": "jest-docblock@22.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "50886f132b42b280c903c592373bb6e93bb68b19", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-22.4.3.tgz", "fileCount": 3, "integrity": "sha512-uPKBEAw7YrEMcXueMKZXn/rbMxBiSv48fSqy3uEnmgOlQhSX+lthBqHb1fKWNVmFqAp9E/RsSdBfiV31LbzaOg==", "signatures": [{"sig": "MEYCIQD+1BH0yTQtFQhUwXeq51U1+QPpCEhzIyIe5iXrVntIHgIhANF01aLZ33XG6kR2YkDC4HnDOgOFnBUIZacbGrogA+1D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6836}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_22.4.3_1521648480969_0.14578182581500454", "host": "s3://npm-registry-packages"}}, "23.0.1": {"name": "jest-doc<PERSON>", "version": "23.0.1", "license": "MIT", "_id": "jest-doc<PERSON>@23.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "deddd18333be5dc2415260a04ef3fce9276b5725", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-23.0.1.tgz", "fileCount": 6, "integrity": "sha512-r0YJXAe6wy6DSFB87SPEKckwrq5XyE/DtJC0t0+uD41GFZ/MsKAEjN7CZ+JNEn+2PN+s7L8mTMdEEjJFX60q2g==", "signatures": [{"sig": "MEUCIGnyvKuWhuTh8hdnc6GsJMIzSK4Tz10IR98weSp1utGeAiEAnNBmfDbqZYR6R9lXDttK2OdI00/MhRUL14S1DRhVZNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCs8fCRA9TVsSAnZWagAAGVAP/1W5Qr1k2Aq5t/PqKKRH\n/idlvHUhM+zWRFbLhTwk2354M/Mbz7pQrf+3ASxWa0GJrGzwHPEHd6b3tYW/\n46Kf8Scue+oMoZFWOY8YKjrCDho4GsIbWuU5c0DJWEC0YKHS9VpGcrpoJv3Z\nlyutwvZz1Q4IBnrw6L4QrUCX9k3Os2h+BgaXmDFqgK2v+T4LgQkkxUtOUUmr\nrgHcqyj/W1XrXjJomHK4/mTpBfApa4jZjsU4Bx2GB02ICVEF2Piu2wIgliHb\nE3kCUHQzLuGBRc6AINSYxj2cDAMEZYE5Equx5Zlgc+o0iXSi3oORPy/N+/W+\np5EfowiYZ0oK+WkwcJY+wJn5pcr3pNNfaGHPjPWZ5PtswmZmAm503yE+ym3T\nGu515tQGVX7gNm6j5v3FpsPP4sUVf1cJLf2VbBejNc/1/3C18H/rbMATpgI/\nnT4wViS//N3ZaOFMZmPc4t7nITrjLxZDmmXf8/0RMKeaQzYPKdZIrlSgMZB+\nEMcJ+MFEs3rkWH56xEDxNZUr0tqzjSv+aTO0runeMVeROczZLghR5wYPQ2bR\nbi91l1LwtX9v3DSpJNw5cGdET5wQdus0RbO7NakBtXZhb6AiRpC9gIT0QW0E\n55F8epMiQH8YvPSaJk/2Rck8v1t1DqPxHvNhIRpC5D4fQ44XhljUSl3oh2fa\nenlu\r\n=sBwA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_23.0.1_1527435037660_0.4043132189291583", "host": "s3://npm-registry-packages"}}, "23.2.0": {"name": "jest-doc<PERSON>", "version": "23.2.0", "license": "MIT", "_id": "jest-doc<PERSON>@23.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f085e1f18548d99fdd69b20207e6fd55d91383a7", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-23.2.0.tgz", "fileCount": 6, "integrity": "sha512-CB8MdScYLkzQ0Q/I4FYlt2UBkG9tFzi+ngSPVhSBB70nifaC+5iWz6GEfa/lB4T2KCqGy+DLzi1v34r9R1XzuA==", "signatures": [{"sig": "MEYCIQDNz1iUd9sxmh9HT3NgzzqdqOKc/YR3ZBj4HXXfehjmrQIhAKBOjzCw3s1+6rO4R+CAfb/5PTZweQG7SIRwV761tGch", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6979}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_23.2.0_1529935509595_0.6208300856403208", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "197b97929a9bce01b36119d2e813be8bfe93b17b", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-EeNoZcGnQNBokfBRl/kRTOzl4n3EzVsHMLF0dzLxnSuEG/WBUjd4fIyvErzNoM+sHrk5mkwxMgL2ZmFknpMHCQ==", "signatures": [{"sig": "MEQCIFG/A9eTlIeLKulpX88IkZTancpK5/pNkvX9V+fftjMkAiBSP9aScgQ8+ZUsGHv/D80ln879EBKngZLd/e9YimrOUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbycotCRA9TVsSAnZWagAA3V8P+wYMzNPs+TNrHo4MG8N2\n89H1Xp23NKKvNvEziT+KjvvhIZWYcnjJout44Jnqmzl9R6VsbCutG8rSo+GW\nQLxKZ4i1PJRx0M/QNPNhTOqL7C3o7WlbBfRj3D8mru32qIwb8Mdb/+TgP+ui\nAOCm4mXhUS6xK8xNwch3vQuK4cEtDailijVVBbdf8rquLH5G1mCoLBOAsgvJ\nNZnim3vFCzii6E/X1HBYQt7d1p2hUFpLh+OIeWjqoqhKxi8WHDHVUFm5Gseh\n8VW7MqO8aKQu+tbaCgs6WBkdzx2iHmnII7KU6wxdURs88EI+HDq2eJk2jzVM\nVsaw+cx5+AjCuLoyZvk04d0SNtDGcl0p98JgH8ODbg+mrhfiv3XUrh2/TCBu\nY2FYMJSYIuqR83CAEFM0JlavJFnPUXpLW6MfuEGS9dNGFKaMXX1ptf/NiRqh\nlL73N3U/+RoeFyNdoknW9YSzIcYWtMvHeKfW0ehoz2ZSJotUPLdwFJlc0l/t\nYf3gl6G36veIvqg0APOJ3B5mwP8wrM42owu+V3LYMFJIvSIgLCA3f2mr8wkl\nSCtkoeJlMC9ZKMedRxJgKZsDRcwxJd+TIahVI8WlWHCq63DWZ8BHLN+DKWWZ\nudTFPU+B5z1PkoaS5Ktqz1n5G+RZMXw5tnN8F3AvVrqqMHiz7RQPHMXRfNC7\nw0Z+\r\n=sCzV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.0_1539951148680_0.3296039645782811", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "345f64288e39f63ed778ad4321dbe20dc1b0cda1", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-w5UD7rXu8/710oSN5ZGDzxgtmL4DohS5r4nqVAOUMKKhTMvX51zBILS9HFvDr5MNqr12Mpk19fdX1yt8oLlU/Q==", "signatures": [{"sig": "MEUCIQCsrvce3DuZVJIPou3ib3MTVgkvEVUCsMU4EWXoZGc/UgIgHj1QHnVJikzEK77XEpCBwb+ayxrkaADK9gMTEjN30PU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5HCRA9TVsSAnZWagAAPCcQAIjc48qRDX/DcVcIzDkb\nkb/RAKgZFspSI9YBN0XjbNmaOL49Ige/xqiDH0UpSenAOpI4N8VXjJxsP33K\nr9gZXuAwjQZYj/6F5gwHjTANd/RUwhili4UpBcB+HyJ2xV2SH+vV1QBJgOCC\nFynu5CUc/rm86r0mswYZVlQ3pjL795xJU4TuJJAUgSoJfRpt1rdbMvCB/pVo\nHMil3+R+D+MHfbqRrDGUsUXOudh/urgQcFVHXCGFFbh/3t8U809KeAwUmGDE\nov22SKTii1EJmL0zCyoPebjDhXhWPOO3ljsp7LanKDMUQ/Q9yhW4QeRu8Fs5\nr+YZidHPS4dtSSi7PziWaqR7PWeSOGHXYDGADYfaKj2wpuSebSeePtq8f7oK\nutpl+Euhxh6Ce7sY817HphCfgORChjk7txuhf0+jS2Aa2L5LaPayGbU9oXUS\nXILAhyWZcRBi7ql+oEboJBrodVtFCiUXzqwEx5+GaEE/MoH8t+KBn9jgom4I\n+xDXtqTUWJi0cN11gLwh2fAHTbsR9AcepRJWlrp/Ii4MkZsrq1f5xJXaVZq6\nqmsCbmRPjeoREli+m5rGPcIPXwq5BclNv64FD57C2KKc2tPqh4KqxG5aG7AS\nZx8Qjdu+McZDfbUZgbgBmrjP34e51Oa5PzxdniaKZaGxIJ+Ygsd81GioGVhw\nHzUL\r\n=/5XD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.1_1540222534717_0.7120926626666033", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d2a6b14f5e325af4c1e79cb401e538dc32bd195f", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-i21Lr9FhzQjCmfsp8T2zH8OnMyePmcLBa31euv0RaEqWyzIHbMTz3LyFpbhbiEhLqHfaiTzoygvba77rdtO4cA==", "signatures": [{"sig": "MEUCIG0tlVF5kQKzz8pzjCsvGIM6MLgk4AjfC9EYutNct1+gAiEAuhjsLrUIJyXYJyBP/f+ny3UKwaz/7HgDj19PgEuHU/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0aAWCRA9TVsSAnZWagAAoqMQAIQ1cq9BcCm7MRlUGYFy\nzfFkRnvNhOm2ugDS63dIQ2xFO44vGb1Wc6kYZfXvIsyzRe/F1ughcJm5b6lx\npdj3kN0A+lRCrjPWXQQRdlYbI+QVH8hFmi8U91pMTZ3aRyn9K5K5Yo+5i2bq\nKOipAlv0uKT7Ts8VSKIla6BSW2+EDKWcjcka57a2HNaHvZd9XGvsKM2ySStn\nccy3GfjbMmsQDP0B0HlHFcay2X6eTi1NFoLMblECXckmQbWB15aCXh/RE/pm\nUqID/mTj2fWrywuDOfM2QU1GhA2MdV2VX9GquwN/Mbqj6Px/IwqEovC9+0ep\nPf6QIgKW8B7gw7uQh+AalMsoighIopKLnbtP0/IVrF9poY9lZ9f0SA0lHRkI\nMy/KIOtfryg+8qd88dgql98LNNVv/fC87EGXLbqDymgxCKBxkZPcVTXVuaip\nSp/IFNMWnrJMJPr65kzLBSpDoM6erf+39BigLebz1ss3dnUGWAKwjzMoHesQ\nLK+HQd7JbNLka/9XuzB1zMi+b4y4jQHaZle6DH39JVSy9CJ/58gawvjclLLO\nE5LF37vDnJhBLLG5TvvTQVhiIwUh5WVJErkK9Bw6KSTusiKI1U6hbOJKAmwA\nsDsFmOmdnRUc9IKkRJH73RitO8QMcXuHGECKFrmdAdsPo2X3IGK7YqCaRMHH\nqLBR\r\n=NvIx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.2_1540464661346_0.15722314261225634", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "883264370210bbd4dfadf9ea0bc5f89baca926c1", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-NQ6DsiCR6OLLZ8XI1X5E0L/WzobR1vBRbPj5GDNIseblMKtCBOsVy9tzvl3+sgLDoQcWB4GctfrBD7UM9/pItg==", "signatures": [{"sig": "MEUCIQDpqkJLFyDvMRTV8zu8xIY53cpFuxiCFgUatMpvuYul6wIgE+c6Tz1l1xbzADvGKDYrRtdjjy663VbNwbIaYm+P6eU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00G/CRA9TVsSAnZWagAAV4sP/11ifgEFvyHGjAbH1I5d\n5A2lOPAsKwG0MOLRsitid9aT6sMWDVsWAVEk954vFzv6VlSfDj6hO4ll0R4r\nldLIkRwx8E2pqvYmd2AgrdM9QxumECiaBzda8pT8wYovcFLF4BxQgQWxWKNs\nHn9EKmHi8WaJN+dHF5W9t/GjmAs0oq9Wd6mXMH7plrNM2psDlnBcbqyA/+8v\nM1nFDwk11cIBEvNb9NSy2aQeCS87Qpby40M9d5C4LV2dCsWj/QPpyJz3iwSe\nIiksODWWiaQgee7LDOPJ4AJjrhaebDkBcSvUfkRHluNrYdE78H+rVHG+fQEj\nzPoRYmGXITlVIEm/k3T5hRjXkkzYfk2THC3az7dkvbMqn5ZijRg3nxx+bP67\nr50VMOyjxOMu0SLLo3ROaAbCzu4I2ax9wMnmn7Xctc2233f/bBjwuiBaNjUc\n085u6f+X4Or9DPwwVL3fTPrtxrouOqXTwLBu/Qf/iOO2Kr1rPytTTRXkuwFq\nJPQwqsb45AROimMGzggM7he9CWwLhIozwQANOGq2M15N1s/xYPOZMFA22ATi\nLUOC4P6luMb6MIATI/TWEiBjeBzBZnAtYBN1tim34wW252vmjpdtZtWzS/0O\nlD7/iAreVNuVxH1WDNkuvHH1taFzPHr9/Hi+c4SCg6v9Ogj4tCh8hB8l7rrI\nz/5n\r\n=58mw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.4_1540571582192_0.8865722974611645", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4d41cde48074c15a9167838ce36b122c05b311a9", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-IrLbRQIW0dE3OAoRiQ2BPJBz6M8OatpuyB8RxaBWEtXQzPV7nN5ZmnKdW7laajX4pQ7EkxLITClWMWBdWEfFTQ==", "signatures": [{"sig": "MEUCIFps2B/04+d79j3hSLFcE08riqiLivS0f+gP1oV8uavfAiEAzLqRCmeOxGeYfmi64anSeMU9q81+5iTy8h2dai2mN/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5YfBCRA9TVsSAnZWagAA6EEP/jQJTYrW4wPwof9COAg0\nNYSqN+V9/rhMaY/sFfMn97Ytq/zxO6QK7suVuqOg9AWNw435+Lwy52misJDD\nsdoAkbaAFv6fcbFNLPH1FlFkUyD/Kci/abQI4iRnYTnf4BcXeVKe7xvTRsej\nx3D/wlHNFv2D8JVIaMLSvP4QIuSD+7NM0CgmAuEwhnuj1MTqTgYd8UX6bTyg\n2K//ciG1TjDcBG8xF7LDtl5abYzb6N5TTP2EGQ+gnC5mI7nJ4/pjLQXmJjnm\nzhLWCH/gv1zdqlSgCHFFF1MX1A0ciAMZAQwj9FDMoGLtcw22BwAET3QmmNhr\nD16U4dDid4aM+et2nvw+mMdkm1YxI+zgfhGmLUTSJ0ztP5sPpMV0zWwmng13\n9VhrDW+wumzDA1J/KBgRgs/qsn0zny62hcs06zYHNaCKrKAsl7OrkGJsbAsD\nkOKqvrfrGo2ZIEQ/fNtQBV9sBIxGYoO7/yr1mNkqgLVdcYc5BxbqGff4bupR\n/KZGcC91MD9H/0k6NQg0jnN/P/Hje2ykQ6i1XO5b1pZU8eLJll5RKRydrini\nZ/YoOCjL3MbdpOf6adhbIJscgSkSmfUpyQZoO5E7reZh5MrpHZqvZd8Edq0k\n8YRYoyUXmhB5+oz8RkA7WqCt1Mk/9siJQAs3ysuqkeFXcFowLZT4ZtXQkl++\n/KkV\r\n=hQ5y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.5_1541769152913_0.9105132681885253", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "abb38d04afd624cbfb34e13fa9e0c1053388a333", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-veghPy2eBQ5r8XXd+VLK7AfCxJMTwqA8B2fknR24aibIkGW7dj4fq538HtwIvXkRpUO5f1b5x6IEsCb9g+e6qw==", "signatures": [{"sig": "MEUCIQCjn/eAjw3bOXp0ss4vB49ZykLoB1dx8aE8LjhdlxqtdwIgQI2O3bOywhOI2KKRHc/s/TMR6VD596QQnDuTNY2NEig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5ciqCRA9TVsSAnZWagAAw6IP/258D3p+N1t2Cttig/dD\nMeyznNYVBuWPGyFlDaCciL5k87w353cTvPWVVR4sVqSpwjnqjzO+ivrAiqyF\ntnFBmYi3jl9U52shCx6DHrXNJyKE+Gr8YdhXX7SdiCYJmKkN0YWjzR9Y8jfC\ns2I8qdHLSn5hQwYQbGv955hxJsqTRm067fFR6tWun6l0Qdc9SAe3f+E2mKHf\nK/7beJ5tFUPjegj5z7WiQLTzNwKa76CM7+rxTLCE8GeDrLv3hTkma7PwXU3f\njegpZ4S6o4wsnOOQ9k1v+YKvMuYXIGL6qU0gpAQG1f/wrFqIPPFVR55QP85Q\n5KDf7QKy5GrMDA6LJx0d8MCP4jtwlqKb7+2RwKrRMrjjP4c5YzY1mnZgtGBN\nfOUUyAvDhpL7aKlIpXWd67ytWO2M3cLSrhoTGEs7FuO9edkk9kLLfSYGo754\nCgO2Pd4eoSHWh+CFdXvR0oxSdS12AyuJt5Omi9Q8HR515eIUjlli8yqqS7iz\nmHzmGv7oa9qkzoOQcA8nRLaigIsVoS3daL2Gg17WhgyiMGbi8naTxMbTatLM\nER+7isJRcYfJioDXSHpgLhsmSDJemP+uN4RvPTvWrjRVJefs0Wr8FwYogQLe\nzVR2wXLkwVjYm81KoLTJiOGPWH1ATHU3cegbwwZ/sB+Y2rmg22Ir5n/IJ6tx\nKqo8\r\n=ivFJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.6_1541785769853_0.47281973253350307", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "164d010faed479acbbfe7152b3464a5958b5ba81", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.7.tgz", "fileCount": 3, "integrity": "sha512-oZrXqLLLp0bZgS13nCpN/Q8bdIxC4wa7OeYIljFU39jrMrPy0+ACQ04eOug2YwqIA6266wceD+VGRHD7YXiAig==", "signatures": [{"sig": "MEYCIQDnNBpWaru9mDcfMyAZsC0fFp6Hlt1rTwNwySSUnoRAeAIhAKdADWKHqPeHnKzW5hdUvPxen7cXatSn1oQhS6Q/OiOn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+DJCRA9TVsSAnZWagAA4LoP/RecdOtjy8UDMfp1xfQ2\nWULd1kym67GjkgYbjN75ZRAOZSV40ckTT22qEjXAJoIuPAJ8tE1k5UVprQuv\nPNGTWx/GytVCgKcm0qAgEJzk97Xcl6UnU7gN350ArX7M2Rr1+JH7JW3iuRWa\nBiI/49D6wgMIkcZDw5lKvul8oGtHb3Oz0oIuF9aSY64xlnEibrgiua4CupCb\npTELZuddATOOKdqEMYzt0lSkncLWAC+01jdZgOeAHWwtedD56AqeQigDSsIW\nVO6cUI7SaneYJnKVFctsnviG69TejN/dMT37JJ572nf2PIafY8YvD6CJ/HSc\nrFXbQ2eqMbdig7PjKkvspdIjX7PAc/qISpmvWgMIvudyES4fWoA+H7k3igPj\noa1a9VsZ72dcNpK8mRasW7uPCgfVkuY09pT5T2cdK3qgoTXBEmqCphp3CdzR\n1AAzmAeAI5laSAyBoWDYCJ1UN3ok4i6BHndWon+V3RTxQVNCqfVfDTiJFIuO\n1qrowWG9wfWUYGdtwqMxysInXpuU4yECWealsuJmwNCY/Nvbx5Vmb3vrApaF\nX8sNoLyTu9IaF4MfWvIf/2ALYohTG+W3Gik9hoQJrQeBFDrFCkYDNEUtdRzY\niZ/rWiqtul7SUpMGHlTMMKeXXwpM58wVN5DfFTvI33a5eFlZXqQqpt75rH/Y\nce5t\r\n=B+pe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.7_1544544457231_0.3453023814818903", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b7539fef9db284970c744144a951881378009bb8", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-t69zAVYLmm01Nn6cpJVhRgqnubZ3hn4RhNc5gMwMoabcXIqUxEfjSHyCov7GRd9hgVG4DR6LvTIq9ryU+M/HCA==", "signatures": [{"sig": "MEYCIQDJN7ly934+zqIL0ZV5ToZoNrkkp61BlHB5iR35an0KnQIhANgyKz5n5f+F/lMr1c0hVEkQfNI9olwgRMGgy3cyRVVt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlRhCRA9TVsSAnZWagAAqq0P/1ZVfL/QoBjekt2ALaVb\nl3XhXDPImonnXUmAqTuylU/VHl3j6zIt16fqMdOfW1/b8lhgK5X0vPQJ3JJ6\nWP3Slxfu+ULyBsr1QSs87tIaCn7p2R4qDUgYm8JZw/0K6yESE5JK3AUwYqHT\nDjgR5fkLPBwHlzEEBS/66zD99zCAKjhf9Re9+p20sleB6q1SuK4q2mAJ84Hf\nICM9+LmyKOy2+HsRrvKKVOeWDE8UpBSVAYhiYJERlcyIX9+E7Fyh/mR7QFwP\nOSYmpeQYdz5WxOMIGDCFN3FjxvWfdqyjQEAvhyqFlYtWK1mYugC6Ecz8598v\nHYfsIw5qCnNF8fn+Dog94Zj1MWZf5USM4kZrG3g1ubHBTQDpunuetJnDZEmU\nMQW5Im6q8OKjyBJjLpExvVKLoZ/nY8gtHChdkg8u8rqrnKhtDQqvz7lSgoil\n9uEg3UMZfAvy8qJjFVl/V8tp1uOC7HIpEFVK9u6EuMX5EROR+k+1zcug6Wt1\ncDnVpPrxS+yyqrYoEYvXX0TiDdnCOoHxZe3bxgMNcEZYNK+RjgiFDEqOkRsU\nKjtpsWjDqFlfrkUNWB+aRIyXafgyCawTCjQ+JQBgkcMj6wOdCNknjttENjTx\nyOzmRYjRaKjwud/koJgqN+W09c8Vdkz/Z0egaZDuihNZ6JJlQ5JnsTHhlL38\npE/D\r\n=DDro\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.9_1545229408973_0.4128103925836979", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e8806f327f80928f96133627289b974d28da38f8", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-Ixed6qLxNpv/cTGVj8S5lMJp6Gt3LmZ5dBpLSwgg4jeau1A3uBa11AGk86wNwwYd/k9dZfq/bLvN/bjoqDYX9w==", "signatures": [{"sig": "MEYCIQDWjT4Yq5zU8d27TmSJAH/kK4pcOi3x0LuUEv80g4c3vgIhAME0O/p7C+cEO6DZLkqTYCyWcpHj7blz43zvP/kHE5QL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNikWCRA9TVsSAnZWagAAw88QAIwspPSk+hXtnElBPeRe\n2WgLGqeOQN8mnhW1vi9D+SAXeY+vnltTih0kDRgMOdYC2weV6rJlibVntx4t\n4a6FgDMtF8ov6Exki5jdzqdfRCFsQj1h8oBBWw8Q0m7CVz1gXSCCMSWZDC5A\npxZAE3k/9Bt21DdJhdhf1vuSmNX2e3l5kbHKJW1UCEX2IqyYxKITnHlau8M4\nem89Tf13I13Fotdjqg9gf9CkQQeyBzBKOc8OkqwRtrIVjgWGpZvj5j3dyNL4\nYmpM/9wHJDedLi5xfpGPpJlTSTL7ap7BOqgug9I28Tql2nygIkbf62/H9mxN\nZ+z/a5uMl+inPVQJ/0/K2CjYe/HVmpQQAxnZTQximxIL4T9L+0e00PumOGdU\nFqlkfWOjr8yytImMP3oyLnBIs8kDlluioTAbczTvNQi1paTDp8N/WmjEp9w+\nr1i1XpU72Z3VOjn0f3rR+xHU6y+QoVtpfWZM+95/wutYTk8dYzhmNGFYODWB\n9WzQUZdyCWOMYhtcraNJs0Pq7RwXCyO3+rSpEnHItatVUgKg8jgKI5f+Feb8\nxE+VtliOT8CG/rsVAJkO4XZvRYXacmoKswVwdYWP7RIZe+0lYte0Rg0P+WdL\nZgFc5N/zYKyGqFtv0Szwr/pSKVu1Nim+B5xd62ciWIWE5KIP+idV1mmm6vdh\nzdra\r\n=d/Tz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.10_1547053333443_0.041063396613413294", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c8077b5e4228ace6ed5560a4727003ae5b6d5d70", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-wc9Q97q1xCBHmvQrBOHA/32IsiXWhapKcxjfstqwDKn6SgQNCv7Gr3gXN2wLPXFAXYTmonRA3KKdqeBRz9zMGQ==", "signatures": [{"sig": "MEYCIQDKi4KqL1OlkBkj0HP23wKDKztK4l/JgDl9cYMZBlM8eQIhAIbAZiiTjP8ND/gpYHaADP9OH0VUI8jSCkLvQED8Bv9M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN4/ECRA9TVsSAnZWagAAcYQP/RgZy7XP1uZCYj7yj8XM\njoe9674az62ZuJhObYF2dxIUHwDHbXJGoAPvatQolaqmNsRTzm4FQ6y6ttEn\nhG/DmlkQHlgvs4meS3Cvf04thmV0YKZotbyMsswTOW07HOUMRIZ5qhwL46Yf\nZ0RD052KBI1MKpMEsxk4lJ6nKnaG7lzglup7qQG5sWvVeh+24Pn1vybGOotj\nIjPVE9F8DgoG9fim84CNPVLubrhRoKRZ6K+/PZunc1tzitwXUBVA0JC0nQ4w\n/YUCDaL0B/UjlIQyJHVtq4JhqAP0E9ZYJlFg7py37YtrYQvJ6wP6mnSnVNxW\nnIWLlIzFCGL1OEwgG13d837yd2VxAOMfppIK77jJ8bQtqly+fZQlUAcMymtn\n7h6iaMsHXZRjn3AHoCqKpWxNGYXh3rJZIeftcxfEZa/sDVwNTeeB4DlXmG/X\nCBLHC7vLMQojpA2vEB0WDP+kEXvPSpJkqJYKsIqEzgKMfJPfr/L57xrKE3SG\niIW9smEGUHHJSWdLff6mWmGPkPrfnwNTZH+QNZ4O+iHFU4FCS5CMP9aYSfwQ\nsU9hCoCcBnxKFCxt6aS8jykXG0zZXBj/nNvCYJJIKpmRwG1qdkJeyWcEBS2t\nPf3zi1klKbpRJ96dEfnB3TJ9wtK5qWLTtnzhddr3jSDj/DQ1CObkVxqwY6JL\nHX4x\r\n=o7TX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.11_1547145155582_0.8969480659216598", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ae9290bd09e6e24477b7bc270b4590cc84900771", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.12.tgz", "fileCount": 4, "integrity": "sha512-eWw0++1lV7TkA5gF+5ps3QnHwcONdhaIp5d9C/yDvJHeZY7oFzWpoXwqwuKcyz+9BEEA6JEZBZgF9Ru+7RvwFQ==", "signatures": [{"sig": "MEYCIQDz3B+w3uFKgpKTPlSGUMvR0BfonvERedN+LjtkWnx9LAIhAMhiE89Abej8miulqBlzWaomsCblDAyb1u6fQjnSqjDU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK8dCRA9TVsSAnZWagAA+hIP/3f8MzyQMGrpU7ThD7Ma\nUCachN5ugX5ngMHgmnzChxOgihy9hnI84j5pqe2CiPVafi5JSA1uQYMjS9Ma\ncYDaQqYUcfT66WmOLXG5fTHv+P6rQPMHxYohA2+/5LkvPE6rMH6Cg13ylXBc\ni0l6n7Be4teYv1qx8/WMjt9NW/UMFT0R/AH4evGQRgFHRsAXn8tUS+BmeOpc\n4BbCipPZYOO2TaTr96gOfMe/y8VlaV4ngLmu7iqI9C1O0dYRAhvDp4zz8cyD\nL1QobBVShYe7z36iFnMeclcCXhMpx5/UVk9vBxgAj6jRg3zMQUAWlvJ/aSQf\ngHP+4M/Rx9hfKpC6KyCSilBSoakvCc30ZDQHte3ABMzcD6XelUach8XkzphJ\ntIezpJ5YtxXKUFc+r0OsT5q+B9xkaV5AzJ5ztrmj7vJZ7fcwEH6spA4c75lB\nqxlFAhcsPWfNT02Gl4x4n988AD7ah7rc3aok8XupEvdI3RTm/Q1GmmaZcIVx\n3UmxCLt68DHuBAVfn9Ak3YRCDbyQWjBHylBPlijkQ3rkkFn62EsBlCPTFS5c\nHSZb/fq1MN1z5yH5rqwc8su48EzhL8a03nGoscLD6RE8wqKWdaC1lU5/TMWk\nxvnKftGkhxUPCM7EFqwDsHRL7NudFu8FIBkkxAOkAFcGVR8/Nljvjzt3ma9B\n6FxT\r\n=v9/v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.12_1547218716747_0.5186678314296307", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "93d440c94a4aad4d1b8ae88ca9bfb58d10290201", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.13.tgz", "fileCount": 4, "integrity": "sha512-uBpNSxDgUMgPGdhvnlhDQlrA6rdkRmi1tT7SCLFLWYp5ftkYPFKthvzkcJ01u5X+FwwTQrUl4ej7O7NVzX3o0w==", "signatures": [{"sig": "MEUCIQCA+Qej4CTxHJm+cck/4Gnfp9kQdau52Pq840cmep3MYgIgJlCqdjnoLgXM5eVfEY4nRg0QA272CikajeqGv4SRgA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUHCRA9TVsSAnZWagAApHIP/jDYlHxWkKAOYaHTTZug\nPTaL7/n8cky5Z7XgLc1dekMhwIX12EVslupTZIZPLFGtmyOggQEBXMbIVY1C\nadW4EMksLIGOB1oIQdQ8Oizf1d7nON3PR+DiaXxBfJSOGjAUu6eGWkidjWaf\ngXyJZ/DPLZxv10ZiUKjm6dAE67y2RUSdXCIaYumZIjlV9Xmitor7JFaTHfh5\nDPVv3ZLfG9rN3KNQtpzX+7YwvpjkoPGKEneupxEHF4XlgC4engsDWvuDLsk8\n/loyrF4MvSrF+JHo4qMiiiw47aGrbJeLREEjiVtxcVfQk0wBJGpZz/ZCC+bo\nN7pInISA80HcpFHfer38SmNqA7xX4YUDA4+X9dp76T1m+puzHOfIX6Wxcj83\nxtZA7JJsRDEUggAgdtUh5MnWEmlsc7g5REAf3HBHQ0RF1Jvu4wdyGTR1Ul0L\nHvthSpbyzCWdMel/AzQ6JyH2p3KXl2gFi2SM78u6WP1OnuN7I2O7DHmZzlqF\nDw2szfjFgWHOGariEdAX8+pIPETRRH/dfT2X8NqpgNhvoDOmSY5V0VI6HTpB\n4O5/fsPFa3f84jpXrZmOsl0MPyDOeFITvpd3oj91a58D36esPoi724cbuaBi\nbJMajE8xvXeb/6ISD31FrIP6E4erhNTNHHm+HNRdOTBNR8ABl+kdCUGujzJD\nc5g1\r\n=xigs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.13_1548256519103_0.5241171205593838", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7b448ec158c4535cd1aa544849d6c77cf4299390", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.15.tgz", "fileCount": 4, "integrity": "sha512-mBQAvKjQ6KZqIqEvcXP8KzpAVO9AgXgqeQPTbRggbGFqfGPQhVcoVw3GkAFdCwjiBMH207kzdFnP8iER125zdQ==", "signatures": [{"sig": "MEUCIHUsgX1Nrrx1SVyKna6H12oIJV1DoHdmH7pWGC7ODtFxAiEAlTFSqMLgr9LPoigHNm3YrcMtRLU1SrEPUUmKrmclbz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSftVCRA9TVsSAnZWagAAlP8P/0wQMzaKPAvNoktOfNnu\nbvgaXUK0dtXY0EZXYm3jaf2Kr0pj8PVO8s4eS0WrAbZVHJxT11ULM807CSz8\nKB9CBWLB2tV9/JWLK92GDIvhRmzw98GG1luOGidla1lpD6RuhkuLTi/NMb1I\ns+Y5srbq5oeRU9Ix5qeS9r13+yqsw4LWOnNtw/tuFlOg4WtPRQaIGJ/RPcM0\nzUdKIo2pPCNsYo6Kjr4t1+oNVvpwmjWRGcqzYdRVrkYDgzCIcoxv/travyJ5\nEdO6cirhLmN2nzHZhaSNaHeqiYRBljpJWDNUdM9VC+1kdcWTJu9YJB7sksxe\nWYbBlXYIbO2nTWFdHI7WMmRxgO9uFZEn95h6c5kt+QJJq/mWY7/ZlYlBTVpZ\nMuh+sDwaZCR9/KNXydJErOE/wcBBs6eLLKAFozauWm0Qvk/fn/XiJ7qIZ783\nLCy7iLn5qI+i3Q5dJ887OzD/EQvIoMXPVheBJ684fq6MtbZGPdDBkq/h2Fb0\nbtU+9E/u1vhmchEvtN8BejQrHWWY2gNGm+ODRFPyRRDsz6zeUAZUkUmBB1Xi\n4QUkgPrWH9nTGRsIezKeCfCIupKt/XDYiVxhdm5mfqvPA+kwseytTxxNuBrV\nQOAQU0wA1GSTnao3bCKyIiLvC2EcLZZX/JXlYwCtVc2jjktRiQxnh4uuAcNR\nyyFW\r\n=veA5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.15_1548352341297_0.2274360779980149", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "jest-doc<PERSON>", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "jest-docblock@24.0.0-alpha.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3d99268216b3b7722849263816573c976e9f5fcc", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0-alpha.16.tgz", "fileCount": 4, "integrity": "sha512-Ljun5UFgDKnf7bv211y7qWYZ8csxD5uEO9iFgYeZR3PTjeP1haBildohPXGAS0xGkA29ga4XQ6KYLkmG0tByCg==", "signatures": [{"sig": "MEUCIQDIlSSJoTsdTK4ww0eLxry5V0fUUT60I6Qv0srf5jZMLAIgFOwayjyVsPILq4Tou85/q4Uc03NKRBULe7+kau4i65s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIgCRA9TVsSAnZWagAArNUP/jNSY6hG2vDUjNiK0ZHe\nYvyqEgXkEVO9yQkAJgQ7WTc66hOFfBXfHkQgskDHjD8Nx6OaxQg1wyIbTBeP\nqIR4QSwTmnueYfIvcIBvesW8oSaeb3EaiQ6LnlgFaC1zXNNl5P5/AhE6nRT8\nUh9+pz8QaGZr9sdhbXQ/qF6qrsM6xJsKZufqTBsuQHiE1YUUg0pcZc6nKyfh\n1xvjYviD/+37sNCAu82VjOUgGUPi8tUykQatR9a+oIgfU2l4kAHiXFzHXk2d\nnAPz5EMs98hrrbBp1UgvoBkrLC1kgI/zbatXx0fUC0OqBEkLUn+FGVT8qe73\n/n9I3tyby7zY2d3/+MT9iap8D/UClYZqWXGCAD20gPR9oL3a2M1x3iC6DidI\nu0tps5oe1xb+38G9PyT7datYzU+fMduGy5mlyFUigM0bTDpQv/dgTE8HpP94\n52HuzBmUW3ZjXC38OF8DvPcnUI1cPJD1LlcCHRDGz1uGxPSNX7GGZt6jdIyY\nXgIY6irZ6mqRfvqsyHCWOMlsz3Wva9I4Z9wT6jWrzxqUa6GMu9prNNEWsJDx\ne1HltCUVlDmooJIyiTU8Sy9l9quC3E0Us7JIin8lomIDaErLDCuTW8/3WKD8\npsegMUaXxEKfDD5TuePcaUGsYZyPkcvy6r+DrLuAHpP8dXc8MhpxKe9RJvPG\n1zM6\r\n=BDeH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0-alpha.16_1548423712163_0.6475145078117917", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "jest-doc<PERSON>", "version": "24.0.0", "license": "MIT", "_id": "jest-docblock@24.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "54d77a188743e37f62181a91a01eb9222289f94e", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.0.0.tgz", "fileCount": 4, "integrity": "sha512-KfAKZ4SN7CFOZpWg4i7g7MSlY0M+mq7K0aMqENaG2vHuhC9fc3vkpU/iNN9sOus7v3h3Y48uEjqz3+Gdn2iptA==", "signatures": [{"sig": "MEYCIQDmWCoApVOl5vIFMT1cHSNGyiq6zPSV7TaoYFkWBhQZrgIhAPNDT9knNvQHN8++8fo+CzyfEMhzTjYZK6ZVaFjzKVKV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWOCRA9TVsSAnZWagAA2goP/1DMGDI4wGl8Gi3a0Zlp\ngR4jdWIwTMwYjUvGQXFtlkluEYTpYo/jX+oN8z5xgtRmz8Hc4ls3iVXc1GyS\nbdGuu+U877LRJuDrgSGUS+aRm8sgECTo0uLGJxXRkUC+V6xXU+tTT6hL4819\ne+hN7Ow9dYlB96uKrc5eCZLHL8VwIROq//2/TGr59DqoxFJPHs7kwrt4a1n8\nAKcmltEo9dx8GFF9JZtMZ1QI6rxWNvZIy7q9TdJ+dPUoVTWoYtblsT2WyOcw\nkzklkccugTC8jCtliEMTzkhntSQgh9hsbQni5uAqLmOMjPAqgNrRRqUr08+U\nnkSOHT1xZMsyjBId7GdqTsSM2oXeoMbXRG5G9C4dcH3RC2r8jC7NBY1CiI0i\nlhbeFKc8etRKfKh0xwdiAnCStewhzcRSYRrYK9l1CIBcItRYnd5gWosu8c+a\nFhgfRwuzxFQUcGfmrqCGfu78BtPbaa2pMTm1QthFYtNq3BHkp5F3BG8Z8cJ6\nqRNsaWEtJcMCmPSfk1EQlj/C0+BvjrpXsGLggiJ4HaySdlFZeF9FtxpsNHFx\n4bOU9k2hWCboEvYVi63r+8C6tScKwLjrHETH8p9crE2ipqm228HVqAKYMqAG\nWKif64QmDQ/20aZP/dMh6aZyPyWahcxBUonz/x1GYHaRB5h8JzTlZMw0pbdI\ndkP4\r\n=S7rG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.0.0_1548428685554_0.34466242541931136", "host": "s3://npm-registry-packages"}}, "24.2.0": {"name": "jest-doc<PERSON>", "version": "24.2.0", "license": "MIT", "_id": "jest-docblock@24.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7fe1d22f7636db4f4f66d07052a3d08f63748a72", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.2.0.tgz", "fileCount": 7, "integrity": "sha512-BCo9v+rB61Hte9r2WUS2YHIsKVjW3oGnY2BhWZtSks2gxQjuDVR5QMGKVAywQtOCwmH92O9p2NgNpfpeu4MY8A==", "signatures": [{"sig": "MEQCIDpVOkoRbeunQD+qWvDIzzZ4HDKMfAkndAyncsNA9I7LAiBPlOWc+d1EMPD1hy6c0KoLougdQMO+gRtXIIk87E6wQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcflwECRA9TVsSAnZWagAA7MEP/0CW0/MFWLrn7kzIzcBX\nRTf4HN/MPTXSGcTu63KbN6w5alELN93IpQ1fHGi7P+IoaLFfoeP8jQpWdJRQ\n+shw1SAxET10bMOgABChoB7NI7P2Sh+LW6kk0N/4akwuzDtyRK+qe+ZxfVTX\nkqU3h8Ox6Kou6LR2KDL9IQnJYQmseEhDXpMOpTTS56RUq+zVamH/8jFxxXNG\n2ONYSn6vzKw+zba3MGPO0QIEcMvo9ekm0p7t5Q5yLPiwoRu2Lg30VkxpYwhx\nLDoTNWmzrP0OWr5QV3DqlJLrDCuPd82ysQbAMANHilpOWJrcjUxF0IAFIozZ\ny13ErO5iRreRgPf69Qv3PKs0TUMLTmeIhRJ/oOfxi3oxBOqc1AzBtdPgaR4l\nZwuiCJSYdFoUKotgGeJq2KKFBJVChhgJCUaAXVKiRiuAPJtCuq803aHceHLj\nSPqzYG1R21FD5NZTqGOzL4DcN3t9zAzrad7LY9b7kw1avghExYrIDynXQrTY\n2vpQuVBepmiAPAwd8VvQmyzjNXbkwkrnaS8g2OX5vmc/NptR2z4NanqE20vt\nviTtp2LFFQ/vmbzKvDwnLN9lm8x+nnua1eDNVhXyrSD2dPT5mObLhLvQjp7o\ncuslxqSpzwY99Apa3tR9QkVr5yVADanRZOnndNP4v/t/Stv0pIwfNWwdWSZG\notVs\r\n=VXJw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "d23f1ef18567763ab0133372e376ae5026a23d4b", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "This version was accidentally published and might contain errors. Please use 24.0.0 or a newer release", "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.13.1/node@v8.10.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"detect-newline": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.2.0_1551784963707_0.7523664125503529", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "jest-doc<PERSON>", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "jest-docblock@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a727b4194cd8393f6fd66a057a0bba1f9f55a400", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.2.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-UpQe1ahzXjTqKtIMZS7rl/N0Q1TfXt0+WNI7ItqR6rmeyuBb1rhdTwSAuf9GQfoeHjXtSpFz5XqnkjQ7aGgoHA==", "signatures": [{"sig": "MEQCIG5rv9yX9e69m8H3HcHMDZBk9k9XTz6UbAxZVuJXZIW8AiBHBVpcvVboiKn7PJ2wZYJm2jSzcWEe8Xhm+ndedhi3IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfou+CRA9TVsSAnZWagAAG8gP/RP8X+og/QOJFX8X03PY\n9P7Z5mPIlhYgavI8EHeTK2IVlJv9ZO+V0UjscazyaMbmTia9m1gMO82LcKvo\n1nmFua/EaZAYvaxnuvgLleWffUrXaBshgAYEZUtMRLTygHr4q3VP7az7jT7S\njaWY0qNBiJbjXmlihF/Q7W0W6fmwUGFAeWaOxfvXBAvCnUjPFO2Tn8QQijNK\nm9nRtIxrz+b/3JoVKWeVRLq7293wmnL0LxwpPRibHQ4LGJEeHX0d4M32+fOR\nmU4qr+n+PuV9tV6D5KfLHY0EZaWiop33sHWBDK3wYxHEuM9p3y6c823SKCEo\nJAbLPX2AxiPWY3yv1LKki9MbJPwwvx2lOIYFNdKx3uTn2umvUanoUZ2W6vm5\nuzgLTVOefclB/UMmwwo9nPM2woMMEvySIHQXUnDJMNNkz5gFDMztbA+8P3VN\nhRS0MkHEuFrZBF4ILrW+T4iGYit4I9K2Emb1KC846myoeX+XK9u5r9xW7D+c\nbRs1PM4VjW4NXtomH8mGCRVRMm68xnz2twZ4OXM5KPIyOKPYHFFm7B12ryRA\nrai0sBQLs4d8RHR+TPdfOYEMGaBgRdWZbymVbHbiy4fGr5Nsd0MbbRt3SXrl\nEuK+l17aI23nDZk5w8HmPdgKfxQdibJ/ZyVbrzeiO3LbI2wfjcyHcdzB5mn0\ndyO2\r\n=1C5E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.2.0-alpha.0_1551797182368_0.42438801429020945", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "jest-doc<PERSON>", "version": "24.3.0", "license": "MIT", "_id": "jest-docblock@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b9c32dac70f72e4464520d2ba4aec02ab14db5dd", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.3.0.tgz", "fileCount": 7, "integrity": "sha512-nlANmF9Yq1dufhFlKG9rasfQlrY7wINJbo3q01tu56Jv5eBU5jirylhF2O5ZBnLxzOVBGRDz/9NAwNyBtG4Nyg==", "signatures": [{"sig": "MEQCIEXzoLm3T01E3wB2vKKG1a1AJ5UsgahrcbNHYndlgzSkAiBD3agF6j59RnlxH3ZuxDM3g8yQLB5UqSe2pJebUL9svQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRWnCRA9TVsSAnZWagAAysIQAKDnOVSq9CbqSq1ywhv6\n1HL7Ij/Y/18SQzygIyKbHh3BITxFO1voAZC1H422fumYnULjSIFuaarKQY6y\nqzmZB1Xn1/05Z7laErXTMbF589JiU/BWXWTQZujs2fQn7E2zn4dC0mJq90Lh\n9f48WIFmDY9lf+6aUb2CZe1evpTYXg9hb+GqgveTqHIlhRuI6tx/efCszN3J\nzWeYdy3a6YBqvR2+zPXC1ecwRuPWrpyVAFSM74b6yLyTiBHss6y7FPlFtEgC\ntsWQjfBz7R6QsxEpYbx+0Vvi55B2miV3yc8o2/lKs5TOckuVWgaS8by2SwfA\nMeUYq3San6M0nvrl9IRpBqogfqLg8k0ombP3GEXOz63O1LaFHS+ga8fN2ahK\nyb8wKmrIFb8L5dpehlD0oTWE7wVhDS2lSRC3AOhE+2ScAWLxVOOEhGWn9TxX\nXu09kageExx03R+C/oLlotuZfEw9PI0gzWkaCAuLG2y0o/kHUHlxiNoDFm28\nYGLiyXoizxdewV8mQl9lv4LqTV3RnZw/dpGKHX5QQHqfuXxshz95wqQgnaFt\nKpcAbRYEZEr9Zz/JErDofhv2bNpnotClIq1cADnSocEYgwxvnWAnX/qqkeQS\nlrKpm6MNUKuKRsaC2E672Y0excJ4qAlDOV6WzriwXX+Fajm6THPCdazgnJkW\ntSZF\r\n=Uc7C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"detect-newline": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.3.0_1551963559067_0.5589869587355765", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "jest-doc<PERSON>", "version": "24.9.0", "license": "MIT", "_id": "jest-docblock@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7970201802ba560e1c4092cc25cbedf5af5a8ce2", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-24.9.0.tgz", "fileCount": 6, "integrity": "sha512-F1DjdpDMJMA1cN6He0FNYNZlo3yYmOtRUnktrT9Q37njYzC5WEaDdmbynIgy0L/IvXvvgsG8OsqhLPXTpfmZAA==", "signatures": [{"sig": "MEYCIQCvVmPHi3hvcudPBM293dCyWcnG0qQ4WD413+qACRNWxAIhALWpJItjj7aFpH82QokOh0U3O+1wygDxyzteDfAINPUM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVjCRA9TVsSAnZWagAAQXgP/3Ol5EZIb6H2hc7ht3qL\n+9k68wFUkYGmhSg+DGKF4d0Cbgb+BfuY8nqrrWS8MMgJH/jn9gE8PGEsg1cA\nL1BYBz0mat59XhbjUF6swVKK0pzQDxCFtceYLZXRW9MVS0rDBEKHDJAOSdsM\nI7ZPwgWcYucHjw5pJPtHuTGeyooUxFcIdtlmdm5bs31h2O/UaHUCloNZ2JKe\nyz4tnGJpcCX7CdWm6gCqaMwLr9W6vCOKgnPqHrryKSRqmT6zzu/tBjic7f7T\nLgbSKeqr9b1SrcIUk2ABru96E85iArzQdNQeWGIZPvjK/VeHDdKdnrV+8cvJ\njzON8X7gpRdZsnCNPbOjVjwmDlgdEb5SEGTvWwETMNEYun6NN80dbFxId0HW\n5we/8npnsrkjnDm/tLfUn32LwTAYq2nV4XP5XbXCHWHZ7Ty/9s8OJz0bRInI\nJN5xtmmyn7zXWv4OBjwlUipAC9TSWtKVWOdrpzSqURx8I3CgOU2tVxmJCLN8\nULZP7ogzkjamidOYKmp+A6S0xIKgHjF5xMpEI9hnZvR8Os4odHnyyFnOmv6Q\niwoxLx3bG6d54DC43tqnlfSJ6xP+uMBnrqKqsLUfQf7iFSupuGsP9XoGiACY\nc6vGtwcaMngkzjebSU5Uk3y0sOHjDYqaGU6YRAPMt/dXbMq9fLixOkTbmM7s\nHIFA\r\n=ZuPv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"detect-newline": "^2.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/detect-newline": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_24.9.0_1565934946555_0.7586727128820485", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "jest-doc<PERSON>", "version": "25.0.0", "license": "MIT", "_id": "jest-doc<PERSON>@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9a59fa463d2725737fc1dce2893048e6dc58d97b", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.0.0.tgz", "fileCount": 6, "integrity": "sha512-1eB3NdAzyPwH/io73W8wJS2YpzZoLkubX+wMA6SWO9DH8azFcEpQCncuxkvBhOaHTVu97KlTkJTsW7p7s8o7KQ==", "signatures": [{"sig": "MEUCIHnfCwRFJUWktiGbOfXRM7syCph6+RLqlwXDkCsrDnCwAiEA/TSzYvk6LrD7VwBE0gWZUGoBIrSn65ZQZMGzZVlQjdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrBCRA9TVsSAnZWagAAFK4P/j3UKPYc4BtLU43bXdyy\nYvYgcs9O4HS8kTN4Tz86ODJbLUenWkEaQTOyMlKbsD2zhVFflSCxdrbgXuwd\nRiA6G+QEBgjyBNjNfgU271rXSDPPJJPXaUtQR9oaHFiiPOu8krYHq5w2ZelF\noEJAfoAwMAU7N0VLEiRnmjJP24nTPOEQZRa7nDhueX+1zYQlRpfF/V+tp9Nh\nH+xmHEDcRl1LMYZ75ueAM/8dZZf2mEMy6YG6Pr1V7NIfoh18yz9mL6CCfEUQ\nx9Uu9hHmAcJFUnlgztLhrSmCF8pKrbMxDpjD+GSB/ggXRT1Wed7u6zyfSuoD\n4GWyf0CuxeYGR4T26XBV9Xi07wTh5Luf3hDgh/rVQYOWaApp3XfG7iT7xFP5\nXwgme8JrbsdnPQQpThRA0PZn/GkT7dyXcRvpp1IexWxC6WrZlH2uNLTH4B38\nuiatD6KWmWwkNszZKhxChQjh2IRCVeX/KKUZwTQLV/JgWSit39YEQE+LnKGw\nKUKqG13iIGH+mQIGwM/JF1orPe0OdgBdahwtjuHIU9qxmpBBG/SGrrzJ53dy\nv3X5mp9JExp1DrsC5dIYGKQ9bslnYYZud78Jakprrfj1jYpH8sV38wO5CORz\n9PieliLebzAt7E8CbTS/4JzOCOJ2uiXEduiCfPrgPn7XqOzmFR8+acnijB/Z\n5MD5\r\n=5H+g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.0.0_1566444224394_0.6340661382094768", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "jest-doc<PERSON>", "version": "25.1.0", "license": "MIT", "_id": "jest-doc<PERSON>@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0f44bea3d6ca6dfc38373d465b347c8818eccb64", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.1.0.tgz", "fileCount": 6, "integrity": "sha512-370P/mh1wzoef6hUKiaMcsPtIapY25suP6JqM70V9RJvdKLrV4GaGbfUseUVk4FZJw4oTZ1qSCJNdrClKt5JQA==", "signatures": [{"sig": "MEYCIQDI4j5Kh9aELb28CfiM2SynnYSxSbKanOMiRqyJrx90EAIhAOeVFyEX8Od2ifI0h9wc7NmruIJsFUJ8Akw8TF4a4OlQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56ACRA9TVsSAnZWagAAECMQAJntqzG6hZw4ZC/y8vzG\nHZeLd5bt40pDAtNXfnpAXNZz9ROTOB0bf5VSmp8vulFvgurSA8d4Yc/8PBV0\ncHEeHkamk48noS2A8YHUOWAqJg1PgYG5vYc13AL7Qzo4tUOVxh4jdCpZOecE\nMucosTmmoDoM/1I9SV1SqOt/7L0vk+FwJNYv1XTSdWof6iVCzPhLur+8hKZ5\n7e4Omq9KEYhBYeYgxSGj74k+HwwiOLvmxWvHhkqqQL36+jj2uJCuV7XBBrKY\n5FM5hM+1z6ekX9G7nK+CpymlLqaWyfpn4Shu0sT/3Qsu+Umr7Sjrnf9l9JCF\ntpu5hQaad7cHW4PxlJ9Z1ideqFqp5gZz6m9y4I0SoFRPuSeGGk65OhsSbvgG\na1IUybZ0iahDT65tZ4yDIwoF7idTuZHcEAA8ZE2IZURnxxh4/I8NMVTuDNQh\nkle7abkd7Cm6LUwXakm3fNZVE8zvM831rFZwZy3QrLdD3CfxUkAKLcfpHyX0\nC7Ddq6+/+Hh4Qeco+XLDCl+j/Oi5SQ/qoCJyPfL24jlpdJU9ylCc/rnDcG60\nPLxiIkr85iaBZk1QR+99boPiGuP+s+F6YuAIHyae/AAF4DgAChqrROWjKXG/\nsEeP7UDEZVr2DFf+I2QDWHasZmW0TkW9PrQ3TsiiF/BuLIYvmKk5rqCAobb6\nBi0a\r\n=wiUb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.1.0_1579654784461_0.3751168580733437", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "jest-doc<PERSON>", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "jest-docblock@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c3b3ea96dfa422ecd47f3991f7244626d1be2913", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.2.0-alpha.86.tgz", "fileCount": 6, "integrity": "sha512-JQyph7NNw0HEECVjbltc0MQ2gRPGc0Oms/xEeow6dNkQGtyGR1TtfzHd2zAY/d+RKR9Ngj5dONmajKWbDA6bYg==", "signatures": [{"sig": "MEYCIQCRuaVuSCSy1QymRU2Kxj1UrSGph/Wcc5+Fy6ieumw0TQIhAJwa//kFTirp5WlWqlIUCAvgn+dx4oX373lda0SZqUiE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HbCRA9TVsSAnZWagAAXGQQAJnJyDvTSetZG6qNKsaY\n3eaHg15C1MB2onGSZgIvUQDD4WDI1v1lNpfaPlcTP6oRXCL3PijNPzUUfCIv\nOORlveREUip86X7Yed0lDwbgSKLYX4l7HWxD2+BFBI2WO+gnn1hXl30kP4E8\nZ18el8krXcRVWj8kMyzF2l/COf5+VTPNh2vK9vxQqBlFkenOe1JGYij/qx8T\nVkw0tRfdlAywDQdUC7DiGJ+qIfG6qPAU7xDFblSNDRSj0J9LSX4lUHqycaca\nTMcFZNDeSLrO9IfUvW9HGoSQMhE5DdJf/JOFHE+xWjbmEZ+gZ8/HWBiQjBVP\ndsWx2Mg4/1bi8wCpE0BxmsTkw0zENDz6vsTxPI6skvSshlaI+1BaPFDnM6na\nbEm5fIztH+peFIX2vx+yTxb0hMF6lyeMlyq97EJTcSeq9p6PxWInpJcd4xvi\nt9K65o0O69ZjXTbJ4zrLoM9mfhojMmBmvINsHKZjBEwE+XUGdXGoRhPf8dZH\nXtihrt7MIAicz9VPGl9Q6ga1pLrc8srIijvGojzi57s4xPjnah6gU16/SMn+\nUgybzeiH4gHggw8Xcrwf5DihzydMo6cD84wqxLoPGYrCMHNyYCaDJt60iIaJ\nsrzrdI/gDT2S7zEtxdrNpmTbRFaXEvWOwg5QA14fPCtLfFPFlabG8u144vG3\nQZ7q\r\n=c3VK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.2.0-alpha.86_1585156570953_0.054631581519205774", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "jest-doc<PERSON>", "version": "25.2.0", "license": "MIT", "_id": "jest-doc<PERSON>@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b1b78e275131bcaa9a5722e663545ed949c278ee", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.2.0.tgz", "fileCount": 6, "integrity": "sha512-M7ZDbghaxFd2unWkyDFGLZDjPpIbDtEbICXSzwGrUBccFwVG/1dhLLAYX3D+98bFksaJuM0iMZGuIQUzKgnkQw==", "signatures": [{"sig": "MEQCIC+WqBZqw8X6ZxsjdoxUA/9GfeA6HP04fgA3Q2kDWiJpAiBsUEh5Scx12/2YMl38aWVCp017eFfgRVDJE7LLX7XyFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5ujCRA9TVsSAnZWagAAGeMQAJ3n32nxCeY0WhoYsMHz\nfxsC8LW7ZLH2ppibEXnXwZGQL3g6EH3swCgsod3zj98/EBNVMexzdZW0Mt05\n2uvmBqugr0rzUobunBE4KxlC61FqjqGG+qyqdm+6NjmOEXE2Yj8Z964hQPhV\nXndLOlxhCCd3cn0Dqz9c4M6ojd5tKNUC81VvaOZOCTtjcJa0g1X0gd9gKBtA\nKLWPZ6+/DQZGrS8WQuN3ZfDyxAUXyAQzrVxfjKSLiz0EPOE/mtreFl+4Z2vB\njpBGl25ZXxqx8EK2HrpEbnKYwDICYQ1zKAaLPQ6ems4MOejFfEm31Qfi0zgC\nKdZh09PkGjAAbwdLT5oXhPcVCjRALDTroV2b9Ii6pq4vGVAPjr230ljshfWF\nb5rKWuyqWOcDVPtxWYcynGMrmvXE7Xfyxri8Y5kO7cGJc/lM2KjefTRBA66G\n5SQP+igkaR2DPx23+r+ieSvZd3RfzNXGdOHYjWCj7tQRMBdU78ViUo/RK61g\nKOAOYnKOl2r+/IqaL08tHEXNTIOkFkKhNgeNYwcy939t1SSBl9Wjj0TicbJv\nKC1LJFkqop7R9e5vj5UbXY2X8AKuey2QSfYiDwww5iX0RxurzvIqc5VV5mUN\nRdYTmGHSjS8kX5OZ4xfveIGChKFRKBSiZnymIJL1Xm63CEAZL0T6ocbZLKXK\nLTVg\r\n=/BaU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.2.0_1585159074840_0.2619149853091376", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "jest-doc<PERSON>", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "jest-docblock@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bd79bf58e166615f7e5ccec33dce0feb4a52faaa", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.2.1-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-5Sk9QeZrnBhGJUO7KlzHPh6kjmchRT1Suq146Q7zJVj3jaearzQc4XcaE643oXEKbxoTM6bQ+S+mhRgSEZtkng==", "signatures": [{"sig": "MEUCIQCFVkso4jFN+o7Z8mfE+HHJDBskVx9RYvhYHx5U7uTBfgIgN01wNfQ2Uia53v2FwEdPCmm8m9u5mKCDwSWfaUzaEIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+lCRA9TVsSAnZWagAAb9MP/RHVuXtUz0HjNovi4Hdh\nzrhQA7BiR+M8ukGJtd16lYxmgV9/Hgf/WKw+Ky/QWiPPf+eH5LhrkpVzlsN8\nn3Z4JV3cIIqTU8MHBtVLjug5RCk133qpDbg2fQLOJalXAWngs14KqD4q0E/7\nATL8OBDSLrtP1/9Y0i9DdKJwEoGU3POJUo3TvsUwczXC+gW9pcjM5OkwNdHV\nZGC5A3ROqRpgW4pZhChI2orxSy6ANLL9mrrRspsnZzZvdRGesawxR1RxUyt6\n2akYCo3PGa7iDERBlAoKi5pglBguwh1M7JqziXu/sYWGnAqePPkuc5zxEVAA\nOchvnnx9Of+mxqlLTqTRyKW9a+IHogXuWtZ4tYYhWX8amEZ4UC8UFixbfwOO\nqta1ePa8sofejp39PoZYinrxcLAKoIsiIUWxQnZJYUK3ABdv0+rNZyuzrc9n\nLguISr+GrsMSy7bBFWZ/CuMq9+G7WqOXzeYn5sDMoSrae6KZ4Og1DyRknMfx\nSH9FLT7pj0X24mrQXOfzGJpiOUBsFZ8q5Ofdf08UtN3PDU+dmLJuRfE/58wz\nCot4zmhpnKsFW4nosQ9tomUMR4wFmRVThJfczNETeDp6K+XIU1FnnzsHuDCR\nw8b6pEVZgmQn1P5vpytgilSd+T6OMTgXRJT5BprCYYsMwBcJhe5oFGNtx+VD\n8rg+\r\n=DWDN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.2.1-alpha.1_1585209252761_0.7433017390459422", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "jest-doc<PERSON>", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "jest-docblock@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0ef5ac870f0263d6b66301d07d8fb1a5a05fd763", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.2.1-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-ud5w7LxzzN7pcGDZgur73OEuL4iMhD3vcMXDbugaR5WnVUlqjsRCtXlq53vO1eRBDBjU/xx9YSUfMn9LQjwzDg==", "signatures": [{"sig": "MEUCIQDGGf3Uc2H2fNfZsxb8pRzh6KlOZH+Px3IAh+FZOJffGwIgCJV0cGGV/mFlK1DMJZibKvswmotaPHQuzCg0jBOfOtM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGNtCRA9TVsSAnZWagAADxoP/3KBwIX7kz1JdHtT8bOF\nMGoHY6XxovGkvlnwHhAwemazsaCjra60wfYlcnE4DGOWRLoTobx/Q5H3GNHN\nqu1VexNNk1cXPKxZhHGC1VjN06ZzSrYhWplmIzi3NHWhndBtEALJ6Nx6wolS\njdC/UYyuFzxfmRbC7NOT+Cp9HjEAlnUEuxUHMhgE7Cd0IBB+UBcSKvLmlZgG\nvWspTs2zXcZL/t08ID8JwUk7wWGKZAZbPiPhtgEHZsNZPaqXZYwK05H5qx6I\nDDcwDX9ANWoLxEbNyk/pUwjv+SU8964w/FZGwuuVhcsdkPbzimMtktRmB1aQ\n19fIQehg7DkQCUpi7sFOtI/tp0MdeomfQRTtc6sAqyiW6NIjUR1jE98KTtZ9\nexXso3YzE603/+GMEL8jLXrMiG1syylqAEv1hw5YgSqnffg98/UQkGsTBKsc\n+aklrXL25z358vXtgmzh+sqONIoF8Zl3KkpZunInydP9yV/dcMNGq6dKStRL\nET4ud859NfGvEG0ukxwLo5bAGXZVdSAn2z3CAWL8CnigybThgHQfg77sQN+Z\nu/0puV/wE/7rLmP1psrFyr73MFtqeQhCLBEJv0OndGDujwovWjpzgqE6w4CU\novgKYZI5ZTruhm+uKenrpH1BAligIu9tQCSZZFLjh1tjHgHcDBD3YRGMNqbg\n/Vl6\r\n=rsY8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.2.1-alpha.2_1585210221539_0.6964604766967781", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "jest-doc<PERSON>", "version": "25.2.3", "license": "MIT", "_id": "jest-doc<PERSON>@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ac45280c43d59e7139f9fbe5896c6e0320c01ebb", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.2.3.tgz", "fileCount": 7, "integrity": "sha512-d3/tmjLLrH5fpRGmIm3oFa3vOaD/IjPxtXVOrfujpfJ9y1tCDB1x/tvunmdOVAyF03/xeMwburl6ITbiQT1mVA==", "signatures": [{"sig": "MEYCIQCYCi560kDWu7FXp3aCm+3utfOAhqzWvUhlWfI2CqY60AIhAPX8cb8Wbw5+xxIPgV44+/oPsfgd3WkP357f0XehcHQ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+JCRA9TVsSAnZWagAAtYgP/3GJuFRpuy/O8J1iuV99\nr8oh7ENf5FICw3lwJRoamgfXzph6U/oKRlboq05OviAP0YGXiGsHpsIs0/vn\ncIXKhst1kZbc+7kRP06u0/BUB44lEprTZ6LYzWJc1PGJ5h79j0/6z02vIoou\nfPCTYvzi2QSwtycvDeHhMf+79auYVyI2UT2WJjbZ3bM30Rofax5nDf/XDyIo\nmMYg4cByY/IyQY7XWtS3O7ptlwag/Q6HmLRtNpWCAuuczxUL2dMOQ5GEL08m\nfnHDW2xeje0MSNmGa3VgQ39WjABYm7upwRlobIHQkDqyQqWlHeZx/SZIwVZY\nE3+XIwYZzDoazcyiYry+jbafoHVzDPxHU4D19dpiokSScdQGD2tfBklTydYj\nI0sSZaz4ZjY+ohhYvoNJ/ciGwdAGmjFNLQqHFX7ArRm+681Z61HrGTSaDsiz\nKhqSYMhRkP8w4ZZ/m/G2rR88YTCJst8FN3nwQ5rYrfQ1T5bnROJI9o5LRRm9\nBW8CzH9YnGaCZYUcwDYdbjkEwd2Y9Fu7ZYDC4rcr59zIMfV6Ff1u1Cc1AEQ4\nLl3X4lirgPryJVvrRbyD02hQiIUMBTOVXli7Rx5JBF6nzUWawspk2VxA1O+r\nzVbx2xFt/98KyRhCoqy/ecuM5EnJLqBG/lX5gN72sSNIn0Om8eLpl2J+OxZj\ntvu/\r\n=ZVUR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.2.3_1585254281171_0.9900240777528733", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "jest-doc<PERSON>", "version": "25.2.6", "license": "MIT", "_id": "jest-docblock@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4b09f1e7b7d6b3f39242ef3647ac7106770f722b", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.2.6.tgz", "fileCount": 7, "integrity": "sha512-VAYrljEq0upq0oERfIaaNf28gC6p9gORndhHstCYF8NWGNQJnzoaU//S475IxfWMk4UjjVmS9rJKLe5Jjjbixw==", "signatures": [{"sig": "MEYCIQDOU3tgFK9j0QBHZFEPqLlUuPxVPiZT34ukPdw4sx3UdwIhAK9B2MzuKqkA/wPnwYOO3lWMCZ2/yqG1ShRch5Lg5kVc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb50CRA9TVsSAnZWagAAZQ4P/2Q7c49ecznEc+vtyWkf\nNkuWL0rkbafKYHyMUdeH/HV/PI+4NOsYqxvwyZgnESrpROgLojT7wxNbJcBC\nn371rfO2r/fhU6P7j9zNOWOWYJRjKFP7Xaef7KOB5ZU2+mQVDbTWzL4MDnnZ\nyZ0dBi6R8url0oObh7x3c6aamZ9vVvyzK+lmj/SiU/89wa4XMEFIorIYoL3b\nvbXlzaMB0yq7+Bp7xWpd7FnOFh+9jm5iVxrpJtm3eHp7npWKUEkR//JPcwcj\nS3dmXR5LOYFKMA+vgY8lwhSQEaFHV8EEd0+oY5vpkzkmtZTRSjqDQntgNuQ+\niOSKWuaPABQ0caEr8HDDmv2PkBj6Bdd0YKdkawHO9VLXNT/qdSWjRLXZbN1+\nahHQlb1b+F2BH/91vPmXGtrzRviMxzASJ5HvFCfioRHCqoDw6yFSvX/xbB2e\nq4fKbbfZgmyRBCNQmPbu6N9gWmiVzeEmX0AaPIlMToLls/NVq6otIYJ/Nu02\ns5A8qaBRb+aSoj4OX2zHAcQIgXbJ0EXeumIjFLmZ2I6mF/A2/u6qKbIfgIjy\nK7lN//hs9xQ2jLPxIXrhLWx1UwmqafOQTjSt/m+Aw1B7kYAbtBAFPHMBPGT9\nff6Ot+R58Fz0JcLIwjBeWrNVIo/zIRrAtcNWmHFxVSDOktLu6H1u3cuL3RDY\nxJOF\r\n=H243\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.2.6_1585823348030_0.3284774691536134", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "jest-doc<PERSON>", "version": "25.3.0", "license": "MIT", "_id": "jest-doc<PERSON>@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8b777a27e3477cd77a168c05290c471a575623ef", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-25.3.0.tgz", "fileCount": 7, "integrity": "sha512-aktF0kCar8+zxRHxQZwxMy70stc9R1mOmrLsT5VO3pIT0uzGRSDAXxSlz4NqQWpuLjPpuMhPRl7H+5FRsvIQAg==", "signatures": [{"sig": "MEYCIQCRdgk7LsYxeIuLfHcep5mrvgB5MXVaNsFBrgRhH8nzZwIhAKBcHqTr6Wtnec2C0NEqERiFsuQTjG7dE8k8y0LymKoK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc+6CRA9TVsSAnZWagAAJTYQAKJFRRnbrp+bi6hL+IlS\nzN7DWrqnNGdnTDkRmLyHXCCxG0PGwRP8Ru87If/crHPDE/hPZ7ZiIzMJKUIW\n0nHvnqxP4xSS8XnJ6HQ7IRXZTRWfa48oT3nQrUGuYY1la1E9l5nDSLFhDdxi\ny6yxfP7rIDrEeL4wAz306NTOsdJMmj9vMlEbtw+8XQSlF5zPltVlbNZR7BAJ\nYXvh8IsfGoVwBy1NURpLFXMD/2RvIqpdEUQAX4PLBdRdH9nuUcw1kyK6M1N4\ncJEYQ2XyXW2MU9t68bhplvn5RsOSltH1yzahaOjnkaGXwtAFJ81x84pUSUfu\nbM3LgBres9FnEOkIHl7svkcyc3IIYHrfnxin75r3zSGGpgxeidImoD0Lou/Q\nWswqE72/c7dZURw9jP5ZZ3+hFGJw82OghmSyD+phj8ESjY7aLnby3zRMA6Vf\nJV6JmDHSlds/JXD8Sy5EwBD8cYrZyti/YT25G97JGxDt56sq90+YOoBJxdul\nPocccEKzTSwZMbhLTM1PpXxr9MrwYkwXeL6C979clloDREbR7/f8TI4zKeE6\nItYMH18Ks+jFwd8RosWJpvc5TtWKsigG/t4bjIHGTukAUv9nn70xtadZClLL\nA+XHa7OEyotqwBzPfq1E5ciNIHC76hrKaY0M5gb4n1PMu6oMLDrHckWnvtSq\n/grj\r\n=Jm9R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_25.3.0_1586352057773_0.31066290370675054", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "jest-doc<PERSON>", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "jest-doc<PERSON>@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "40156b80323c0c6bc662c4b5d8546d77080000b7", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-26.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-VRtLjyVoPcYhfogT0lOZn4WTfPI6yLlW18aMtHbq6I0VoLv3EYiH0FbMqn6t2N5C3gqemDzSzn4AfW8cWbd91Q==", "signatures": [{"sig": "MEYCIQDnS15zuLyiu7gNtKn+9CNVIOW14QOAQ2/ODy/s/A80uAIhALO0ojsttmcRPBkHjfx9WhSW4fDPrgSGuoYhVPN39ZIH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPECRA9TVsSAnZWagAAaDwP/RxZLqbruJL4k2ghOtRe\niY6oxgRsFkjLcP4A6Y6qTXvTjVTu61OWL6O+tGkRUmv24f1KQKD/+biXO3hZ\nyVGsDgq46a+h8QcPC5tC2O/RkQSynTSDIGsi7MnDhUrS28mXuY7eMnSsE91R\n3Lg1Nee2MzlSvhjRYXj9UOweOo25mBtnW0cZZ2D3w9aYIBTbW6KZJmf3Qk7J\n/dnj0z5Yf2r6QNOzy7XD6juatmkU+OEzZBejNwuQcO3qKs7V60bBaxkgErNV\nXF7MljkWuAbN24pTMX0PUBPbRIXYrTfAkIpJ87GPsGH1wqo4iBlNklgNhZ3h\n4DO/YCUNyyGw1vh+z3MOb9tuWh0tv0e33lzpTrLTrpE8gflHhIIki5aIEndo\n23aDJPEDWIyHuDHU+QymFN4u0vqFjJsOkhF9g8BzXP+a+zCHeF4hS3iwvgdu\nhLB/ufGps10BcEi+wx6CiqPeXnRJG7qCEMuFxmCHwNRO2i3q7GdOPydk+cA9\nsRZ2aqC+u+WoiWIMA6n0oxaQglvkXCFEL3hDkkm6M6hG7WYr08HBrQ27yVHO\nL1HMYhcg3eerxGWbuuIDw2s5Rp/p9WovV7cq+tWRsjGN5neW70GVC54HrKsv\netzNIftvjSS7jnWzsx1WLyG/mlKXCN4mhxSd8L3UOZb0O2D+qiDcGRRstdxS\nKPdQ\r\n=IizJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_26.0.0-alpha.0_1588421571896_0.8862767760268666", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "jest-doc<PERSON>", "version": "26.0.0", "license": "MIT", "_id": "jest-doc<PERSON>@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3e2fa20899fc928cb13bd0ff68bd3711a36889b5", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-26.0.0.tgz", "fileCount": 5, "integrity": "sha512-RDZ4Iz3QbtRWycd8bUEPxQsTlYazfYn/h5R65Fc6gOfwozFhoImx+affzky/FFBuqISPTqjXomoIGJVKBWoo0w==", "signatures": [{"sig": "MEYCIQC9ZNUm8OkU74GoUEZIBdMMgi1NZHm0HkTpYgRCb7ZUlgIhAMVwvbv5o2NTTp0vztKhFeCNY7PzQw/qnmj88G1zF0f8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFZ4CRA9TVsSAnZWagAAzWQP/0oV0g3mKNHl0EnqG3jj\nOz93tIvJt4lvW1vApjjZyWpO5DYsczDPNK+BzgSiV3VmrL61s6sx2Xwv3fow\nk7C0tnaJynVGbN6hprq7CkBL2ChZ0uNhJOMg37DqRHGj6QBOEN6gII7a5EdB\nuWGvvKU+yVCuZ6k+3mjBpyJ4xtie6tpGeH4ViEOFGHZhr9Ny7w7NTfXvV6va\nUAzQFVLsA2Fw8uvJj3/JCKi91IkO3adaemKaVAROS+RaPYR0wB3GXRc6JnFF\ng+cmRuvJfSsh+lXJTZXZVngLSJf5wkHQzfGw7xFqz5zV54f3i2Xh2onXpkcI\naupF7Hx5yxMZyC7sHr7d7aBDya3cKaudWcMzI8+wGQR1GRGxwan1H14uDVYI\ny7+dY3pakFUssJEdl1ilH0pF+HvmsH/bxMf9rcHLVAvk+L5/M0KXKIWA/q0e\n+9NZfM42BMwKH5BwlRfEuPvSJZR4n/WoQyqBHm79ANqqDjuPi3lTuIiE8bZG\nLTgVwEYi1xX3XSBV6pJdP7VrooTdamu6AP/xqmuIqMAwmj0A1NWwr21RXUHJ\n9r+hU/Ny6JsIbJPd+QKZLymtkMMSzV8//lcLxL/+JyNQeSCKf0uYJqj9L8/n\nXKVEKQz8Oj888uc8q1meZXPgRiU2g8r8FAwxCHn+cpYkkweSGqYS/FW/vytU\nQojq\r\n=3u1m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_26.0.0_1588614776230_0.7088662178205924", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "jest-doc<PERSON>", "version": "27.0.0-next.0", "license": "MIT", "_id": "jest-doc<PERSON>@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c65a5d914d2108c70541ea195dc6fba8a32d28b4", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.0.0-next.0.tgz", "fileCount": 5, "integrity": "sha512-YZU1l+n0mo55VHY3+Mt6wsRxkaCV7iQ+Xhv1AtEQI1hrXmSeR0ligscyIy4rr8zA3Isfw9N62zREb605FZnKZg==", "signatures": [{"sig": "MEUCIQDoqZ7W3z+LgqA5HTH9liXr1XdtN/aNIeqCJDaFf1hlqwIgKpFuyfVtEG56TDbKHRIQ0jBg0y4byX/7pvC4hGKWgyQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8J0CRA9TVsSAnZWagAA6V8P/1Zt9OcAHN6PWz/rEl+u\nYKwPZwt0s5yBDIiFUzqNyBsdUiD8F8+VUB7OLtZ6i0nKV+ZxD+qr/7vvP3aI\nkXeIc0RJFNWNsqcZyV0xcpLMLYB+lXwUU9+RtatdLaloykPmy3N3F/oGdhA3\nYEJbqBadyS+eKzn3hwYuveOSE/z18hqA+oKt93sr5CTcn/t9QPxp6Ouq+PM6\nFdqQJp/bSgkFLyEsjL0t6s2Iy17vjV+xj43C9MAjbLWdPi8vmfn1CLSeH1s3\nJ/tsb5brK0UIh/AJq+K2G4LHHJ9SW6XwtezCTT0UInQQpZ/7Qn9P5k8NGs8+\nhUsWZut56TSlMMdFq7myYnAjr14NVtnUCoXSexIek4UfopK97FwmzCvtbWFA\nH+BNqLalYoJUM8fugZixk5rHfsXfsbTtrJKf8cNBCQ085bBHQaToccjt5n2i\nzOunLzwpSzocXnWfbMk8CDQOAgynpkv+e8/cJBBisIX0/f6MwZN/31U1rBFx\nKFJNzAbyitvA28xUOGDnmZO1B/z5UmVfR7gFYPKt4SwbkXPmqnioIrVIlZLp\nJj2itvbXM/nmctiQ4NR6X1CtFJGF21eFL/7SkBo09xuhpIuhWhGBkut5/URa\npHgTef9nf+7wcvPOAkMtbGGgYTQecAGsAagnqwIcQqzEvglJmtmZ9Kg5d/s9\nesOw\r\n=nvos\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_27.0.0-next.0_1607189107497_0.5339460398334825", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "jest-doc<PERSON>", "version": "27.0.0-next.10", "license": "MIT", "_id": "jest-doc<PERSON>@27.0.0-next.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "690fad00076503c4339386b7de8a6745a95327d9", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.0.0-next.10.tgz", "fileCount": 5, "integrity": "sha512-qTaIJvj091zgf9uoXUC2W+3xZrvo4qrsIlCh0U8BbWlr4e3ZNJHS9pCRpETmF8rcmIDRnfZn8jJjt83hgAzpUA==", "signatures": [{"sig": "MEYCIQCvKsNeejE7hKvKOd3pciR8xXYp0s75qIx9nPn8jlHggwIhANmWO4cE5UAj+oD4x5QKhQKXkVuwcFEjD7O2PJkCnflB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4ACRA9TVsSAnZWagAAJb8P/2DiaTsae9PacKOqB77D\nTsqu2JPh+qQ1oIyiNyPT+gvYeW6Nl7UAPdvgJi22hyus0+bfW5j9vG8ejIMm\noeeBo9F4OOgwZtr8y8I30zwj7cd3ouR0oBj+AlJX0v8EJ3p+86QJdrmjterI\nPC3+MNHk0JW7zjOBQVFgA20lihbi+HZyHsyV0gBGUCNDw6+khxyDYPcYdhdh\nf8W03oE/PjOQYwdwzd5kIONqgGrI4hYO6dsZVBX/Weiwv7ulTXYOO1NhtsSK\n6cCi+jzgNtR6r4VmXa2a1IPM6WqckZWKIP6ztxebSAFWh+FAsFHhprTz+0r1\n+0igGQYYcKuZKiwVw7f3nbzjZ74mujOW/LWAmGBPNb/NeG3avGj62k1h/T9y\nYrPvnx148W/Yjj4N1UzH/U6MT6/YWnX5Wz7t3hXFmN9QP654AOW6ONMDVsUE\n7PaXUU/vFuPTPsnisaCP/04hxsyBZXx6Z9vSrvAmwGI0tdbuDi8BEs6XGjvs\nfh/dxiEWhVOeuElVGvyrz1nebKhnx2VSwNMC7KQZaJEvprZlCLR9ygioeVoz\n0JG0a9p4DbBRkaE2XPFkatlCnCaTMT0/yQr/FN9N3bNnzMpIZD3FnfK725Bk\nGvusKIlcUqCfZ9GhRhNM0CIT0M7srj9TKZTAGo4ccWJMh4OOCsuZfxpeOAk1\nUC42\r\n=1zA0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_27.0.0-next.10_1621519872133_0.8513620512657898", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "jest-doc<PERSON>", "version": "27.0.1", "license": "MIT", "_id": "jest-doc<PERSON>@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bd9752819b49fa4fab1a50b73eb58c653b962e8b", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.0.1.tgz", "fileCount": 5, "integrity": "sha512-TA4+21s3oebURc7VgFV4r7ltdIJ5rtBH1E3Tbovcg7AV+oLfD5DcJ2V2vJ5zFA9sL5CFd/d2D6IpsAeSheEdrA==", "signatures": [{"sig": "MEUCIFOqddIWt2Ywwcr5EcNl/DS27BfwBqgTokxtwh+2oxSrAiEA52NPwSy+v4RS21DomeaXjAq0fjk+JDYHl/Mtbm7iqwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwgCRA9TVsSAnZWagAAKU0QAIRY+xD4c6RPF/jH4Rrx\nRbPE74z7kdYk2PrH6FHrj0un0+PlQqNRSZenKILpq6n9y8R63F79e0N+oWQc\nNxLvUfCkkIISCPHPFKwmvvX2czMDX0amSSnp4AIgh+3eoLJwjjk4Y7ZM76EB\nHG42AoCgSMF9i8gbjqzXgHW3jfm9I9k4ksr49TMSkJfBGtMuPc+GKIAwbFzg\nYKaUDm6G7z5KGGUBjxoqbiR94ybpbTcd3MN2wuPRMlvs9Z7Klfo3f1vs9leP\na4mzopW7HCXN4o0WSaMIn51LnubKQ6HNWeCMc5TOLAdMbWfBJ4dIKnQnpx2E\n106r4vd0DfP5LxtmvrYu7VAg2KUbWRWBtLkvdFId2PY9ZTHLRm4Gs0lZHvZ9\n3PTR/xl3Hwim+DAqYHqwosLKjOivTSpOHe/hKPmHMH8eOqOK9DtUQoB19QU0\nPQteHhiXdZiXAZJqmqomMiUnSHDpsjX71EKgKHHNw7uoW97sl0pagN3vwWnu\n0/stXcWs8FJYGtMKrXVkwijym+Pvto2kvq62vrG3MP6POxG0IC8FdPfUeSzo\nH7372xdEwkL/V0RMRfwc2NW06r/0mGq4iVHP7ptqWG9/JTiW9OFBZ67g4X3S\nyMzP8hqBci1Ie0dYk1Sfa6I9/kApytKXbyoDJRnytU5bmeFndggxtWtUmZrP\nsUdy\r\n=18oe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_27.0.1_1621937183784_0.6576070457212186", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "jest-doc<PERSON>", "version": "27.0.6", "license": "MIT", "_id": "jest-doc<PERSON>@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cc78266acf7fe693ca462cbbda0ea4e639e4e5f3", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.0.6.tgz", "fileCount": 5, "integrity": "sha512-Fid6dPcjwepTFraz0YxIMCi7dejjJ/KL9FBjPYhBp4Sv1Y9PdhImlKZqYU555BlN4TQKaTc+F2Av1z+anVyGkA==", "signatures": [{"sig": "MEUCIATQRSZ/su6aPxLC2SrKHgcnUDs+2pr8RIsT+I4nEKgaAiEAmMvbwZIkeuh/qZJnRzA08hsvAVRit0Bm0VWRLPYeX9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFbCRA9TVsSAnZWagAAZ5wP/2RSOoCt2k78kaWq70nq\ngBU4Ej088s4CI8sWpb9j9OFte5/dccB11vwa7Jz4QxEg+lIaB9N5dKdQ3QKK\nsUqOQmEAPGW7hXtCWw/RvAH3MwEmvQmgCVsQ3v57Bs73x9RCFUfDWEnx1rdG\nRcHcDBq/f5sW5/x2aWJ5+R8+x+JrUU4gma6lA46iKKR/44da0O9hXD2WTkCm\nOm4pHMAxA4szWIDnDDakQGnfZ3po4rLKMFKGYZ1B43QwfeniZtnEiKpfcpN4\nZea2lYVVy7Sd54JH3pbuXy+EAb3/nYwAeUttsSDBpCXCCBAT3H+tDMGSd2iP\n6Wa1aWBdn5pVu0yUWMfihfyZiVFa1WuGkbFSJdSLgTOZfWF8xriYMSMGYbUA\nVRECa2Xt/dbwvVWivAn+1x1IahJHR3GzAtzEVRqWfR9n4YIAgB2UDRkYuBdX\nHbRzsBXmDIVhee3jgha+rQU4G7d4xcj+X30mrCiZBUJzYSSeg49ycMJ+caQK\nOCftcraqGcua7XW3g/JuwpeQL4AQ4IPGl8fP7GnXNki6qWTdOwXX73VR2o3P\nQzx6fExZwoQEd544xMYHva91UJcaSqc2i0wP8A22YHi6GLcZcQwhxM5AcYFR\nkuvKdNPy8DastS2zCyrtmxU+Mm6qbM5vO5W18O4RfDpQomT+RLXACYJEPr+N\nY2f7\r\n=fa9U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_27.0.6_1624899931213_0.9796901051384657", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "jest-doc<PERSON>", "version": "27.4.0", "license": "MIT", "_id": "jest-doc<PERSON>@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "06c78035ca93cbbb84faf8fce64deae79a59f69f", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.4.0.tgz", "fileCount": 5, "integrity": "sha512-7TBazUdCKGV7svZ+gh7C8esAnweJoG+SvcF6Cjqj4l17zA2q1cMwx2JObSioubk317H+cjcHgP+7fTs60paulg==", "signatures": [{"sig": "MEUCIB5KFWbUz1cnAUkleyAwJOLLQT/y5sibvpNx1tNNKUMaAiEA8EUYvFPvyQCj6cRA+v2eAb/9dBMGujZlYYzwG38DGwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd2CRA9TVsSAnZWagAA/asP/1ANH2+zjQZd4Cs2Dm5l\n6awybY3GqzQZE1tw8RGIFMII2OasTJRTmBrYPSFBs8/UNjpimn2PRkyAXM2K\ncrGBjBhU5LGtNxxeiBA79IaKEuV94Iuk/3jtzp1rlMZaYthG/YTass65NM/3\nK4v+UJcqx23KakHokG2gNQLD19BrRGWwQdGgS6DWpNNdZiCWvAWsKHDc95An\nlBJyytG0z81ZCV8RZ3E8qb+/5Wm+PX/8oeWPNyhb9YzIfIejFRE3capvQnZ0\nFmbLVQZmoa6cq4Ow2ymgwAwwCc5nARydhaNmubEc72KDWYeW//UCWbBin8xg\ngWfhBSXjerWsF+UzyZIzv3i8+zvtAcOmTuFZTd3LdOBKsqm1rS0ciVeYS+/7\nbppi9TDSFq8qxaBl90cK8dxLWltt7fk7i0pCyyzQZHsSLWqvoivIs435SbHZ\nesZzW1tKeeJAnedJF8v58WOQGbhr6U2EJvf7u+kiTKiC3pSZHyipFsE8HsyK\ndwAPqUHs7bfoMx9FW9jBn/9YheqvfBTCMl1N2fszYM5Y5V8XtRl+Vz/YcWeM\nXEXSZF0UdWhQMVUI91TCe2MOn2TNzhCIHNEEUIYZstKp8/51FkbJnknosIpn\no411G2nIXHofoOOH3z+eDndYx/9DfM6P65QnMIU7Br245b+9fE/jvpXWoPmT\nEILl\r\n=g1LJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_27.4.0_1638193014661_0.48127315781708724", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "jest-doc<PERSON>", "version": "27.5.0", "license": "MIT", "_id": "jest-doc<PERSON>@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "096fa3a8b55d019a954ef7cc205c791bf94b2352", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.5.0.tgz", "fileCount": 5, "integrity": "sha512-U4MtJgdZn2x+jpPzd7NAYvDmgJAA5h9QxVAwsyuH7IymGzY8VGHhAkHcIGOmtmdC61ORLxCbEhj6fCJsaCWzXA==", "signatures": [{"sig": "MEUCIQCvBIC9++Hz5mgbAj+SuDA4zuhh3biyAKIySFDAklBtCgIgNAUgCKTELqZgNi0Yf3XlmD3zHj10bplWYJxLhhFjbWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp1CRA9TVsSAnZWagAAN/MP/jQFCRFeRmAiTbscEYYr\nWwrLDDt5fS/A5kSlAQCVmR4eMEVXW00EsQzdgAZ0CAuXOxYfUvLSRh/Sagvt\ni3xaQ5TWxezQwoCxqt0tl7GJmPGUMDyu/G3AGZWJQ74O3R7k7mbZEgWbLKkp\n9iZwqO2eAtr+bg4mv6wW+1D/Dtwtm6cmSCv7NpuRYiLkC86wXS84uBxqavN8\nUpnxqOz/qR5IKkXUkXOWdQvJK6tb8lg7Uos7g/u29K0WKEsTZ43jlZFXKM2y\nvIxL1Kd0xCxrsR4dOjy/6cnxtKCkrHs9dbpxRzHSG1JbkMsn00rTYog0YTtL\niLBsv8tyi0xVRZD32RxF9Y8zHaMM7MTZRcKYvXCbg25HisNMmX22BKFW05Mi\nFV3cJ8/rVnOSBofqFQrhmnBfy8Q/fewDvDra2xH8VCEtObUGthtS4ayOO810\nHygdol5WI2xs9avO0XsDpRsvVODbxU+nZ8EZNvwph+XOqR2b+oiGgqOT5GlZ\nYHke/+aQ/oKc6rlrfFNHzEtMAQjDs0X93ePE9cASoM2Orp201PnPFYEcwvvX\n5cs2SYh7GQ1RLj6cQp1cggRWax8sBS5dHQM70IcOARsAnIYM8XutjCf/XHJ7\n44Tg+/CYi7asU1SApeNwr/plxk/a96zIK+sFRf+2VRTcQTz+uhW5O6susK30\nqsPH\r\n=NX6T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_27.5.0_1644055157741_0.44295075307886855", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "jest-doc<PERSON>", "version": "27.5.1", "license": "MIT", "_id": "jest-doc<PERSON>@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "14092f364a42c6108d42c33c8cf30e058e25f6c0", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.5.1.tgz", "fileCount": 5, "integrity": "sha512-rl7hlABeTsRYxKiUfpHrQrG4e2obOiTQWfMEH3PxPjOtdsfLQO4ReWSZaQ7DETm4xu07rl4q/h4zcKXyU0/OzQ==", "signatures": [{"sig": "MEYCIQD2akShGK73C8sJz/E3+UDLR2AxfXL8+LN7kaNtz24eGQIhAOd2reB8PEwexceF2zs92qtgMVzl+q/DbWyw61LGGCsD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktcCRA9TVsSAnZWagAAR1QP/2TTQeBNR+cnHt98cHPX\nZrV3p+W44JIIGssGeTbwnEdj5gM+XaZnK4HPDcGguCDOgLQAAD4jDRyYX6aD\n5HUzE8zbJbhRy+nxWmWdej9mwa2Woc5+kZv7QZBM/19kGoDNL7ysqVrjQylo\nThMGhSQKUCuMz6XpH+2XUcpRQNL1gJHBtQvXWf2qs4ZACMU3jTYZwWs2W3Rv\no/vbQkYCI+qyz+qqtJ+y2W2DQyonkJRc8i8anq3uBE3ywYiTMxw9Maxpgups\nG94NDR04Rqp6H1j7QnR3WbMzHvHzxaxyvSc4iVQ8BrziodaWaXHIrV2NcTQb\n+tZg5wD9XLGXvfphH5wQWNZlZgoxzFq4+CZ6CYOmxtDedeNvGgzxNWgOJOhV\nQYXfUOPoUNn6M2/37Y25Wy75xqOEBhqGOijTP4j3DCDXPcgRpoYM/BHSBWBt\nltuG1TL3mg6BZT76Mklsrww049Elt5mP67GvmGbAW/6VZLCLj5gNFgTH0oit\nSQ0hm0bg86Ym6L19j6bk4EwKY6b8iu3uukDqupFQzRpk9VbDN1Yj02icpuqN\nH2sDBPlTGpepPjWJExCaWr/MQekgUdFeUqep7hZ6ukwJYCzhfnXZstK7VbvI\nyCyTTGcTYNXjqmOdrmYT0FlF2OnN7H+xBJwTjRVUn32Ga3oDhlvmp3XP1/h+\n0SD/\r\n=uIPo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_27.5.1_1644317531932_0.2688906891588452", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "jest-doc<PERSON>", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "jest-doc<PERSON>@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8432f49b4375ee504388329d32f27e6fe9e4b259", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-28.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-tYQ7At4gBNjBhVWLFspYO95PLtPy0xMOMQNbkrrxDCoDNEHZP2UVRDUryZ6TZceS0lp3LXT1gwsnSKoLjYYANQ==", "signatures": [{"sig": "MEYCIQCC73tf4gLtsHBELcIHDfysdZVbbLOoOzhxmkx7wksvBgIhAOBY/G8kpRzIX8EH9loGk21cypfinFAKSgK8ak+BVCxq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa2CRA9TVsSAnZWagAAsnoP/0w7wZxyDiwhA7DkKP/S\nf10gs02bxWOMV20q4S9OpsEPKJxWD3aId/wI7lcVfZ90esMacrhBaeVMZJZn\nJHdp2SkxNWm48ziLW+1a24aLNFBQPhqAkykgegxt4DLWvI9uS8BGI/reHriF\nx0LpJm6N0xZvdpHlBcbhnBQeFvEy10y8dwOOtSbHRP9Hob5CFA6CvOe7rWnN\n3L0P6xPX5Y12MIXZGlUzn37wTlqJYh6cs3XlYD7BPDy5fh60mgzfazLgWF/E\nY6CC/feBW6jhFDZubfM608fKyC+ShBDD9RsFzhlc8CO7hbGvmZk7nMXJEi6n\nP0uszz7yNf3LfTXPyHIsLngAvWlpAhaCoApc03npWUbrz++ze982gY5y4h4p\niRf6XJhQtRz2nD2sBShIPA8GAQGy+2YzzkSedve/ovM/4FjBCj/h+eAgCgSV\nsFSLvf6YdAHYvGkD6BEhA2DOrdY6IK44xP7bu1ZkoAgGZrdzgIMvwWnI2Hiv\njSkGgJAKiMifQAEu5tEVIwswWtJ+F0GftinNfNpJ8LkWLmfJDgPpaCRPqGSQ\nzulD6mDJuSp9FOLpWYuTg2y/ZrJDLW/oRMTwROe5uLKLXxTkvOgBMypC4LAS\n4V5Ouf6s60HpUjZtMY9GB9axlAHn1rC2ZAAKuXZJsYI/3ztXi+Wkqybcg3tJ\noxOn\r\n=y018\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_28.0.0-alpha.0_1644517046361_0.14727135094447363", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "jest-doc<PERSON>", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "jest-doc<PERSON>@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "48ce331e559dd06514150c73196a46d4a4bec018", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-28.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-JzMrVYHWHyPNHB7Mb2uU4n20muXLPcj27cIOEPxJa+HzLkhIHRpggqucTwescdudRyh7Mt2efGEbE893VoV8Hw==", "signatures": [{"sig": "MEYCIQDdzqg36koZUbRCQA50PqSrxCLaxXSWirP424w4oVQw0QIhANWUtJo7DVbdyD6FgXpbWT39PgHJcA7bDZwHKWl2Zkz3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLXg/9F1cBpmGPKWTjO970rB124uRWwJL5dThd16TJT0Ee2iYAwAeS\r\nyWTT3bjUeGpBLVkaUpf77YtNx+pDJUyCOXH88VSBJ0GwdlOq6eQ0PIVt8SSH\r\nO7PvxkZ+LkuUUEnPC6EwD1F4qbn/0SvPZAL4kzbNh/QLqiz63RFIoI0swUyo\r\nDG1HRHSZFWeFAfQ1DzjvPsQ7osHPrYLo4n6181HOlOnvQ4/dG/Bxfz9VsKBl\r\nO8lq433y/GpXHQBWe4IZ280y50hubX+ph1eWmOa1VomgypoS6wIuuX1okiS0\r\ngzT8x1RdDzfBlP9AlknLMyO4++tDLnQHddFsQhErHTCEz5C4824PwpEZ79WR\r\nkkD71Dvig4XqFhCfYfxlE8D0GQCDV54+hmwsHG/WKe//ubZkQMoi9l5jXJmJ\r\noddGY80iflOw2nHGXWtuOUbdpkK329a/PAj2vVrM1L5CIGtV4SQ8oagiQ4kr\r\ntuqiASoWudidpQ5gunOYCBjHG2XO60XB/8yizaixt3zG0Vi+95aLIeBN8iAw\r\nPTRVXvCfYtuCo3rJZyi+5ZcTx0wZFlL9MTRXDDR6WSBLOzqs46O1K+WiFrD5\r\ntDYTAHS3DzfdDsoRNYs86flLQleaMG1aijuFIOzWOG073XEJRj2JBxHrrerd\r\nVhSPzom8nUyV6/PcBbYz97ZUFeCsxkv0HRE=\r\n=wB3f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_28.0.0-alpha.3_1645112539978_0.7928016773689945", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "jest-doc<PERSON>", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "jest-doc<PERSON>@28.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d03b691efadfe25844281671bae13d97b7d7ea34", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-28.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-vtm7Peh2zec3BI/0GlceeznjWEzN0Nm8uVjnFgjVbaY+YgmnMxKkChY9+oLHxrOd127k51BspVe4Ts3Q2/btTw==", "signatures": [{"sig": "MEUCIQCRmwwQjFinMO/CnCxz3pqymW2N3NM/8WZAStCabm7iWAIgRlTFz+etSHW+Q3MRhkDohCUadXIj+ReKmHS0PoydeTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmponA/+LZUebAD/VmpQTHT85ukWAbh0FUX5JL+MHDOxi3JN6Y06ZBYY\r\nuwS7dBAZijKzl1Fc/MYAFpRWrcZNd/EdH3akq1YlG//AgvT9a1Z4cX4wpWhU\r\nS3zi4jL+IUp8A5+5lK8M33Gz26tRHrLqb0kDaD2ArISOUdsMZt1bPa5rxmNx\r\nTjX8MjKPqbWpaBpsUWBUJfAyMsi8fTJsxwPnsjSANXPLxa+aAiwy84AJSetS\r\nA3G/h6YfzVR22eTFvmSLpYLEy7xJ2nnjm+YVM+iMjUR7/khQyk2dg+Q0tb95\r\n4STSOTWT1tUn1FatA9DgQmcGtOqkdZjHMzqacUwpwdRHL2qo9igaSYxeDn+u\r\nd2nGQd3oRUvVPMe1QP1Ab5gljKxKF6sNROyvLAkfwEjgp92fTMHbPxBaLo45\r\nVPqPQXKffvansJMfLMGW+ysWgPlsCABlHkKGvCUzDGPHbH7wkxbedYkUn4l7\r\nrF8aX2TOBupteyYG4my7l2gMRcKLueJ1F3p3WuzBW5ZM68gubSFYE4dW1NOd\r\nMEfAX/l1DrG0/c8gY4hcSr1G7uOwSl3SIdB8nL94Jk97VpX3ysbRSJ0rEvFD\r\n6FcL8myXnCbxCLquw7aML2x4liRmhBDVMB4DaKLqbqOad8FnIWXQ7KxvByJq\r\nuuec2vdTtuoOuHfKRcwnYpC2HIwpkweSY7s=\r\n=ijau\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_28.0.0-alpha.6_1646123542300_0.7334649617780589", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "jest-doc<PERSON>", "version": "28.0.0", "license": "MIT", "_id": "jest-doc<PERSON>@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7f39dc998da47dac243157fbe7fbc1e993ab0eb8", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-28.0.0.tgz", "fileCount": 5, "integrity": "sha512-88od+z1QkHyvtpj1gRA6QGysopOzImocHNNlvvM7OydDe9ER6z1siLtHJXbKEfi5FoxMpYqDtszYIS50JVs0WA==", "signatures": [{"sig": "MEYCIQCSNgNkqpQYUq2NMTOCouJmz7F+mWnEVufs5KXRcttV2QIhAMB6nbxnjvKi7YcrjbmNz+qGvwqkDYganiNDJ6Jxvylf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqrkg/+JL0N3d3Z9XdGLE1HW+ht1o44q8PDpCp8dfms6vnuNSX5YwAq\r\nUC7y4qsCCbUCeS6iGk/DwfU9HlMObVNPE9nCHn+UfeZrYkqkiddM1oUcKEqh\r\nlF+Uilr9xumM8oyHrBOgOwlXqNThwaS7Qo9LBohQzsloLMzxnL27S7SlQXuT\r\nPvdsDU2vn1ndZNfTgGIg1aTB49nKseYf5/8H2YBwKj3QxlCQqQyGfYkeunRt\r\nTgyQWNmjYJzPvheq85IZgpdqJ2H02no6gxFGhuCWX2PUd0kY5pDwvKV87Zh2\r\ne0e2xrnOdmJNQ8PJICB/Xkecrm9FN/yUQ7EXWMdC9Amggf4b3qhqZqyWFpRD\r\nQEwWcnem9NKTUaReYm2k/BAv19bJecVb5KLgTxQWMYwHxKj8XEhdPB7qAUNA\r\nJPYVh1mraJRmG70u5ERGbztiiz/KUXJKZvSVdPSZwb/ZS1dmkGMJZ058yRKv\r\n88l/m7L8aUo627DONptE2tOtC3TNAfdVKBAnByVRsld8iTovYjZARMbsle9P\r\nUlOBFDEqd3KZ7o3UMcGkouzGGIFP12fhZfK8K82IjJU7XE5wk69EinKawVFV\r\nJ/WiKzvkaLzyoCHmtCJdpzTTYVAbb+IM7wpxSxUIoTi9PijLH3bcQYwoFnAJ\r\nd4uXhn13iIzCYy3zRkelzfQ8oWQAwUqygWA=\r\n=xGr6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_28.0.0_1650888483010_0.4671647473750611", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "jest-doc<PERSON>", "version": "28.0.2", "license": "MIT", "_id": "jest-doc<PERSON>@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3cab8abea53275c9d670cdca814fc89fba1298c2", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-28.0.2.tgz", "fileCount": 5, "integrity": "sha512-FH10WWw5NxLoeSdQlJwu+MTiv60aXV/t8KEwIRGEv74WARE1cXIqh1vGdy2CraHuWOOrnzTWj/azQKqW4fO7xg==", "signatures": [{"sig": "MEQCIHY6gicQFX8yEYrlk3XF+/Iz8f5tVqNgF1OZlu8vFVTRAiA1ImBy8FEAttf3SHrjtfUpmfcyQXiFPw7ZEDwCX0ONYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9sg//ZXLiM/VuPGzOEz/0FUVC8/OgR//q/f+qO4O6QpIXWi4U/1FG\r\nuhRF2HDOll50WDUqm4hTD/PClvKiifPcHQWtEvwzzMAkDoA41st3ZZJAc8Pz\r\nZxUC/TZriIP7bQdxydPuf9QT+RETMueq6iA7U7QmGRogqIOiMLQ/t6Fdr8ym\r\nMYQn9PPv4UVS+mECHPf+n2MhAPp5mKAz/igLsl5avidTRpNg405vUfRsSNag\r\nY2d77Py1mwDQn0AX+Ze1ZFsEwXekhlGnjunA+aj1PkXWWsKSvgq20xgxrWtb\r\nLPD1z6ZYjFO+spKFNQN6f7XmadCq61uUxF0IXLCh7WNkhquskPkTIbuYGHHk\r\nzX9PaHGIUzRegYad+ajBiYbRU7fzS++hV/8+s/YbPCrtto9vMZDxDRU0GjTP\r\nwEwJfKDXgQs0YqlF/EG1BaCm+N4kzFsQ1FhQ7jsjqgImPRpApm/Dojz/Yhjv\r\n1rHneI76gHPuFDExdl88tJv1+B49BHdXMWG9lBF4JwkMGZ3y1sk7CdEvK190\r\nqCDZ7i2C9kWsSf12NxDfD2qVZRE3NOWVvKWD4DLm5yFPMx9nmra7IminGgrI\r\nSkwahUplvZt65Xp1EuuPMinVQ2MfXRyReuLlEYVJVBq7QBZ6o+yIhx1c9V4m\r\n8u+MFSbqc53dCsM/HlDAB95eij4iLjPxKxc=\r\n=aadj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_28.0.2_1651045440008_0.8569264813941531", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "jest-doc<PERSON>", "version": "28.1.1", "license": "MIT", "_id": "jest-doc<PERSON>@28.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6f515c3bf841516d82ecd57a62eed9204c2f42a8", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-28.1.1.tgz", "fileCount": 5, "integrity": "sha512-3wayBVNiOYx0cwAbl9rwm5kKFP8yHH3d/fkEaL02NPTkDojPtheGB7HZSFY4wzX+DxyrvhXz0KSCVksmCknCuA==", "signatures": [{"sig": "MEUCIHYYa0skTst+qw6C75y9rAtixfEBvt0hh2WlN6wRnHHyAiEAzdG1o/LetniNfngOmgUvLTLsIwbH9t7/yBUM674Mf7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuufACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDLg//dda2dxx9LddjEuyvpXuckj7XWsNnDh6IjKPgDSr6p5CVLLnL\r\nbEaUwJlWn1rirecabDyyL3u7IqaVjZL4clvvGMvmDCwbLIurWTFYgcePmpQT\r\nXLJyGuP4dI1F4xdqDWHpJr5ZAp6WwY0jf53SvhK8CiCWLUjAUSCgCxx/9Vhw\r\nMx8rh0GQq934CxcsT4saOnpLAzH2hO3C2C1b+v2WkiSlxGvPgLZFhdNry8nJ\r\ntti1xMjDrhZxzKFzk18BF2AkcO+bVnzRaryqQ2WEd0X1f4AQkOyiuF5QCSGq\r\n2nhD0RHeh8TQ953hmZCzyFe2lIaDQFSIQSs+jFJO+WdE+JtVwxhX4xbDdJjK\r\nYJx4cBQ392AsTxCpZzUuNOrpkxVO160sjNXZKHuCyojn55ZkaLX9+1+ri/db\r\nxRwgGt3x48zEKaodcmqFPQPyPjTzFbAhztQj2+7fcHhQf0M4hcNc6LXlXQOZ\r\nkiZUFILxsWFFg4ZThdYDnAnzC+9GPl2zY3UlSCDDXb3eGERrSDf02unMSpOw\r\nIYwYdgxKqrv7srj4QlNzwxWZ94pqy0G2kvsmMJDfPfOnxHyFKpQ/ThdDWsMG\r\nbopjHF1616DJ7s7828zGCsp2bOntChPptSTXqhH3e4qDP+gNCuJfIjmpxd65\r\nRJTM0y/jHWBddIrRijnb3B0l11UVx3Bj17Q=\r\n=0jGy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_28.1.1_1654582175377_0.6119411582261309", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "jest-doc<PERSON>", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "jest-doc<PERSON>@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6182cf89756cf43f722d9bdc7b0a5b16b0fef03e", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-3IAojM2wQMmx/a8ZZRZbLu6gzoYLb7oTv+XsjbLs9GjP4yB3BhLqR9nBve+qk0dMAYAmLzh2j8p/MaybH371yw==", "signatures": [{"sig": "MEUCIFF7uEUN9vsKYtre8Kir/VlNg5CS8X+wUrcCjFULLMj5AiEAuhnto7hUauypkFKsMM2DEd1zZS9Hze9PkTOej7TEFRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYHQ/9GVf5yYHTl95rfXc6R3sqTC51Xc6O+12ZcfFqdhYRDNsLaxTn\r\nYrnNGlKGF2PvLN13ItlID9/E8LCLjMcnGMc6UPs1uxi4IAKKKTmIFC97J7XU\r\naZk6iKKNWRDrMBfT6xpoNyFECV4LdAnZnpUrRAIGfNK8z+rZoh2kYefpvOzS\r\n+NG099Ndyyc3nVqd14cMvupUs85XJY1pVgpnWxlhPopDz5Ad2mF/BYckuOQc\r\nJAB4/Muok9R8RkADvEthaeQv0ub7GrvQSi2pFQKCCA/KW7uHWDkYeYUSxjAR\r\ntf0yP7x65wjWyU30ey+VdwNCzjE0GHULurtEHlCpuCZ2cmssafaPy7UfZ9KA\r\nM7NfB0KFtkbJBVyCqCrwsbTN1IBMm9tDVnxmOKuZM8iPNiC1sHBsXgVNXIzZ\r\nVJaeLaxTIf3Lb3KeN0frE9CxvqBE55QHExdjUFAAxTF4Ay/HmwqzqJlCChD5\r\ntDJAJa8tKMWcd3VcFUYythCzJMG1zUwvfkw3dkQqDvGzqGvjNxtZRW0+YZ8g\r\nkRZjJennEau+DnrjIzpxmvXaHJjw3eJNKTl4CF71McE6/c9ZlwmvQgkGkM0a\r\n+KLxcGEnjEi0pcX8zW4nRAygeBw0BhXHJSHuq5elVVwJJIGcwBmcHyAw2Re+\r\niiQCK1YOR7XqRfbOKOScEewlJdLLapQfdmM=\r\n=DHsg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.0.0-alpha.0_1658095625746_0.057603445851248436", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "jest-doc<PERSON>", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "jest-doc<PERSON>@29.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f71bb4c5c739e56978a2be09c1b0bc4f1c5e2868", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-He8ldTffZ4FDvbUMfvtPxUrj8V63hl4Y0or/F9PTWkspobCwRxtTFRD/M0kkOWcnQ1TZAs0VRxDvzeBpw/xPCw==", "signatures": [{"sig": "MEUCIGGLzcEmjvKJaRKkZBwExiU0Z2dboxEdHDdTw6+********************************+4XBhUmWYQ8myW5o4fHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbDg/9HdVH/9zAf/PnOhSPfUJ0yonI/TRqMeXnCqz2Qp0yoDsow7OY\r\nH1zPefZYky06vTNnNuKxlgH0UFj04bujhSJ01Im6pcwCCXRlsmqyKlxHQyFv\r\nBg173kL5Khscpkw+kPUhDRXnMV15CRd7cNR+MaPb4QNkUrw5Q4hMqW+RusL/\r\ncIY8sLFy/SLFRroaHBMxT40KhEi94zX4/MGvjU9tk1eRWl54+S8EwLqRkoqW\r\nmFemPyI1n0CQBCQ3fwywbdYuOJCdPpVIAQw/XNW1h2f3yHVPICycrdXK/q/9\r\nMe7aS+l4EuN9Q5vbsBXOpO1dcqPRgEjRT2cLnZMVDQhI9iPKr0uBI+fpONAI\r\n7M+UsEXL6ToP4r6KcbaHarlKmjo2X7B0gHSkv8ZBG4wXlaj5fx60GUoXDEA9\r\nzdpNJd7xlSLoLxQW8CHmyVjrB4TG8l9megWB0BkD1MAxnXyhQWj+p4spwiXU\r\nDJ6VTGSC3cyplLY1h2xYH/uPbqpUnYV4yN/8+kLPzG2cctSGNCvaC8pzUF9s\r\nY5p8iBRH/MdIVZ+KH272CdrGW4HfZYkD8V5VkCt4wzw5J3mY7bml3Tt+0dke\r\nUly0AkoENGuku1JB0qMljdRWzobzGWHeyXrzPpM4wVPG7AY423Qx/wq+dAyp\r\nXfpsX5wl95cueNLKjFxZxqYZPzwdc2TAeno=\r\n=PS3F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53c11a22213dfde9901678a3fdeb438dc039066a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.0.0-alpha.2_1659742345192_0.9810309997029609", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "jest-doc<PERSON>", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "jest-doc<PERSON>@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6fec7deb660713e446bf99946bfbf70ce710a8ab", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-qA7iesYq4EIitMwDB8+j2D0CKbj/tyeFjID9fC5pX8+fcqlJ/ecbN2Se3uAbBBtOS99tTcblprA2MJzlTcrgCw==", "signatures": [{"sig": "MEUCIQDBxrc/ds2LHRFjLjZUuwy4gbiovrf2qZnJyAq5RHjWrQIgJ7dGQOhIyiXTAV0FbZZgvq+w4LzAp19SxDqd2KpjbGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbrhAAoIZ5HalHdn9reS+HPaa+bjmWw9nJCmbsaSsXn5hdVJNsyL6R\r\nTtOjIdWCAAix2IOdX+YEerkRYJjlmuIo84Z/L25SumJk1z8+mQq4jCXL8iTB\r\nMNej2TwjHOOjUVdNxEnhdcS2vIGxILg1hrCdOLb69KOFCmJgo02gG4CRVMum\r\nZx5jZr+/UqAKRnjpcc1L52ZajuffKK+y+N/0FcRxaxbe2Zu/Qcuwz2ERwrVx\r\ntPjCm99w9uQdEYEGOArZ8S3FsnxM++iXOSdds9Zqd3oLaTyXyakLa7hGAMoN\r\ngubCwppGGutEyXHs33sh3dfUUPAETAqGwTXxFiiAm4IICILy7K04HGc6q6zz\r\nY/kHfot2Ljf1eO4I5ArDHx17gKnu7PWQaJoykF2wo61h+BM33frXZnBdFuVl\r\nK7T5ii79HRhKmZLLf263PDxzegmaiA161gad7zp0MgBef7sX577EnRChWsv+\r\nwgEjtrXLUcQBPkL1z3uOHrnVffBpNfeJo8V+OaCWO5Zrp2beVySmPkPpbT6x\r\nMTPm/wmn+8rRIJdjEBv/utr7/JOrAaEC0s9RxH1DFlti6za42R4BMThOMLKz\r\noBxhjLs1aO39z+Gc6aJKPAwfnYrwZSmymvD4cHZg5sDuOnJ4WkwXBFF/xdYW\r\n90VXnhfIwreyFOmf2FQ9o4ICAwvYTpfolVU=\r\n=kr7z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.0.0-alpha.3_1659879691855_0.7028515028105788", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "jest-doc<PERSON>", "version": "29.0.0", "license": "MIT", "_id": "jest-doc<PERSON>@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3151bcc45ed7f5a8af4884dcc049aee699b4ceae", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.0.0.tgz", "fileCount": 5, "integrity": "sha512-s5Kpra/kLzbqu9dEjov30kj1n4tfu3e7Pl8v+f8jOkeWNqM6Ds8jRaJfZow3ducoQUrf2Z4rs2N5S3zXnb83gw==", "signatures": [{"sig": "MEQCIEcsPsGbbSE/xrTkSinVClScYRAb9ysQXFuNqKOLjIhEAiBrUqaaHWylkWzGecbEW/jNVGtxZed3jHtKuXS7E3nB4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKXg/8Dk0ochsARyTMl+PfsEG0MevTiU3rsvfAZFvhoFepNpDutniU\r\nqF2x5xoCPaR8WVXeBYWHI9xNWPuD2WgiKylQjqoP518hpze+5W5gzSione0S\r\nBOJovzOOK36m1YjGELoU/U6YlaFIRjnBzP9zdPptZuLE/zhFo17PO4psqKCh\r\nrS9z/9UYJOMw6zXCCXNCCmrxfKIwJH8f4jJH+trtNDz0L3kks375rUh1KhG6\r\n5vh05e0uDTP6RG7/Uyrh4zAK2koSACm1LQRivMX7s3uh8I4Y2NxkIFTjQ1u0\r\naHMFJ/80KJtFeEOqY7yEFAVr6sla6nCf+xzCsURLH89oB6yXkNW+Sf4zb8Yu\r\njOrQcCU5rLTmyM/6x/gbgo7krTv8hN1XojykbWe9kndt4iUSyFMdeLAc1SNv\r\nqQ6kcoIAkC44/MHo7dR35tfUV4tUwNmDo5WmhdqNcoHN876xzUBqq4VRqD+c\r\ncaiRStDPTzEfvggEkEvHjZ0BnSzHpFpBxvZ4DyYCMl5C3+mBTUuuInVf+wmI\r\nJp9szynmdoFQdGkHeA1FwUvKqtrcC3b5tRHzSKXopEl1VGaDMnSENfC8fjqi\r\nsPF+dZsHIB63LOcSlmxWPj6Fxxz6mUAvaSUSFgE+zzTq/YeLDPDxwImW1UMW\r\nDoPVdkl/OFlAOIkAJGoPCuzX3wBr31Qy/+c=\r\n=rjfI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.0.0_1661430804846_0.7351543802847218", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "jest-doc<PERSON>", "version": "29.2.0", "license": "MIT", "_id": "jest-doc<PERSON>@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "307203e20b637d97cee04809efc1d43afc641e82", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.2.0.tgz", "fileCount": 5, "integrity": "sha512-bkxUsxTgWQGbXV5IENmfiIuqZhJcyvF7tU4zJ/7ioTutdz4ToB5Yx6JOFBpgI+TphRY4lhOyCWGNH/QFQh5T6A==", "signatures": [{"sig": "MEUCICZSsxm7keb0rVqiYOSgEOcOV5LtC4/pBsxfTNLeBtwaAiEAnzI9gMKNPQ/xv08ihUG925xHEAhoAPnbQhPhwihu31g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQaA/+Kk3/mvSe6lstvjypfaS2y6tyci5OzK9WWUFwikc2fZt7ybLR\r\n7pNC0EN7dE7Ujt8eCwJptmFzRGOxNsM10QZ7nJ5vylXkpfzAuPH8YMz671mi\r\nXtvSyzZaew8JVCIwhrOd7DsZ5znkMHyaNzFSM9K5syX2dVlNE54sJ3CukZ28\r\nS2sEXMYcTtL/KhbTvjCG4PMn/mp1wFqWJfZpJupjEK8Ew3PWsr/+OgGZj/EV\r\ndErwpKI7wKynU6+Yhln660cgJkFNYYNjiNqwqsp9UXKI9lMevgqAQ3A2s7qu\r\nrWYvLw+FUZz++JKeOlYYIQjpFCfjEILnCV8rw3AVVdcruKxCe/pZ21/aY5ns\r\n0NvFb4/kZRBX7h6pTIA1jwswRwnKWWzUJWvDwh6MC77JBCNyMe4S5VcIWl6O\r\nIC2FBiRfZi+7yCDsvKK62kq7odzGZBczcjk8tYHOKa47l2oZFDY1ZERqc0Um\r\n/KkPbBF61Sjn/YBDM+c6HBJ7UJTN9d2cunBTF4w31B4xQzQn0BA3dU3GqMCV\r\nkNN1N829PQ+ausXnShe/PuC+ovIi7VxAcPnc+KaSvKsFTx59Op9s7S/HCbII\r\ndoBpKaTMtw3D0W+rmm4a8PnZlyXa8Zp9kAd6Tj+sKVv0rc+p5gV4WRzwAiin\r\nBo7EORhSzEJSpmMoHmkpnfn15aTMNX60fa0=\r\n=6ZSs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.2.0_1665738821701_0.31167763142662896", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "jest-doc<PERSON>", "version": "29.4.2", "license": "MIT", "_id": "jest-doc<PERSON>@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c78a95eedf9a24c0a6cc16cf2abdc4b8b0f2531b", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.4.2.tgz", "fileCount": 5, "integrity": "sha512-dV2JdahgClL34Y5vLrAHde3nF3yo2jKRH+GIYJuCpfqwEJZcikzeafVTGAjbOfKPG17ez9iWXwUYp7yefeCRag==", "signatures": [{"sig": "MEYCIQCWHSqfEKRyshVILCBGlqV0ujaTMAAjDLskqrx5YHlklwIhAPtaOa37stpvcPtDVnMwQTwuX+aI4VLy1l6I9SxAvtm2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocEw/+OhZC+MDVljCt+rjYgMpTU8Gx+X37M8IV592XwauFzG9V19wk\r\ngMPHzqzmuh4aEri8G58DET2je2HRvtmMaNCyOeZA5it34XhQNZ6kkfQRqBYM\r\nZKBy7vtPqFh6O8uNjh5Ux7mN1VIpXeUFT17tkzC3hHui4bc5MfKgZdZl5d2C\r\nAImZGa7IA476dUsSaK9STbsH/pGJic8UCKemwjdR7umDJhY5IglNyoqdHplG\r\nYnkwC/K/sJ3qdU3pxBrIcEefcyAnCmz+0Mjjm+6fI8cgbi+8fq6wN5SIgQXj\r\nwlmpU7LEF+AT6J1nMNd6W7OSQsu+ly8wvh3bQu2sBJaXsfaDGPUPAZqGatTT\r\n3uwhQgJeWcG2Guv46/ZgKCkzQLHYhQkY3yzf0EpE12e9AGP9wQu/stBNpWSx\r\nSdifUu2qfibfwioyRAmqxn3K5bjiWvnu8Q05OIBKAGdlIkcGp5xtnKuFfQ0C\r\nsHeKQw4VKBKZocGDtMSyolz6rJSsB1BlkUfjsiJRPhnO2q7sr2MnUaNz17rl\r\nqoVab6vNEMTChS3KfsGPDGdlja/f1MfslOxj32K6eqQ643vu4Gps3XTlzfED\r\nU4MkaH3mKAbruHhFQXvDUB9bhaHa949P8WFQK8TSUh9ENJg0KPeD/3jq6Pqu\r\n/ZlA6XtKrtBTX7SvBaCBKN/y7wSfTD7Wmrs=\r\n=XOOF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.4.2_1675777521709_0.5384985501314281", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "jest-doc<PERSON>", "version": "29.4.3", "license": "MIT", "_id": "jest-doc<PERSON>@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "90505aa89514a1c7dceeac1123df79e414636ea8", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.4.3.tgz", "fileCount": 5, "integrity": "sha512-fzdTftThczeSD9nZ3fzA/4KkHtnmllawWrXO69vtI+L9WjEIuXWs4AmyME7lN5hU7dB0sHhuPfcKofRsUb/2Fg==", "signatures": [{"sig": "MEYCIQDZdq4+NnMVK38Va4EYyEiefjLjoZ/BCSLhQQA1A0AzgwIhAMupPJ4IityHPXluq1zkEapq70KvWnnqpchPNw0iJaCV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC3RAAh7T2TnP0UhmvI5rZ2IYkosZNKnf+1cH9l0aL1nHmUIKyIVfV\r\nQKQlBmBIOEev7DpHX5uV4T6VKqmyoQn17832Qph4ktsTnK+xHMFdWVsYyl+T\r\nHE3CGtSJ3FfHdm4KEyqAkFTUGeTbhcPfSU7/c+SuwqE6UzVYJXbTcrzQoGHo\r\ni6W6vg/HPBkjiH0UTfFjPxXQXCtvdA8EVl9879rAQVlyVyGzVnr3FEuiQp1T\r\n2HQV9k+ZX4pzcwZ8j4sWgewxijah4W0sIOdvLRj66UlKxYXN2GAkwFSvgvfQ\r\nT91delssAJDsPuuMfWgzExE6Y/zWXfemSAMXJTWXIV5CmddT+mVHJ/hoae/D\r\n6LKo2IrDs6JLwFxgkmGOJuBKILnMHpBqlYu/NE8ueT5QhdzN0j++JFmp2ygT\r\nR9tDbMyK8RAaY80iEpyyeENiVQRYAAJumnEGOWnXNb78WhA5c89gUBZVjRf3\r\nVKhsoA/uLV8fpSlc/C2iohkOi3bgWQROhLO+3sgg0+GsOrpSdlKOT0LlpCti\r\n/+cysBd+cNUAg/o10+Iwuo7/Gn9C8TqmTQRHWicd6P18UsAAL3MdzT5sN5te\r\nwR1zwIHtLdNqEOYWho757S+WmBLZPaRBwTV7zPn4r9gGq4tnLIm80llFbk/T\r\nKKD58wy2Jri1J1rNNqhKeFzyOfNiJ8Gahys=\r\n=mysI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.4.3_1676462236418_0.6733730536684097", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "jest-doc<PERSON>", "version": "29.6.3", "license": "MIT", "_id": "jest-doc<PERSON>@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "293dca5188846c9f7c0c2b1bb33e5b11f21645f2", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.6.3.tgz", "fileCount": 5, "integrity": "sha512-2+H+GOTQBEm2+qFSQ7Ma+BvyV+waiIFxmZF5LdpBsAEjWX8QYjSCa4FrkIYtbfXUJJJnFCYrOtt6TZ+IAiTjBQ==", "signatures": [{"sig": "MEQCICjlpx3HNklRgNkY0iXI051zD0uydOmVVCg3PFOSqPuJAiAmPCRmeh/UbQ3Va76nE57jgZjISc4jToM7ZgJezVzG+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9034}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.6.3_1692621537565_0.2134958422196498", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "jest-doc<PERSON>", "version": "29.7.0", "license": "MIT", "_id": "jest-doc<PERSON>@29.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "8fddb6adc3cdc955c93e2a87f61cfd350d5d119a", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz", "fileCount": 5, "integrity": "sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==", "signatures": [{"sig": "MEUCIGKVuQ7Vr56Vrcx4nvq4+bjfG6yalAjc4I752aPoAY67AiEAw9zgX/18ExmuIxEsWAwUrbcaPlxK1Ts+6aka6ZnNQrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8989}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_29.7.0_1694501019432_0.3851613349343064", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.0": {"name": "jest-doc<PERSON>", "version": "30.0.0-alpha.0", "license": "MIT", "_id": "jest-doc<PERSON>@30.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "3d398a0e88a8c4ef0855e25fdec7ef98fc5a8336", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-pfDOYPI84arpcawGUubRFz9C72jjaprMjupwEt+s/q8bOnJ7k5wHwRoBd6iHgbIoF7OHFEGJ2PJjN/gHeyCRtg==", "signatures": [{"sig": "MEUCIQDCzvtb4EBfUVpa3vt5HDLlgNPanbg+J9P5uTtlorVLTAIgCDqD5+wz6zhZg1uqb51YYdcOf/GOiZDA/Au1ZRELHJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9717}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "780ae28333df4d188b2ef78bd19d4ed5bc53562d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-alpha.0_1698671619560_0.9495151058754323", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "jest-doc<PERSON>", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "jest-docblock@30.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "b6d28d89db3ffebe1c9f74eb683d4d0034f718ba", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-k26n59bLkqiX3EkfUgJeOWObYGKXg70fjLFsvXhQSNpvGMUw8hpAzZNPef8MoUTRd9uZvxK/GcBKtmKQ9OXFCg==", "signatures": [{"sig": "MEQCIA94RykGVLzTaQrnmaCKdvNDZhUMg1TZuuNxGlKavbF7AiAkwJesJ1J5+6RlS93UWGXG/ME6MN1BpYRQn3kLwh5BQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9717}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-alpha.1_1698672765997_0.5566293353350371", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "jest-doc<PERSON>", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "jest-docblock@30.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f3d194506b1a0a0352dea4fa045ecc98742a3f98", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-QA0h6gCmu9e+mVr2cZIZN9koLyP4q5uf/rk8QCo/5pO3M0tCxibSN4RAqiCStLBzRRH6Y8He0N6k+I+hEK1+aQ==", "signatures": [{"sig": "MEUCIBlqvs094MxvmkCsyaztys/Cxc01+lKWR9pWAVirLefsAiEAu4qHYC6YEMyxn56fT0XSCXU0Lpo5WGt9UU4a4QP0s0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9718}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-alpha.2_1700126894578_0.9266329609663679", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "jest-doc<PERSON>", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "jest-doc<PERSON>@30.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "67342d249766d5d354f983e303de6e053a814d23", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-kVMYlUpTIYCPUdl++XuEfAAew+1KWLTdkNiq2bwq8PsAU4rWMYDKuGOBIBxOHP0UcEN+zeObCmXspudpAG2xJQ==", "signatures": [{"sig": "MEYCIQCMDH1VmSrVFJHL/TXIetOiDeU27NJEaGsKOXYd4HJoQAIhALffN5PdFg3jy5ejcdqKc64Ci/iB0fbJQ2IyweFXtBwq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9897}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-alpha.3_1708427327883_0.4722443502366409", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "jest-doc<PERSON>", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "jest-docblock@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "df4791e13d60b4524ca2a8c2556c2f723833970c", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-3X7UlE3VW40oZDjdtQuznLIVCNpCt1cNUV0bm/c+gzkQjccyqiSNCKlGkjDCWhhhzsWbEu3GZidkQAKvTXXRMg==", "signatures": [{"sig": "MEUCIHhYjuRWF8uimmpPQaOMQARM+Fb++AT4M4fhfjOhcHovAiEAnqAXQAlUsoHjOHjlzdgZ8/vro3amST0hg0xYvviHq3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9941}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-alpha.4_1715550194256_0.20466815351346135", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "jest-doc<PERSON>", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "jest-doc<PERSON>@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "11f06623395ff32a54754a253924cd0fbe702731", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-P3BGGmZ7z0pyD1mgVRzsfMmjyj+zpn839p/y44tRS8k50Fg+NCL5qUoKHjDLAUnLHcwQxFfk8nrqraebphHfZA==", "signatures": [{"sig": "MEUCIEx7Rp+qCCtJeUv8l+3iOPU+mNECZIoQdFDS9B/U15ifAiEAsuLd+g10kuLCU9JR0w6m5hWH4kQWKI+0dwJcNN/mVCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9941}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "`jest-docblock` is a package that can extract and parse a specially-formatted comment called a \"docblock\" at the top of a file.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-alpha.5_1717073032955_0.8259864292354238", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "jest-doc<PERSON>", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "jest-docblock@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "2d09323375762fc0ed5f72a5e4883b8a5ed17924", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-KXRLgRo7/rF1wqxQupsFCZa6wOp1qrDg4GdSXKfIHODYQb0dpi4rYaYA8xV5l2g9KwYc9/zV7l1tPe9TOr27ew==", "signatures": [{"sig": "MEUCIQDTd6N3xnC5wUp0gP3HN9A8msPxfJl4bvVgbhZ1Bwhv/gIgPZtjG6ca19fTPnfL3UesMDEzcH8jwD2/IgMExufswJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9931}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-alpha.6_1723102976077_0.1723819111749756", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "jest-doc<PERSON>", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "jest-doc<PERSON>@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "3abc54a61011171070baff82767577af2c72bd55", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-vAid4aUsBD0yke/NIbmSBi0BuJqcyqX6f4vR//7VADq6ASWzmadfpRPSKrfvOeBy+ElBGVNf9UVaH4+eSPfrcw==", "signatures": [{"sig": "MEQCIBFNvuNa2aAwN9D9ZjLQm2PaeGd4oatYgBUTg6RNxuxkAiBwS8dGk+yoiNbN6C7qKVMe4hpFBCMY3vvDN18EnwDBKQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9932}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-alpha.7_1738225702716_0.05842902324953858", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.1": {"name": "jest-doc<PERSON>", "version": "30.0.0-beta.1", "license": "MIT", "_id": "jest-docblock@30.0.0-beta.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "79d7807c847a5d414de66dbae0228e0f7c03f5e6", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-Z0Kmv1hp1/Ms+DjoB4foD4AavQ7AiGsg2oUOG1MmEwRAFryjc5cZOKObio8ZkgtJlebsKeEJR57I2ZK+XKzakg==", "signatures": [{"sig": "MEUCIQDNT7SzOg2JkqMrxCzK48S9IgpFmtqBFU7WA3X7QBfszQIgd9NtmM+rxYnKKCE78lM/h7HYWb5tXkQOKWuJQJlUOfA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9930}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ca9c8835e3c74ec17450cac43c7cd3e8bb5747b2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-beta.1_1748306895280_0.36023652432121334", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "jest-doc<PERSON>", "version": "30.0.0-beta.3", "license": "MIT", "_id": "jest-docblock@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "f77e4f0eb38782292a711ad731387e5cfd6056a5", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-dzgokmq3pK2P0kxi8g58SpiVDNyObdTppRMPtcYEZPX6CeP+wRqPwkA2ZJpMoaUXNV8xRFYHYn/4q8oLhjpEeg==", "signatures": [{"sig": "MEQCIApKUFhcp73Um0OwzROPj2qsgCAAYgyNt5O6pnRqTbUAAiBBRt7FriYLu7qdI6I9+BDA2L0fc27AYYqY6NlR4WF/Zg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9930}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-beta.3_1748309255044_0.39355491416598376", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "jest-doc<PERSON>", "version": "30.0.0-beta.6", "license": "MIT", "_id": "jest-docblock@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "6b4ff6578c1050642f4b887c9ef0e4d13979a29f", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-beta.6.tgz", "fileCount": 6, "integrity": "sha512-fxWiyoMxQ6v/q7tvVfxTXMSXqGCxaiBe5Ogvs4xCCVxYrQsADENnnbJXqYlVq/MVtPyOVtFJiQpGbASv2bs18w==", "signatures": [{"sig": "MEYCIQCBcJyUb9FnxqNZ++0KmaE77IKSrMU+woFeNaDPnSXC/gIhAJ0Qmdcdkre3785W1CY73tCZ+JVkGhoX+yVQv/mVLhk6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9941}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"detect-newline": "^3.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-beta.6_1748994637286_0.35447355332068775", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "jest-doc<PERSON>", "version": "30.0.0-beta.7", "license": "MIT", "_id": "jest-docblock@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "c1384e86656e93e904e1abd6bd0436628f8ef61d", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-beta.7.tgz", "fileCount": 6, "integrity": "sha512-q8eku5gqiYYA3MwIWOT0Xs8jAfoa8AS8q1XC+4P6uimeppWsWjtu4+w9zB+Or6bDctZDJdQ/pBsQIdwCWgH4/g==", "signatures": [{"sig": "MEUCIQC5Etv2qcbkdMPch4BP/55DOdCFFcEurZeWM9LRmtgdpwIgK0i1r0RMM8clRiBLbDOPhM1nwSV/FuXBSOz5xkbvfJ0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9941}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"detect-newline": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-beta.7_1749008134323_0.7676407607219558", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "jest-doc<PERSON>", "version": "30.0.0-beta.8", "license": "MIT", "_id": "jest-docblock@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "bd8ae79b4cac2779b51a9bc4373ae8155af5e116", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-beta.8.tgz", "fileCount": 6, "integrity": "sha512-mOanQUkgsPu94ZSSuj9DlacYOAZDaD/py7jESGCBg2lpAxtHHFgydfzxbQ5aY7IXq07USJR4Ky6m2v467JEG3g==", "signatures": [{"sig": "MEUCIQDKRfGcGy9f3zZYBMacCUZBw2Sv2bZEuG0fJfJGOOegIgIgPTd0QkCX0hz86X3IRgGr/ZY7+F4JgjB4OzT+4DmWMD4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9941}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"detect-newline": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-beta.8_1749023581350_0.6372639617052076", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "jest-doc<PERSON>", "version": "30.0.0-rc.1", "license": "MIT", "_id": "jest-docblock@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ff7885e66b911a7b9c7cb0ca45f88cbd90f3ef50", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-VWv8H89Aud6UAD2Fq692IVN5OG7im4aqTMqiPHKOVgbLK2ar1ovcOFg383AurP9l2eAXfdBO0vikUdS8yruPtA==", "signatures": [{"sig": "MEYCIQCAhf9wL72GPiHl5XX8oQK7UiQaERfTiuQsbYcou5mLHgIhAIMrZGvuWcRaRKL0dfwqgFPiq6WSqL+Lp+IKteqcJ8aW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9939}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"detect-newline": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0-rc.1_1749430958510_0.7788325685041324", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "jest-doc<PERSON>", "version": "30.0.0", "license": "MIT", "_id": "jest-doc<PERSON>@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "1650e0ded4fa92ff1adeda2050641705b6b300db", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.0.tgz", "fileCount": 6, "integrity": "sha512-By/iQ0nvTzghEecGzUMCp1axLtBh+8wB4Hpoi5o+x1stycjEmPcH1mHugL4D9Q+YKV++vKeX/3ZTW90QC8ICPg==", "signatures": [{"sig": "MEUCICBqYh5Nqdf8l5ze2g13porXZK/4dZ7V0QAXIOYKgoAaAiEAvRZKMg0eq5gnUWu3z0OagfLgDaq6+sRrwncLqSLO6/I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9934}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-docblock"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"detect-newline": "^3.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-docblock_30.0.0_1749521739964_0.8431160118113064", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "jest-doc<PERSON>", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-docblock"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"detect-newline": "^3.1.0"}, "devDependencies": {"@types/node": "*"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "jest-doc<PERSON>@30.0.1", "dist": {"integrity": "sha512-/vF78qn3DYphAaIc3jy4gA7XSAz167n9Bm/wn/1XhTLW7tTBIzXtCJpb/vcmc73NIIeeohCbdL94JasyXUZsGA==", "shasum": "545ff59f2fa88996bd470dba7d3798a8421180b1", "tarball": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-30.0.1.tgz", "fileCount": 6, "unpackedSize": 9934, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCBKAN4ttDZ6+BWaBCQ/IhZJ0sb4XCbKMK73F6biGzEaQIhAPeFyp8QtG1bE8J0daMXUGV75EU+zOQLAxqJ0cajXAcY"}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/jest-docblock_30.0.1_1750285877466_0.8920453194498115"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-03-03T15:10:47.875Z", "modified": "2025-06-18T22:31:17.971Z", "0.0.0": "2017-03-03T15:10:47.875Z", "19.1.0-alpha.eed82034": "2017-03-17T00:41:16.826Z", "19.2.0-alpha.993e64af": "2017-05-04T15:37:33.146Z", "19.3.0-alpha.85402254": "2017-05-05T11:48:17.507Z", "20.0.0": "2017-05-06T12:32:28.979Z", "20.0.1": "2017-05-11T10:50:02.174Z", "20.0.2": "2017-05-17T10:50:15.853Z", "20.0.3": "2017-05-17T10:57:06.139Z", "20.1.0-alpha.1": "2017-06-28T10:16:14.881Z", "20.1.0-alpha.2": "2017-06-29T16:36:41.495Z", "20.1.0-alpha.3": "2017-06-30T14:20:47.539Z", "20.1.0-beta.1": "2017-07-13T10:33:35.644Z", "20.1.0-chi.1": "2017-07-14T10:24:56.876Z", "20.1.0-delta.1": "2017-07-18T08:46:47.780Z", "20.1.0-delta.2": "2017-07-19T12:56:37.158Z", "20.1.0-delta.3": "2017-07-25T22:12:22.359Z", "20.1.0-delta.4": "2017-07-27T17:19:06.250Z", "20.1.0-delta.5": "2017-08-01T16:33:35.258Z", "20.1.0-echo.1": "2017-08-08T16:49:49.585Z", "21.0.0-alpha.1": "2017-08-11T10:14:01.567Z", "21.0.0-alpha.2": "2017-08-21T22:06:47.400Z", "21.0.0-beta.1": "2017-08-24T21:26:43.142Z", "21.0.0": "2017-09-04T15:01:46.493Z", "21.0.2": "2017-09-08T14:19:07.478Z", "21.1.0": "2017-09-14T01:50:05.539Z", "21.2.0": "2017-09-26T20:22:08.834Z", "21.3.0-alpha.1e3ee68e": "2017-09-28T14:20:31.547Z", "21.3.0-alpha.eff7a1cf": "2017-10-01T16:46:44.755Z", "21.3.0-beta.1": "2017-10-04T10:48:35.376Z", "21.3.0-beta.2": "2017-10-13T09:54:01.018Z", "21.3.0-beta.3": "2017-10-25T19:33:55.948Z", "21.3.0-beta.4": "2017-10-26T13:26:47.899Z", "21.3.0-beta.5": "2017-11-02T13:17:22.392Z", "21.3.0-beta.6": "2017-11-03T16:21:21.109Z", "21.3.0-beta.7": "2017-11-06T09:39:40.187Z", "21.3.0-beta.8": "2017-11-07T17:43:28.771Z", "21.3.0-beta.9": "2017-11-22T13:17:21.661Z", "21.3.0-beta.10": "2017-11-25T12:39:17.732Z", "21.3.0-beta.11": "2017-11-29T14:31:14.541Z", "21.3.0-beta.12": "2017-12-05T18:48:27.721Z", "21.3.0-beta.13": "2017-12-06T14:37:00.334Z", "21.3.0-beta.14": "2017-12-12T10:52:25.372Z", "21.3.0-beta.15": "2017-12-15T13:27:30.972Z", "22.0.0": "2017-12-18T11:03:18.209Z", "22.0.1": "2017-12-18T20:29:17.259Z", "22.0.2": "2017-12-19T13:52:57.350Z", "22.0.3": "2017-12-19T14:58:45.116Z", "22.0.6": "2018-01-11T09:46:36.352Z", "22.1.0": "2018-01-15T11:57:02.048Z", "22.2.0": "2018-02-07T10:25:52.741Z", "22.2.2": "2018-02-09T16:28:09.883Z", "22.4.0": "2018-02-20T12:03:27.376Z", "22.4.3": "2018-03-21T16:08:01.054Z", "23.0.1": "2018-05-27T15:30:37.705Z", "23.2.0": "2018-06-25T14:05:09.698Z", "24.0.0-alpha.0": "2018-10-19T12:12:28.845Z", "24.0.0-alpha.1": "2018-10-22T15:35:34.871Z", "24.0.0-alpha.2": "2018-10-25T10:51:01.648Z", "24.0.0-alpha.4": "2018-10-26T16:33:02.380Z", "24.0.0-alpha.5": "2018-11-09T13:12:33.068Z", "24.0.0-alpha.6": "2018-11-09T17:49:29.991Z", "24.0.0-alpha.7": "2018-12-11T16:07:37.359Z", "24.0.0-alpha.9": "2018-12-19T14:23:29.087Z", "24.0.0-alpha.10": "2019-01-09T17:02:13.557Z", "24.0.0-alpha.11": "2019-01-10T18:32:35.685Z", "24.0.0-alpha.12": "2019-01-11T14:58:36.854Z", "24.0.0-alpha.13": "2019-01-23T15:15:19.386Z", "24.0.0-alpha.15": "2019-01-24T17:52:21.434Z", "24.0.0-alpha.16": "2019-01-25T13:41:52.292Z", "24.0.0": "2019-01-25T15:04:45.693Z", "24.2.0": "2019-03-05T11:22:43.849Z", "24.2.0-alpha.0": "2019-03-05T14:46:22.479Z", "24.3.0": "2019-03-07T12:59:19.197Z", "24.9.0": "2019-08-16T05:55:46.675Z", "25.0.0": "2019-08-22T03:23:44.552Z", "25.1.0": "2020-01-22T00:59:44.599Z", "25.2.0-alpha.86": "2020-03-25T17:16:11.169Z", "25.2.0": "2020-03-25T17:57:54.954Z", "25.2.1-alpha.1": "2020-03-26T07:54:13.039Z", "25.2.1-alpha.2": "2020-03-26T08:10:21.717Z", "25.2.3": "2020-03-26T20:24:41.277Z", "25.2.6": "2020-04-02T10:29:08.120Z", "25.3.0": "2020-04-08T13:20:57.931Z", "26.0.0-alpha.0": "2020-05-02T12:12:51.983Z", "26.0.0": "2020-05-04T17:52:56.371Z", "27.0.0-next.0": "2020-12-05T17:25:07.724Z", "27.0.0-next.10": "2021-05-20T14:11:12.263Z", "27.0.1": "2021-05-25T10:06:23.935Z", "27.0.6": "2021-06-28T17:05:31.430Z", "27.4.0": "2021-11-29T13:36:54.829Z", "27.5.0": "2022-02-05T09:59:17.865Z", "27.5.1": "2022-02-08T10:52:12.101Z", "28.0.0-alpha.0": "2022-02-10T18:17:26.610Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.130Z", "28.0.0-alpha.6": "2022-03-01T08:32:22.467Z", "28.0.0": "2022-04-25T12:08:03.136Z", "28.0.2": "2022-04-27T07:44:00.158Z", "28.1.1": "2022-06-07T06:09:35.566Z", "29.0.0-alpha.0": "2022-07-17T22:07:05.881Z", "29.0.0-alpha.2": "2022-08-05T23:32:25.330Z", "29.0.0-alpha.3": "2022-08-07T13:41:31.970Z", "29.0.0": "2022-08-25T12:33:25.023Z", "29.2.0": "2022-10-14T09:13:41.843Z", "29.4.2": "2023-02-07T13:45:21.886Z", "29.4.3": "2023-02-15T11:57:16.567Z", "29.6.3": "2023-08-21T12:38:57.785Z", "29.7.0": "2023-09-12T06:43:39.655Z", "30.0.0-alpha.0": "2023-10-30T13:13:39.777Z", "30.0.0-alpha.1": "2023-10-30T13:32:46.181Z", "30.0.0-alpha.2": "2023-11-16T09:28:14.757Z", "30.0.0-alpha.3": "2024-02-20T11:08:48.041Z", "30.0.0-alpha.4": "2024-05-12T21:43:14.395Z", "30.0.0-alpha.5": "2024-05-30T12:43:53.147Z", "30.0.0-alpha.6": "2024-08-08T07:42:56.231Z", "30.0.0-alpha.7": "2025-01-30T08:28:22.907Z", "30.0.0-beta.1": "2025-05-27T00:48:15.452Z", "30.0.0-beta.3": "2025-05-27T01:27:35.425Z", "30.0.0-beta.6": "2025-06-03T23:50:37.469Z", "30.0.0-beta.7": "2025-06-04T03:35:34.481Z", "30.0.0-beta.8": "2025-06-04T07:53:01.535Z", "30.0.0-rc.1": "2025-06-09T01:02:38.704Z", "30.0.0": "2025-06-10T02:15:40.165Z", "30.0.1": "2025-06-18T22:31:17.709Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-docblock"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}