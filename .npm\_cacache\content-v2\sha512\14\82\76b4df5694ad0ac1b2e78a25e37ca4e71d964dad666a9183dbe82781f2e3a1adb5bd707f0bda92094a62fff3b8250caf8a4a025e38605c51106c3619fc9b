{"_id": "has-flag", "_rev": "18-9ab38e1ffb70f77de4db07fb8d902f94", "name": "has-flag", "description": "Check if argv has a specific flag", "dist-tags": {"latest": "5.0.1"}, "versions": {"1.0.0": {"name": "has-flag", "version": "1.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "jbnicolai.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/qix-"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "0.0.4"}, "gitHead": "621de2782d538f28f99f0bb85b158799cb3ae5cf", "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "homepage": "https://github.com/sindresorhus/has-flag#readme", "_id": "has-flag@1.0.0", "_shasum": "9d9e793165ce017a00f00418c43f942a7b1d11fa", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9d9e793165ce017a00f00418c43f942a7b1d11fa", "tarball": "https://registry.npmjs.org/has-flag/-/has-flag-1.0.0.tgz", "integrity": "sha512-DyYHfIYwAJmjAjSSPKANxI8bFY9YtFrgkAfinBojQ8YJTOuOuav64tMUJv584SES4xl74PmuaevIyaLESHdTAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGJPhJVs8d0OfRuxcVvpsx8/GlyCDY76+T7lmUWEAiYbAiEA2FMa55+Q2P/9xW4CO98gnJER9U0G98XTLOt1Fb5LZQQ="}]}, "directories": {}}, "2.0.0": {"name": "has-flag", "version": "2.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "601137409f2617c75838d8f3febed5c6e6e8ee2c", "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "homepage": "https://github.com/sindresorhus/has-flag#readme", "_id": "has-flag@2.0.0", "_shasum": "e8207af1cc7b30d446cc70b734b5e8be18f88d51", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e8207af1cc7b30d446cc70b734b5e8be18f88d51", "tarball": "https://registry.npmjs.org/has-flag/-/has-flag-2.0.0.tgz", "integrity": "sha512-P+1n3MnwjR/Epg9BBo1KT8qbye2g2Ou4sFumihwt6I4tsUX7jnLcX4BTOSKg/B1ZrIYMN9FcEnG4x5a7NB8Eng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGAVqTFFTaeMGmOLp7C8GcVliAujwOiJBlkgnH1vJ67PAiAzfhhRC3Qfcl2Xcy2ecWV5xyaxQu70coaS5895eDJYzA=="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/has-flag-2.0.0.tgz_1460389675597_0.3113734829239547"}, "directories": {}}, "3.0.0": {"name": "has-flag", "version": "3.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "8b2ca7e693b2c742b29f2399194077b64b9ff781", "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "homepage": "https://github.com/sindresorhus/has-flag#readme", "_id": "has-flag@3.0.0", "_shasum": "b5d454dc2199ae225699f3467e5a07f3b955bafd", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b5d454dc2199ae225699f3467e5a07f3b955bafd", "tarball": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYPznbV4hRZzxmumAMzd7EHPhJS1CSCRg/h8+BV/oW6QIhAPJ6qohX1iPmbo4ATN+qBxI8hj86PILSMCets72aK/oa"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/has-flag-3.0.0.tgz_1514920915118_0.33958922349847853"}, "directories": {}}, "4.0.0": {"name": "has-flag", "version": "4.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "474aa39afca7333d356c022fc5be4d31732bbba3", "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "homepage": "https://github.com/sindresorhus/has-flag#readme", "_id": "has-flag@4.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "shasum": "944771fd9c81c81265c4d6941860da06bb59479b", "tarball": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "fileCount": 5, "unpackedSize": 4419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqMqCCRA9TVsSAnZWagAATrIP/22VegVJi5IYjjhsWhNL\nllQoPP8zRlEq/AG6a36bAVuF2lEGtPvtausr/IUbsWZj83n4orGFECflp7Qb\nAsJ3u6zFZLtnp5ug+AhjcqloCM57A05T2RT1YtMpIStapRGsIVc5xudQJtQI\nC+i8ePGQH42iYtLweN7xEw6UKa1ke8UG9hn8FvNP3EkGJOhS59TL8KfsxD8v\nQaNYjOr15UsRsL8K44afDQyhlO4njhr/e+INBDBQhR9vbjTysSxjf/edhzqu\n0CT+WdecdjAMk5egAjIIIbFTQmJtzITfVGJy/UnDmHF3Dyn68QPCOGVbInzy\ncyR858RhE2DAthInEOTYoM5ZfzH2YVSboXcsVZCn2/qcwmKVEyMcGzb0Mzeb\na2jyCMKacWlFO4LbdlXelLaiAfax5auAGiCtDyVfxV1QzBfGbOPoHpmN3drr\nRGxKBoQnlvXrrofmnnpP6sTHQVJJEo86y+F3WzlTwXqp50sC1gq05JC/TNxp\nOIsT5A8RPYmJBvZZKArMo3dHtlvOPPCN9GNERqrKr6ITq55d6h2dylmi46pJ\n920HPH55IKZlB3w73e3ylfAqEmG1KlM3auk/bHqRZJpdPRxUtHP+9pxweDbV\nNYAxJI6hZ3yMhAU8CwbLGBDYZTXSEJt2QxfJj8Yw4lQbQfUScjiML+MmJipS\nM5jU\r\n=+VZG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFIW6CbIUufytlg7z6IWHf4WQ5H0IgTew2Vydxc86Y04AiBa0VEMmjPBw7a40g+RZ1HkEW18uRdGxBR72XhW67yDew=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/has-flag_4.0.0_1554565761745_0.3213063984648754"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "has-flag", "version": "5.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "49240fe75374a5e8f90c9a32827a0faf9f001ad1", "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "homepage": "https://github.com/sindresorhus/has-flag#readme", "_id": "has-flag@5.0.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dmhh9fPE7WRat4yiFvyhXh7LytudXDcE89VEdR3sxfWQKtFPr7jpXip5H402aeFfA36ucqSKecGEapc7N44k+g==", "shasum": "36e12f0b14052d6ffc8007c6f768a87623f6f692", "tarball": "https://registry.npmjs.org/has-flag/-/has-flag-5.0.0.tgz", "fileCount": 5, "unpackedSize": 4210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeUG1CRA9TVsSAnZWagAAYFAP/RZHf8wA6lQ6YmX3zWOu\n0PEvFdah/uqRIaJgXSsmxStHkADFAY6Z95lc0SGjCkEowEQopai328zfJRse\nFr0V2XoLObhL9ZP9XU5qNbtm0TPAUI1xq3EVeDyQomEX8JSp1RU7JB1iWbaX\nVcuDnreA3Lxff+eOgR0j11n/VWDAh7YJme+3F3l8LeH72ErDha27QhW8rBf6\nF+ceVrtQ7IV6B6/NlvgAz5fvOML27KDmbntFMQLJdyQ+nWDbrD5r7pJ4bZ27\n/IdQ1nS07RXUDAx103ThEf+Vr+23bdv59eR7CxnX0QENTw3Tm0rq+kvEKZ2L\n+aGw+aFca3L/q7zPv5XY8Mr58R4MCAEpl4HlPbukBsOeuLN+9uvjn4EUMQoo\nqv2GjaGqUWqtrQyCLFB+EKmTT1/9rWapwDwLEW5cjB+nTZDJTSUqwNgWyjps\n5dWQVQn5jaCNeLpEZk95OWkNFxgtp69uWIhvCImEEiE1uPq4iPhfPd4yrmIr\ny2hBo/B62DeVOfzL2HXrUf5hM6oRh+c+38vghJkIGV/bV7fi8VZPbNIK3rBG\nOzIw4qVrGhaopLRg53G9rGLJiSU4pgTwMY93/5zrxq0IIdU0iN9VDQmq2M/p\n6mRF9tIjrG8hUa29BPOl07XSKjSprUQNpOKi2F0T1gmmpVe1ie9+0/TZ3Qq/\naR67\r\n=N7jb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHlp1IPWIzPvt5C9fvUu6o7NJyuAaXENeYjli2FJtmRQIgHgpln/kHbKB8znY6IoOTqgu1dYvn2qpBmVKCpmMCGtk="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/has-flag_5.0.0_1618559413186_0.44645416684559414"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "has-flag", "version": "5.0.1", "description": "Check if argv has a specific flag", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "0c7d032214c51d14b458364c9f6575ea9afa08b1", "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "homepage": "https://github.com/sindresorhus/has-flag#readme", "_id": "has-flag@5.0.1", "_nodeVersion": "14.16.1", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-CsNUt5x9LUdx6hnk/E2SZLsDyvfqANZSUq4+D3D8RzDJ2M+HDTIkF60ibS1vHaK55vzgiZw1bEPFG9yH7l33wA==", "shasum": "5483db2ae02a472d1d0691462fc587d1843cd940", "tarball": "https://registry.npmjs.org/has-flag/-/has-flag-5.0.1.tgz", "fileCount": 5, "unpackedSize": 4292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9bbOCRA9TVsSAnZWagAA7FgQAIi/uPnwjILtQMmTqCIY\nWcD7Chz2q6Lf72iyfQje+50nmpmSrf16BcbBsNeR2T3NC9lFpTJKnDSpVMpM\nBye41Qwlw18c2hMLtXSpCdzMcuH1B0U+4cXuotoZaovThCA+p40y903bMox9\nG9xJRQrpy4XGjHe8o/4W6poR0QiQQrUJRWYhNgnRLvK87fSSk0PIAx9C4HSw\nsMOQdjCaSeSPby67AiF+qlIDfwwKBZqjsEZmUssxhpbxIqeBELHU+1KGJGGc\nKTqsl8Lez+Q5GYVmJo/84lcDLGxHCUolGoWhJqf54JTanEV+40x3QNmUtNTA\nGhbsi9ISZ1KIvu0NgxmkieLn14m0xM3sTcQ5IA+9kPrGj3b5Miqc0DlahtL6\ngRmrAegEmYUjeXdVLkH1XUJLuAbORfil1QzCZwxurJ0MatnKZtgxuIR1DU54\n9gbiDEpNJdhYs5sVLO7qaV5b1sHbJrEgqUw67eeOLb3WlTzW6mJyXAfqmUhS\nKspMFnXqkw+pOXdNL1TEDrTHb5x9TBoZjgugzcCDc45NpgMpATrgqp8v87x8\nux5C4bnVr6OGmB7rkwdKH7wF1vtIT8hiRR/CKkj+zoSqEJjZiE0hPLNZGmse\nms9671k04MVadIG+/M0MIVuuNd5OfoNmnA3oIn7JCyGotE3ggQXQlVYS+L2+\nTtXm\r\n=nheO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID0U3iZuF4ngw6yI5Q1DMIzgGZOC+FjSkP1cUVWWPxX+AiEArAIRr7tR7h9kIVk4EB0OIr5q442GpY4swyNJsRVrI/I="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/has-flag_5.0.1_1626715853988_0.3138751642264608"}, "_hasShrinkwrap": false}}, "readme": "# has-flag\n\n> Check if [`argv`](https://nodejs.org/docs/latest/api/process.html#process_process_argv) has a specific flag\n\n## Install\n\n```\n$ npm install has-flag\n```\n\n## Usage\n\n```js\n// foo.js\nimport hasFlag from 'has-flag';\n\nhasFlag('unicorn');\n//=> true\n\nhasFlag('--unicorn');\n//=> true\n\nhasFlag('f');\n//=> true\n\nhasFlag('-f');\n//=> true\n\nhasFlag('foo=bar');\n//=> true\n\nhasFlag('foo');\n//=> false\n\nhasFlag('rainbow');\n//=> false\n```\n\n```\n$ node foo.js -f --unicorn --foo=bar -- --rainbow\n```\n\n## API\n\n### hasFlag(flag, argv?)\n\nReturns a boolean for whether the flag exists.\n\nIt correctly stops looking after an `--` argument terminator.\n\n#### flag\n\nType: `string`\n\nCLI flag to look for. The `--` prefix is optional.\n\n#### argv\n\nType: `string[]`\\\nDefault: `process.argv`\n\nCLI arguments.\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-has-flag?utm_source=npm-has-flag&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-04-17T23:19:31.649Z", "created": "2015-07-07T22:43:54.339Z", "1.0.0": "2015-07-07T22:43:54.339Z", "2.0.0": "2016-04-11T15:47:58.270Z", "3.0.0": "2018-01-02T19:21:56.098Z", "4.0.0": "2019-04-06T15:49:21.907Z", "5.0.0": "2021-04-16T07:50:13.326Z", "5.0.1": "2021-07-19T17:30:54.122Z"}, "homepage": "https://github.com/sindresorhus/has-flag#readme", "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/has-flag.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/has-flag/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"michalskuza": true, "webzuu": true, "aleclarson": true, "rubiadias": true, "drewigg": true, "flumpus-dev": true}}