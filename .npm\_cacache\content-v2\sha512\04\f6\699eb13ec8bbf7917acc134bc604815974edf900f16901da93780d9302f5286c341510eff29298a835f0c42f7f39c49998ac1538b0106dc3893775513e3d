{"_id": "@types/serve-static", "_rev": "658-12c5afa03849ba03bde3f812cc2671cf", "name": "@types/serve-static", "dist-tags": {"ts2.0": "1.13.0", "ts2.1": "1.13.0", "ts2.2": "1.13.2", "ts2.5": "1.13.3", "ts2.9": "1.13.3", "ts2.8": "1.13.3", "ts2.7": "1.13.3", "ts2.3": "1.13.3", "ts2.4": "1.13.3", "ts2.6": "1.13.3", "ts3.0": "1.13.5", "ts3.1": "1.13.5", "ts3.2": "1.13.8", "ts3.3": "1.13.8", "ts3.5": "1.13.9", "ts3.4": "1.13.9", "ts3.7": "1.13.10", "ts3.8": "1.13.10", "ts3.9": "1.13.10", "ts3.6": "1.13.10", "ts4.0": "1.15.0", "ts4.1": "1.15.0", "ts4.2": "1.15.1", "ts4.4": "1.15.2", "ts4.3": "1.15.2", "ts4.5": "1.15.5", "ts4.6": "1.15.5", "ts5.9": "1.15.8", "ts4.7": "1.15.7", "ts4.8": "1.15.7", "ts4.9": "1.15.7", "ts5.0": "1.15.7", "ts5.7": "1.15.8", "ts5.8": "1.15.8", "ts5.4": "1.15.8", "ts5.5": "1.15.8", "ts5.3": "1.15.8", "ts5.2": "1.15.8", "ts5.1": "1.15.8", "latest": "1.15.8", "ts5.6": "1.15.8"}, "versions": {"1.7.14-alpha": {"name": "@types/serve-static", "version": "1.7.14-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.14-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "750004cda49886c519c04b6baef789f378fd75d4", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.14-alpha.tgz", "integrity": "sha512-S6nCoSADlNfx5wuxcH/UNBa18hnuXhqsLMT/3qAFXUCIE/PPhsasy57K1cH76riV7PM8S0RKnZWJqHmUbo1qyg==", "signatures": [{"sig": "MEUCIQDGFUVR6GKdOUZDFRau+XSZx2NpDTF8hecCt62EjUWESQIgH/xwFKDdzI+rCzw9alL8frv+uIMsq7HQxKz2/C5tVwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "750004cda49886c519c04b6baef789f378fd75d4", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "_npmVersion": "3.8.2", "description": "Type definitions for serve-static 1.7.1 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"mime": "*", "express-serve-static-core": "*", "@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.14-alpha.tgz_1463511291884_0.7046071905642748", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.15-alpha": {"name": "@types/serve-static", "version": "1.7.15-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.15-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ac0886a1eab650d95baa4a655d913cdbeffd7cfb", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.15-alpha.tgz", "integrity": "sha512-n1a/FP/QpjVa74AjOuOvgH7TtA3Rtve1ezSl7v2ddvvzLfeO9rULj3nYBLilD7fJ4l8MNUHXaVKtKcn+fA0WyQ==", "signatures": [{"sig": "MEUCIQCexUwZdBz2W5Po++4TzlsX7EGLXJroxAlals1zBk8+UgIgK5r++u3LBdZWgRdHijNSImzKgX32ZQuPU+iLKqGcc0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "ac0886a1eab650d95baa4a655d913cdbeffd7cfb", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "_npmVersion": "3.8.2", "description": "Type definitions for serve-static 1.7.1 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"mime": "*", "express-serve-static-core": "*", "@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.15-alpha.tgz_1463696501909_0.3715952578932047", "host": "packages-16-east.internal.npmjs.com"}}, "1.7.20-alpha": {"name": "@types/serve-static", "version": "1.7.20-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.20-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d099ed7c73101db27b0cd02c246f889388fe38e3", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.20-alpha.tgz", "integrity": "sha512-RvLMYVdi8A9zfay5bFy7Q/34YKN1Xxab7Paui62K9rYT2JFK5hKABVVu4ps/yEeeBoqM0ch5vyePnnJZBSEnmQ==", "signatures": [{"sig": "MEUCIBzCgtS9jlVrUT96VjEHjWvI9UB2IzuUHPGdUgo+UySIAiEA1GyN76MgriLp3edd3EjaK/ES2W7ZF7nDiIJklHPDxkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "d099ed7c73101db27b0cd02c246f889388fe38e3", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "_npmVersion": "3.8.2", "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.20-alpha.tgz_1463776805511_0.45362257794477046", "host": "packages-16-east.internal.npmjs.com"}}, "1.7.21-alpha": {"name": "@types/serve-static", "version": "1.7.21-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "140f9074b80320fb1b506d296b4e7b70d7bd01c9", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.21-alpha.tgz", "integrity": "sha512-DGIV768NeIH6R7Y5PyYpBwm/ZVJato5L/GyzB8BlGT4BqhC3CvNRc247k3IaYBhRTbJx278ON2VO+vSd6prLNQ==", "signatures": [{"sig": "MEYCIQC68anpaqhqisFd1Gi9w/wynHueYIZeYNi/ba/aFE2UWgIhAOt4s9OT2h7BRJoF2s6zW0pbZw6CtbU8py/JiW031vB9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "140f9074b80320fb1b506d296b4e7b70d7bd01c9", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "_npmVersion": "3.8.2", "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.21-alpha.tgz_1464155738491_0.6189186845440418", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.22-alpha": {"name": "@types/serve-static", "version": "1.7.22-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "3ce697c38988ca0e92305fd4bc8d2ea2d5f734ec", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.22-alpha.tgz", "integrity": "sha512-u4lhe3imnOZud5VYdTHFFuf3pyB0gKBfFO0R4I0cb4yATaUXDIx9s6xBMWCj1mc4pkT4Ym9RtAtdncHFZCkrCQ==", "signatures": [{"sig": "MEUCIDF/wKw7+35AltPR+88NamCaa0HYSMCdUe72mNUpumTDAiEAhnPLaWLbhmJdz9nehQW00DuE8kjUKN/U0V9jccJy7yY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "3ce697c38988ca0e92305fd4bc8d2ea2d5f734ec", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/mime": "0.0.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.22-alpha.tgz_1467405387266_0.12126819044351578", "host": "packages-16-east.internal.npmjs.com"}}, "1.7.23-alpha": {"name": "@types/serve-static", "version": "1.7.23-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "eb9a62e5759d8ec7a1f97ab145ffd07a2f238c79", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.23-alpha.tgz", "integrity": "sha512-0o8CGND3r4RsxSq7H2cahOFL/5Nrxk0UC72JeNf1KYWiEEbv2ERdj7ubQywvJf8r7LhxmO65pSyoljbGGRnyow==", "signatures": [{"sig": "MEUCIQDDtZdhMHmqr63jJgzrwdOk4JDVdDOqgJroXgjs5qoJYQIgE2POxo+P62OtXr1rwrj9YW1rErvL/QBLALKTMSguwPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "eb9a62e5759d8ec7a1f97ab145ffd07a2f238c79", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/mime": "0.0.24-alpha", "@types/express-serve-static-core": "4.0.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.23-alpha.tgz_1467417286473_0.12536202976480126", "host": "packages-16-east.internal.npmjs.com"}}, "1.7.24-alpha": {"name": "@types/serve-static", "version": "1.7.24-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "07db9315ffceaa3cda6d5a0a120a264ae84926c6", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.24-alpha.tgz", "integrity": "sha512-nytB7srWD0V/U5foANjzCZy8optgtdJ9HiHH3EfopHwz2cXxcL6sWiNNqwS09M6CtGYtoiJmstzTHX1RLVAVSw==", "signatures": [{"sig": "MEUCIQCRhZ02X83vfhdg3Ed0WB0HvNU0G1vfQn+GlW+vxsaJ/wIgAuVfh6OR2ene/TnymZAoytOSRgwOaN4OgA93O0qMJkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "07db9315ffceaa3cda6d5a0a120a264ae84926c6", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/mime": "0.0.25-alpha", "@types/express-serve-static-core": "4.0.25-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.24-alpha.tgz_1467430003095_0.5076400749385357", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.25-alpha": {"name": "@types/serve-static", "version": "1.7.25-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "a5c560dc71cd8a67347ae8710a5cc740c2963713", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.25-alpha.tgz", "integrity": "sha512-R4QHC1GGtW1vAiG8XUiGxxg24lZ/ZM0I3Qs/+1wh95DH+4KqxV0MXSd2FoEuTehDksaW/gCmVfnHzS/EExJJ3g==", "signatures": [{"sig": "MEUCIEcJpNNqZsMEPMWh4ubnLQ7GPTy3SEwhkZRf4QuY+tKVAiEAzyD0q+JBhGSh6riK8ef7lX75h1EX2JwsmXKbp2rjk6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "a5c560dc71cd8a67347ae8710a5cc740c2963713", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/mime": "0.0.26-alpha", "@types/express-serve-static-core": "4.0.26-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.25-alpha.tgz_1467595210121_0.07257580501027405", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.26-alpha": {"name": "@types/serve-static", "version": "1.7.26-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "78574a2dfb36b70ada5c62e4783ecec62ffb3e6f", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.26-alpha.tgz", "integrity": "sha512-j7/idTyBgMWeupCZE2PGLpNiX9hO2+Nnh8S7sKJ2E9qu8cHrXo4oSfu32TznuAhEkAjNj4i0Mf6OIVGS3cXE+g==", "signatures": [{"sig": "MEUCIQCItzqlx22QmIBZttp031qYFdRcVBOQAcay6LnKy+5mhAIgPrCDyOYL7axvJEJRQbJrvZOSHfrd+1WU8scteflUI5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "78574a2dfb36b70ada5c62e4783ecec62ffb3e6f", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/mime": "0.0.27-alpha", "@types/express-serve-static-core": "4.0.27-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.26-alpha.tgz_1468013144094_0.7655938395764679", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.27": {"name": "@types/serve-static", "version": "1.7.27", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/urossmolnik/"}, "license": "MIT", "_id": "@types/serve-static@1.7.27", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b1c297ed33268d02c27e79015f1efb4007cd4eea", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.27.tgz", "integrity": "sha512-pMcns0LP3ojBf1ptanfhfLMy2Q+DFE8VCeBWfLDOyNZKjQ9fELbRxx78A0a1ZgwHWxMIAWEV1Dq2/2n5UO/41Q==", "signatures": [{"sig": "MEUCIQCb/r8N2v7AG/sQ1TsWke33gH5Cgl+HuZlTLqoNQ4gSlgIgE5df+T1IPbJadR7P5Rfcdr+Rvq+924W4+noZM/3Yeg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\serve-static", "_shasum": "b1c297ed33268d02c27e79015f1efb4007cd4eea", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\serve-static", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/mime": "0.0.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.27.tgz_1468512125845_0.8891745649743825", "host": "packages-16-east.internal.npmjs.com"}}, "1.7.28": {"name": "@types/serve-static", "version": "1.7.28", "author": "<PERSON><PERSON> <https://github.com/urossmolnik/>", "license": "MIT", "_id": "@types/serve-static@1.7.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "20ab7299c74a6a13ed4566b336f8ecc820741998", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.28.tgz", "integrity": "sha512-LMX/e1UyVZl/WqpY9Eufu8/iTAi69kUDIDr6r7jUFh29/QO8LdCm8udQxDg9AX8swUo8dIkeYbSIacnMrXlPtQ==", "signatures": [{"sig": "MEUCIHfoNh5Ja/S7MYdOsHPi9myBvaOr/K3ZuU5d65IvRIWeAiEA+TgzxCji9kt59KVAsW3W4bnsViyixL/RUEKG1TOq7yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "dependencies": {"@types/mime": "0.0.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.28.tgz_1470154237927_0.2991018558386713", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.29": {"name": "@types/serve-static", "version": "1.7.29", "author": "<PERSON><PERSON> <https://github.com/urossmolnik/>", "license": "MIT", "_id": "@types/serve-static@1.7.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "92fe990749344c8b45a9b46ca980f149b41fc497", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.29.tgz", "integrity": "sha512-UX8/vuD9qrPFfQbQoufHDVCrs2Y02NZjzUKwxbkhEb61nmYzTCYHUZuoZTOYBKoEBHn6AzDL9XRSf0Ba71PWSw==", "signatures": [{"sig": "MEUCIQDsLVu2F6uCcvJh2uvy40pOVz6MyVazrVsZOcoHyVhxxgIgFIeuj501Um6X8ZhHpTw8EFR5df9tO1tQnEQG8r7BjWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "dependencies": {"@types/mime": "0.0.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.29.tgz_1471621319073_0.5145616962108761", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.30": {"name": "@types/serve-static", "version": "1.7.30", "author": "<PERSON><PERSON> <https://github.com/urossmolnik/>", "license": "MIT", "_id": "@types/serve-static@1.7.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "de0153a44cf90e91d3c796da7a40b79c6e247bff", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.30.tgz", "integrity": "sha512-/ebJyYerDKD8tK2YWlFWcOWBwtJ1pBHK1Yqgqv9sDw/ROYMq446qkAxH0/2ULc8N220xSM9wi0gSbxzlIitK6g==", "signatures": [{"sig": "MEYCIQDkz8N3PGms3A3ggNaBzpd8aRYT2tcBMoF2Dw68txrrsQIhAOZT+T2FwGD+3VgLmYKRtlQ7jZ8eGtorwdPBlq8zHiiH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "dependencies": {"@types/mime": "0.0.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.30.tgz_1472151553021_0.4135680755134672", "host": "packages-16-east.internal.npmjs.com"}}, "1.7.31": {"name": "@types/serve-static", "version": "1.7.31", "author": "<PERSON><PERSON> <https://github.com/urossmolnik/>", "license": "MIT", "_id": "@types/serve-static@1.7.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "15456de8d98d6b4cff31be6c6af7492ae63f521a", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.31.tgz", "integrity": "sha512-ZA6hU8nps4jY6pCNDTjGlydERrMFyuOD3cJjYOy/nEnrQCA4wmZ9ibHppuMDpwJvMlkMQRwnXvMiHEZQHgFM6A==", "signatures": [{"sig": "MEQCIFKHYNBI7k94u8lmqENVldcniGzD+YZbw3Mn30LZ/0d5AiBgNhPPs4MLieNytYwgsI+pvYMWmaEhPnk/GgZuzGXTWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for serve-static 1.7.1", "directories": {}, "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.31.tgz_1474308726626_0.34410145902074873", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "6dbb5dca075bde179287659bb4382b64701adb508c89809310a1ce6a2b87ad83"}, "1.7.32": {"name": "@types/serve-static", "version": "1.7.32", "license": "MIT", "_id": "@types/serve-static@1.7.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "0f6732e4dab0813771dd8fc8fe14940f34728b4c", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.7.32.tgz", "integrity": "sha512-WpI0g7M1FiOmJ/a97Qrjafq2I938tjAZ3hZr9O7sXyA6oUhH3bqUNZIt7r1KZg8TQAKxcvxt6JjQ5XuLfIBFvg==", "signatures": [{"sig": "MEUCIQDlN1zjKOX3M9DO/8wYDWrytPJj86jmqGqMKAVXJgRgnQIgdadmwlTZ5ra2OCknDoQONbtEyAuxY0Zmlj8JdA/7Zyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.7.32.tgz_1503353052180_0.10676383110694587", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c0dac04127ff734150c992cc697e39e0374266604258bd6663a3c651531a9066"}, "1.13.0": {"name": "@types/serve-static", "version": "1.13.0", "license": "MIT", "_id": "@types/serve-static@1.13.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}], "dist": {"shasum": "2ef359d8d5982bc011a7a8fa36016e629e8b7635", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.0.tgz", "integrity": "sha512-wvQkePwCDZoyQPGb64DTl2TEeLw54CQFXjY+tznxYYxNcBb4LG40ezoVbMDa0epwE4yogB0f42jCaH0356x5Mg==", "signatures": [{"sig": "MEUCIFnfgyHnA6aFDEwb4D9i/j2y0/rRypIRpgDadtjeD6J2AiEAo1AAo9lrZ0uj/axj9aXMy/IgkGLmrQprhnoVc5fHgPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.13.0.tgz_1508957465271_0.056656995322555304", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ae0dc7ea3953f2124d01c6a9e6bf70ae53e9b95b4b77eb05a69c59f8912bb3be"}, "1.13.1": {"name": "@types/serve-static", "version": "1.13.1", "license": "MIT", "_id": "@types/serve-static@1.13.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}], "dist": {"shasum": "1d2801fa635d274cd97d4ec07e26b21b44127492", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.1.tgz", "integrity": "sha512-jDMH+3BQPtvqZVIcsH700Dfi8Q3MIcEx16g/VdxjoqiGR/NntekB10xdBpirMKnPe9z2C5cBmL0vte0YttOr3Q==", "signatures": [{"sig": "MEQCIGoUHeEKNRanqIArAcN+8SllJDSQ/0grF5TItWVMlx1aAiByHBA8UZPDSWyDrvzIVr8PoAKWWDfQi8XENopcU5lXVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/serve-static-1.13.1.tgz_1510240686767_0.3011777715291828", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cf21e4794995b00edf49695fcbb6ae17789bfd3889db5ef759e191ea838916ed"}, "1.13.2": {"name": "@types/serve-static", "version": "1.13.2", "license": "MIT", "_id": "@types/serve-static@1.13.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}], "dist": {"shasum": "f5ac4d7a6420a99a6a45af4719f4dcd8cd907a48", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.2.tgz", "fileCount": 4, "integrity": "sha512-/BZ4QRLpH/bNYgZgwhKEh+5AsboDBcUdlBYgzoLX0fpj3Y2gp6EApyOlM3bK53wQS/OE1SrdSYBAbux2D1528Q==", "signatures": [{"sig": "MEUCIB5QHkFfeiqcKXcl43GL2FCndKhR4C2izSUFqNF8nse5AiEApQOqbZ2EkSq1jJ7J8Qe0ZaLE3icxYOFeL/q6d3oIioA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa50KDCRA9TVsSAnZWagAAJI4P/RQkAVWI7q0s86pYZJu2\nWJqtvu/fTz3kdCXGyDsAln/8DdgZ1kch14eW8MO3jN3eyvCdNN3RkFdbkOdY\nLpbPnEWXIUFkLBw9z8u8aGsuNDkdotPdvu106JcKE5jWZ4fyYUpuUj/lE8Nm\nbycD3lNPMZk1bUoZEyUjRF6UZHcB3wFJ7qz4T2ScsdpPehb84FgIgM9S4WYx\n7UvIdK0FgtHhdwaxPVQjdmNt23uSvDf1WUrkd/rkIz8Yk2x1/oOgsz1EPA3q\nUVyCsY/+i31tGH9ColrTkST0W+0Vx5k4gxfufN+aG8Uh0P0GP4zW28nXWmRU\nZTscMsYwpCiSozIQtUjXIB5nRUMsqjDpLRcOsgA2LNxSHJHU30607xCXyMnE\nfPmhskD+OsAK9ZDcNtOeDffdZxWV6tgf2sG88OntmX0m89o74C1eiYvXWboM\nY08wJQJxZ0qD/eZlr1M1gCSVXj9LLHcDmwRJh9cgVK9wZA4yG8K/+1EWPzzt\nAw+hs+LNOkPeaPE236EpsN5f9bPwUadCLPIGrJLeNdI72RDsPXcdAputQa9S\nDOl6IJ+HZSVG8ABukavHoz8VZ8xsoNz/CLfwOFb34X3a5b+9z0l952SR2ezq\nV/ifd2IVUmVpk5nV0P41XqgUHrC2SFm+Nfxh2gkuikdZ73YxgEyCFB7mco71\ns/ig\r\n=7pZH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.2_1525105282795_0.7865769265469493", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3c454d8447dd409850aab805f4a9ddde32dc4c7606505c4e149b1f938f52728d"}, "1.13.3": {"name": "@types/serve-static", "version": "1.13.3", "license": "MIT", "_id": "@types/serve-static@1.13.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}], "dist": {"shasum": "eb7e1c41c4468272557e897e9171ded5e2ded9d1", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.3.tgz", "fileCount": 4, "integrity": "sha512-oprSwp094zOglVrXdlo/4bAHtKTAxX6VT8FOZlBKrmyLbNvE1zxZyJ6yikMVtHIvwP45+ZQGJn+FdXGKTozq0g==", "signatures": [{"sig": "MEYCIQD5JLneMsLO7COBTGycyF/6gbBmpU1k7izr+e++vNKCmgIhAPT1xtHQo6eWEw17F3Ks//FeLpMzAAHGW2nCCeSdJa8R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWfgrCRA9TVsSAnZWagAALqsQAIeabyMukSqtf9lfvb/4\nKQtponh4uytmhixBa+PujNo485zrqlVC0atJ+mQUJ97GnHt4747P+dbkQO1V\neKitCivsrOT8DxcqbMxaIHyiIQT2p59feaYpafgmaxaqB+0tq1R89f8GcDp5\nybk98uVOASdqNFaLlghQ8aNc9jq+7hb4Q52JRHRDmNBY38Nsww/tyGsUZl0r\nbF4LuT3loGDsQCJnMheaW1zQZsvAIzXqJwAUUOOasAwMh3XcC51XdsrQsw4b\nqOBBSp8jFvBcSH/zfIZlXTuQPuLCLlx5aZXzmNota7yDf78I65hQ8MI1KdN0\np4EFPXZ9hxfhFVyxz81gENWvEAckdefHy9FU1pVLpH6zFwnA6U9dV6gK2/oZ\nSh01CTAbMPwTSTnKIxSLJ1ZHyeDduDfrw40s4uLtqY6+F6d9zVLdrvNdxbrE\nkhCSBSEmEo7Lzc4dJZP1LpQXOSpnWr+ZvxtYTda+GqTiYUSSXfrfdisWV3Sp\nfmWCWVQXGiT3pRXWLZ71l7eh8Y1pxKx7WhoVm7s+UFqaU4/NDA4ei9vOHqO6\nLweQwKHRk0muXv7YU/mgJd4bOXwdqPgtCDKgA0PeBRBlUWv39QsNAokvB3PA\nCU8YH5JbBLiLNwWHIK2FCb4gQAWq++m/v4r7NW5WQZ5N1c1sN410U86kL3GG\ndRdo\r\n=dYIz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.3_1566177322399_0.2093869376630504", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a1bdf5606e9234e72aecdfcec0e66846d6a7e9d7cd87332ef829e4db0a1d56d5"}, "1.13.4": {"name": "@types/serve-static", "version": "1.13.4", "license": "MIT", "_id": "@types/serve-static@1.13.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}], "dist": {"shasum": "6662a93583e5a6cabca1b23592eb91e12fa80e7c", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.4.tgz", "fileCount": 4, "integrity": "sha512-jTDt0o/YbpNwZbQmE/+2e+lfjJEJJR0I3OFaKQKPWkASkCoW3i6fsUnqudSMcNAfbtmADGu8f4MV4q+GqULmug==", "signatures": [{"sig": "MEYCIQDosUtbPSzxwwzRj3zfSsP4QE29JvyxKQO/8FKkaRgfaAIhAPNIRuh4OLXwSEN4ifwOiRdytXAQAP93t90tO25l6NKP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7276, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexCKNCRA9TVsSAnZWagAAAsIQAKMqr2erAk91ol535JJC\nnBFk+JDCfAcYnPCVrqaWuUG94KL6KWKmFdf4+JkJEWwRJ1OS2yHeOTj1yW+H\n3fs4WDLzNQ84JQSxk8lLPx04/Iivh8D5fFqMjH8xiHNUSFqjTQHsXjOunDOx\nRzDBpbAe1Xrz4gRVFosSuD+yzGBSe3mlmD8QD+tbmHKUtJBhA7K39rcN/deE\nFzlC5CKpgjHnxET0Hf3uUxvA9tMry7RjmugSyy95ze6oPhMSQ1k/+FPXFuB7\n7Mf/p86yj2zWHmcPlntB9HiYywwvmBNkTHkjz0iWh0T+x32rnICLCRo+fp5A\nLk31T48xrv1aBBs+mBEuMV6PoaqoCw/7p5rZll/Qtr0Y4b0tYOt/FUvHS7pW\noHTdt6CasN2pgxfQoLhA4s/GJLK6o4QVyzm++mraJjr8dPKmT0fv/LWf68VM\nTfkb6cE28AuQhpX9olbrdZdmxdCIkmRHMDNpqWV9klptOfa0Ieo8MdjiAB7V\ngRXskmVjky17a+wrIMN81WLAYs/f5k/mwsKuImYQ/N5LJ8xApFGkRvdT32hA\n56OcxZ9RigWRCxdhJt1Wu6Uoi8/XZ0ZLhqyUKoIg+OQqF8RHzftx0VcMVC/e\nYKXK7wHApkVH46OypWioVy6QqChjiByj3fp9BKBXsNFAW+8j8sWoTTxEbaYp\nMxDv\r\n=rFBV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.4_1589912204717_0.0023331792939806917", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "995047f1403eb6718b017516ae188546f4e53f0dd9894a502f36ad62a5f3ba67"}, "1.13.5": {"name": "@types/serve-static", "version": "1.13.5", "license": "MIT", "_id": "@types/serve-static@1.13.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}], "dist": {"shasum": "3d25d941a18415d3ab092def846e135a08bbcf53", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.5.tgz", "fileCount": 4, "integrity": "sha512-6M64P58N+OXjU432WoLLBQxbA0LRGBCRm7aAGQJ+SMC1IMl0dgRVi9EFfoDcS2a7Xogygk/eGN94CfwU9UF7UQ==", "signatures": [{"sig": "MEQCIEedaz408EjqpAvJKMDY6bQUflyXlOcV85yzI6aSsI5LAiAhuoLnyVzY7PuHKmnEVaqZb43pg9iYoFuZBG/eiFVS0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfH86nCRA9TVsSAnZWagAAIUgP/jOiAGAHaqLWmtLff58S\nUAmtyPZ79UyFxboPE7BLBIxL3M1m08EoALUPQjB9f2Xv8+uoZqNkWCURdNMM\noxVf1L1cID+abpigNgSbRG4XLLIS2Jmlm4Px65h/7KDxOoBYxf4N0DEchwF4\n3kG48GT1JcbgX2O8jPs2amR3ygI6CuEISuX+iYivbnLLO+ensV6dRfLpgMNf\nKJBTGtD12287l3uhgT30b7aBRkgyc9/RG7GukwnCbMoplpyOY4X4HMAm0dnn\nmSIU6MSpGkBt4QqYpEo8T8Bi0eLMVgTzuJm5vICgPyn38OE0NwGXU05bVM0M\nlLHuKo6JQG+joVHZaA0hZ4HoPJCLXZoK8hnEFBNMXdNCgXXfjravez9i6eoV\ntJV5wpj5xvLFs68r4hDi9ilTOLOGGECk8esx7tBIwFAMtLXwTLVuJp4Opl+t\nYAePxKF0EWIGJgJ57JynZcOzjXbcBLE5YZzrmFEfxB52nHJ3mbPggzG1lUYc\nbl6DG3GdlUCJTQvc5vLOZrDHKhNP8mfxtTdz6EZIIv4rUT4A1+XdVAY5JWyP\n4EQD6f5rMYLl5TVVrbvL8grBODCZXaLQVNgclpi2o481igpVL8+OZDqI+CPI\ncGfNKw6idTvSEXZFCGrUheJ/ufHrxJiAJaLDqQWv7LrYLlrfrLVrT74RYGob\nClbS\r\n=ZS5a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.5_1595920039346_0.41947239172763884", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "be710541b956835e6ef2e8e82d2d69fe6c61944dd361fec322155ed3ea7344e2"}, "1.13.6": {"name": "@types/serve-static", "version": "1.13.6", "license": "MIT", "_id": "@types/serve-static@1.13.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "866b1b8dec41c36e28c7be40ac725b88be43c5c1", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.6.tgz", "fileCount": 4, "integrity": "sha512-nuRJmv7jW7VmCVTn+IgYDkkbbDGyIINOeu/G0d74X3lm6E5KfMeQPJhxIt1ayQeQB3cSxvYs1RA/wipYoFB4EA==", "signatures": [{"sig": "MEQCIAxk3wVtMIlQXtiAjIW4ezzwmVqZUEt+qywyYsp/YLC6AiAofIeGiARVRkaDapMPPsxuRXRgrVC22EMPU1M3vQ2UFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjfKSCRA9TVsSAnZWagAAz2cP/3wHxfleZhwuUzwd0+V0\nmgzuRPgL2tZv5HU+S0YOwmCF2yZcesn8U6/WeEa1AgJJukbTgh2AAnBBigJn\nD2iwHhSF/A3Ftvsc2ermiB137gBXakpnN+t0oBQMOQjpVE3HixekrYEnfpcx\n3gI7C6VN5xjG1PPg2cCkWS7gTrWAg5ddcqS5ZG3J4tSSavtotaMYvV1m3HXX\nU0apziVoZi5JKzezFOLsgl5lj597M89xS/yBJzGRpzJCtMCWkxj7AUGWEuGb\nJ6p8wgppi4QyjTL5FMrEtihMgUChYNfMBCcgB4Rq17JFYIJEQpGYERfqlUjq\n+jaffkViit4z0pwcw/JOAZA98lKnjuH9r8RBoByL3IYxnRlJDNpEVMSKJ5FM\nP3k1XvzQWUaBkQh4oRFLyqKOumUZSTStlogiLov7Pfkb6VxW8ywKJZdsgsbl\nzXxAkfEBYeVltTC78Vk7VSl8GMkwrpO9ZnJUSMhr52fAMV3rqU44MSN3KAtt\nuHqPq47wR4AF0+vjQXJPFq7TVDfo2l3LuvV45RflpKmkLGsU9DLeH/Y4oiid\nTqkusgiS99fWeuOofgNMfc58GjZiMS9vLdll4E9Y5UncQZSvv1StWn3R+SAT\nIJoknc8qgGhlqr/FILF96B0vqtQpQUmd6JwHW+ekiJZXu7PZt/maV2MrKTgb\nXS1k\r\n=M4Fo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.6_1603138194391_0.14410151753624456", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "acfd83562eb5b0e8035d70258d81f8228a8d0f959a86746e49d59c3b82084e7c"}, "1.13.7": {"name": "@types/serve-static", "version": "1.13.7", "license": "MIT", "_id": "@types/serve-static@1.13.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "e51b51a0becda910f9fd04c718044da69d6c492e", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.7.tgz", "fileCount": 4, "integrity": "sha512-3diZWucbR+xTmbDlU+FRRxBf+31OhFew7cJXML/zh9NmvSPTNoFecAwHB66BUqFgENJtqMiyl7JAwUE/siqdLw==", "signatures": [{"sig": "MEQCID07ZKegRfuad3PyVLfCR5GK0dFeSuQWukIavnjfd5x6AiBWj6qN1dJHy8qUw85+9CV94cNP0twGgt5YEGLef8LXNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqzhTCRA9TVsSAnZWagAACWEP/0xPx6345bpoX++93N8a\ndz1DMqrI00ILexbXOt8kVt0WENRC1A91BU5iV4KJiHS5sZrQxDVfALxMcNI6\nJIYMM/XWhOnHHU4QY5JpG273iblZ5J+yXjhXL8zxBlS90XREQEaYNLhq1is4\nSPVcaK/cXq+yTrt/VszG2DsB2t7OYphEsTt+OjRAa86Pk8tEzV3SAa13W/rI\ncQLSOg6Bqo6Q/3ZEKx99ZGryOP8pf8sOtfxESURdd/31gk0CU+B+ZPUXUYdR\nGaA+QQM1QVesAQ6LpcUJBdmnrcWL+pecz/TOwWOSDsLWPOrigAQ1OUWv2nzK\npkUZ3XFxOZdU871ENcdfmkLKe3nB/q4Ti4ktybffk/rxcy+t3nojFU1O+Hrk\n3t3+DQY0n3MiGHipY50IUf/3mapHHp20U/aLEtAk6rMoqUVnHGmHJvR40sWs\nmf+8lgTId/bJlMgOw5vpSjtdi8cJurFmCWl/TFvO/MdcH7t2+KSOwa+is6nc\nfUTQz32C8owxKADZwHcYUOwtnW3l6QWX3JBucCUBPDdKrZRaeHnPQGRa73Qs\nfDkkai1o9GYiM7nYkF3nFoziHi5zYLxEkxiU1/D8WLnfpZt6Huz8l/+COAsU\neRP5grCuyYIYKNGfN0H9R0C5sMqqliXuW4oQsRxqX7Woj4j2HZkP98TMXaGv\nlC7P\r\n=b0A5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.7_1605056594698_0.15907841300417536", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "be124a1f84a2da60b74a353336cc720997a7c03380c7d9e5b7d00ccd5f56f7cd"}, "1.13.8": {"name": "@types/serve-static", "version": "1.13.8", "license": "MIT", "_id": "@types/serve-static@1.13.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "851129d434433c7082148574ffec263d58309c46", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.8.tgz", "fileCount": 4, "integrity": "sha512-MoJhSQreaVoL+/hurAZzIm8wafFR6ajiTM1m4A0kv6AGeVBl4r4pOV8bGFrjjq1sGxDTnCoF8i22o0/aE5XCyA==", "signatures": [{"sig": "MEYCIQC6jJy9yrQpaqgbZaRKQG1KEzbeGVk1W/CcwAXK/xI4XwIhAPUlCv3N/j/ULL62LddvZnCqkRQzOiSRkwsg1W1KHE1R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftGjcCRA9TVsSAnZWagAADSAP/21CwazTAMWZTzxnyl2+\na27bIfFn0wCl5MT8m45T3Y4cf9SsEUFDxLogB7K1W8LfMPUFk5s7mL5dCems\nkhweglB6324JC25UKxnPl7y79TcH1xwGjb5xHInDuFg/SsGZ9qrfyDOX/XOS\nh193lokTc9/L5xPU8MZ1cze5oenoYORipGWTKb9L04Sc7A3UaBimdKkiS58t\nCX75Ju17MR+TYkazfNCQscFv7iNJ5RxWtTJamZd3RTJWqrwMxNkp73ad2Y+H\n2am2FhdYBoNTCMUOSoYYjJ+8rrhwbl+yGRGFrTWo6cZ+abchmwYvyzroeJzx\ntWF9lyBHaljqKqgldgS+2EdFtObxMC63vAVZyxMU6OC/OXJMvcgocFQhWeJV\nnWV8+OzvzGCHH8Pr0PaGn0Cx4WpMJ9RkC/UJtSaBmcnhi4f4wi0BYf7NUdkg\nSYAZgoO3UCDdqZsy0QiVdROOdhVi6rLyko9RzEU3Hmw4NqAgyRtqgLhqnxak\nIJiTeFJnwm+qPa/32xuzh5EARWc2nWqdS432FUI1a63zzksWb2FNO8fQA3/C\nqtM9DWNsbftu0F4sXIlKbf13OWLXdAdcyIiyvbfDKDjauq4PYPATAyG6cEI3\nebzE2ygts7nvlhUjA54xHfT9+zZEv+muwsJ9VDwlB6Ml409QIbzoxpcAbRvy\nacY3\r\n=dDHU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.8_1605658843583_0.767666439470303", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c80d90d1e2e13bd1f7faaa45b3d6a85a48ff07783977ae9dc956309579b066dd"}, "1.13.9": {"name": "@types/serve-static", "version": "1.13.9", "license": "MIT", "_id": "@types/serve-static@1.13.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "aacf28a85a05ee29a11fb7c3ead935ac56f33e4e", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.9.tgz", "fileCount": 4, "integrity": "sha512-ZFqF6qa48XsPdjXV5Gsz0Zqmux2PerNd3a/ktL45mHpa19cuMi/cL8tcxdAx497yRh+QtYPuofjT9oWw9P7nkA==", "signatures": [{"sig": "MEQCIEAxLLKxYUZi/caP63iLJ+98oUarfK3Dema3Xcz/E8feAiB7WP7YYepRfcsEv+F/1hb3EyVcue1G9r8mxbXQCqDWmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBZy4CRA9TVsSAnZWagAAWNsQAJwnloTAvw8Z/cz8DDw8\nSOHPyb8X9LwXB4CEnhpHFrZBdrBIeLaLYTHoatA+QqwuotyKDCE0nyb/JgQ4\nfmFVl5Q2egcMYfBjQ5MedjLr076RyHKEPquzeaZSgjYSi2dg++1saZW8Lk2P\nywAUaSAAouLoEzntrtE7BYsVjCs74axbv45IfX5Sb/vgs9vMvpHUMw+8lENA\nf4G9yepDosjP8ODDRuPkupQmfqnu1yWb04cJlbYJSsIuy+sK3EEdJu4Z8BrP\nXBSZgELYEhypNAb8fmnD4OJSwISY7trZ+0CTNdB0omk0WqjK8DI1Ml68ZriW\nlXbDxJaENe09XdH1QkY25scXr+CgZaadWPkZZCrA1UBJe2f1Fm/NDUDWxfsa\nMabClqrvyBca/1zTozuOPB4wjj7Ge1VuYAxZrWsbW/R62ZGoSY8f4N5T/NpW\nmBWAb9s/zv81O9yHGbuHTSnFfoSssi7WLc+WShQHX0TqR8K62x5F7Cj5mrwh\nzNbd96ZaQEggPAa9gDtau38W9lk51UoUkdK63BESFdnthN2L4jazNajXHoP1\nL1JUnccv25mTm3RDTyT6ga+MCfGrNbGZhxr47xJ5u/ceV4mZyP6bUrgmtwGF\nEk+rzTBUvz9iNA6NFc0G6UMFvDF3sGZAwr/h7XeOaDr7s7QHZvkyf/Nc2Mgr\nifrZ\r\n=wrr7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.9_1610980536425_0.8857825367167085", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "128f8f2571ac1e49c45cb7906b8de2eca4e5c41dbcc05194d377a3183a964abd"}, "1.13.10": {"name": "@types/serve-static", "version": "1.13.10", "license": "MIT", "_id": "@types/serve-static@1.13.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "f5e0ce8797d2d7cc5ebeda48a52c96c4fa47a8d9", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.10.tgz", "fileCount": 4, "integrity": "sha512-nCkHGI4w7ZgAdNkrEu0bv+4xNV/XDqW+DydknebMOQwkpDGx8G+HTlj7R7ABI8i8nKxVw0wtKPi1D+lPOkh4YQ==", "signatures": [{"sig": "MEUCIAMmHUJ+FS5IZbtMzETyA2YZiABxKGYMrCiZC52lkRBJAiEArKdj0IUCDm9J68yDhz1Fo84pxxdcptetQavTIl2pDXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5IwDCRA9TVsSAnZWagAAnpIP/369t0OzzW5zvaeS2kkQ\nvSjwRWFELuFJGAhxOpbnkVT7XgtNfcsn8kuZYT8xU9RXSo6SrJjIwCfJ5ymR\n1XFTTzrKIsTwVXZTPSPxpkR/7vW6lCxk76w9MVdrHrBNCj6/9JvMv+GtMpKJ\nOAxEKJbwNNA/omgyt48Kajf4NKDPITvIIolZVAVgj/zyJ1pqgCLXXUqlPlD8\nJ4ueGS5LLWjS+iPCgKZ7q6RE1QNfLqI2VNHw+QXPoZIpmTEKI4MAheFAqwIc\nDExU5ZZYivGSw7N6YNqBNmjdFlBxyd1ADCAjkTtvD7BLEwMiAKgQbFYS9pny\n5aj3MYM81ElejJ30o340Izz40EMZ55+46Ovb2ZI43jum9zMAYZOT0fO+fksx\nDg0XbJkO8tBFBZTHCLuDltB8J+I+jXIwJBttbn3BN5mq8J1pEpuM9TYcDXX9\nbbsU8hHI3fAaqA9khAOG+DNK7de8i2RSHe5V+j4ByWIxU0NZuf3vZfCq6U2n\ncwDWTmERMUYTBi6nuruUPYrcO/QWL6L443GdHjFR/vowfsPsBykDCyZ2r8zv\nBcqRSxTIozWkJfhQfnnEuZ1AtmaBtmvcscCovtEQNtFYz32Uhu+uXiJjeVmm\nqIgMBR3J8rWIcb7aCU+uzzUXvdhQS7iftwN+0MAJrS0gV03mhdnGJ76eTcnm\nPrS0\r\n=4kBQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.13.10_1625590786872_0.7649628193027336", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d7ba4df44ab0b22c50e477e33159f25cc838b045f2d25519651a7d6005a9faed"}, "1.15.0": {"name": "@types/serve-static", "version": "1.15.0", "license": "MIT", "_id": "@types/serve-static@1.15.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "c7930ff61afb334e121a9da780aac0d9b8f34155", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.0.tgz", "fileCount": 5, "integrity": "sha512-z5xyF6uh8CbjAu9760KDKsH2FcDxZ2tFCsA4HIMWE6IkiYMXfVoa+4f9KX+FN0ZLsaMw1WNG2ETLA6N+/YA+cg==", "signatures": [{"sig": "MEUCIQCtup8PzzmM+HaHF+ndJ6m1m5vsvUI1Fd/03UsX+frzrAIgXUpUi6ENNC3v+wEu1WYvcZWC5yWSEeGObs4aWvZFVic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi5FjzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEHA/+NumAJ3gajBe0ThF2bqItCK9jXxvMIfeYubQy8KEIls5qeNQx\r\nE4vq63ze1gVR7s2VH7L6QzA1b1aEUpy9FZ3MT0Ehnu4Wu6WwIKP8G6erXdfK\r\nEkIDMdboa4vgzkWA3Q6R2Z/PtV3RYVTxYr12xX5ql5/cvTGnGPvRXWVdrIpA\r\nBEuBFo5ZH3cYz//vv+DqFdBUuhLK+snJuNqpRfiCbPrGpHPYsBzxSGgmzRuk\r\n6g66W+vLuDyiysijxLCtz7w8ZUZaBLaRFzCQlm61qlGpCd5DG+4jVbVD7m9N\r\nrwGuQL0ev9mk/hKJUEtY/SqUSyc66JqO1U5jcy/nfOMEVV6+OeSi0wKY2MVI\r\nhJmyXfsHGKQexIN5h6o5m9eC/XgPKMvVan7GMLw08ygxk8l4g+SrqZzh/vat\r\nVO96dbl9g/zq7V4V4c7XUHfdWZIst1g8rZJHPvvI7SQVsWDZ8IJrTFUrcV/s\r\nCsaQzz+jndo7CgR5kr1HRRdRWGqkT3GaaUL2kVFSdEp7uwqLx24IuSRDJ9wO\r\nqzYQ6QU0XndrRUABzZ8kKr0kdAnq1l1XhjS9iX5/t3LRGo2LWS2CbczdVF9z\r\n2oio87K3qgEvwfOKNfcQ+SfsXxhgy79et1xKuLC5oFv9g4wvo9VOZlFiPvCh\r\nkXFvo/GRupIzLp8IC46+Ccp/cN023avqatQ=\r\n=3800\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.0_1659132147515_0.91974568251206", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "23c6a1b5183c0a097f74914c31b60d57b77620f3cbef3be91157347745af24ed"}, "1.15.1": {"name": "@types/serve-static", "version": "1.15.1", "license": "MIT", "_id": "@types/serve-static@1.15.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "86b1753f0be4f9a1bee68d459fcda5be4ea52b5d", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.1.tgz", "fileCount": 5, "integrity": "sha512-NUo5XNiAdULrJENtJXZZ3fHtfMolzZwczzBbnAeBbqBwG+LaG6YaJtuwzwGSQZ2wsCrxjEhNNjAkKigy3n8teQ==", "signatures": [{"sig": "MEUCIQC244LtngmeKdPXSsuX5Ix7hdmPEZ7AQMExGIc6qOJiNgIgdHnVctv9rAdOXJfjBsrc1W2TLG+l9Kfr6Hxj/fTkPd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj97hRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAcQ/+JeC902Q239yS3rJMtzsyBMwlLLy03pzBJ12hN+rZ+WOlyKzs\r\nVj14vcX1gNdivKmMXHnAaD8T3rxmp6KaXdI8WGxdWUKJnzoXgb7b3zNjYSas\r\n18OsZ2+h1BiyTbwlH/xyyf0UU1Kfr/1mXDZnygBAnA1dfiHDcypztNGHvqta\r\nu57b9w3wHEgKBIhwkcaRGdE5XUjSrMnMP69yePG7ljk/7NIbkMd3NpAJlPOz\r\n+NSi+CQTIWHKaZ7ykv56G5UFCvBgKmIeVSJfHc8NNiN9jiuD/Hjd8qDoHw0A\r\nMSC8b3r1KYAylBcFKucFllGrBzaWniNsEarNd9VZuXNh1uc5HqT/2HnDbNfO\r\nFpAp4yKs5s1/oWpE3SxBrl5eRP7VsGlFBGxZTd4uPRRUDN5DUYvPH6NAl1/y\r\nK1AjzTmfBveCFJiRER6Aq8U7rGdte2I0gNQxJzk6xz4mcumPfYJNWgPfyRNS\r\n9DaXi5+H3+cJKpeba8rIuRcPQdwR1u4y5ncJPn5+/wkqFd0bZwwUl3L+sBx8\r\ngUfigtFx84IRs0eGzWIiy6YxjekwzPx0nW9z10nqraWi1o5tzjoKS49r7tbh\r\nWyKGiWJ3driSUqvypnEgq8OP5K2ZpcmH1lW2Wa3J3GqmCTjBvhohTrNDAEU7\r\nncYi3CfQwF9OdF5W1nT7U9Y7yRFuPanXkVo=\r\n=lSYg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.1_1677178961274_0.9824291049935552", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a5245bfbae31bb734209d348df3855b5fae08859f07b3de412d264f25a0b0232"}, "1.15.2": {"name": "@types/serve-static", "version": "1.15.2", "license": "MIT", "_id": "@types/serve-static@1.15.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "3e5419ecd1e40e7405d34093f10befb43f63381a", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.2.tgz", "fileCount": 5, "integrity": "sha512-J2LqtvFYCzaj8pVYKw8klQXrLLk7TBZmQ4ShlcdkELFKGwGMfevMLneMMRkMgZxotOD9wg497LpC7O8PcvAmfw==", "signatures": [{"sig": "MEYCIQC5fJxPOBFNWW7fc0uI/JIpV4PVl3n/iRxvsIvhP+54OwIhAKgoFxGWkQmLP73jdXYjFKP/hSBQn29BIiHkx3G7Y8U2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8154}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*", "@types/http-errors": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.2_1687507359826_0.42739158814745215", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "04c8d134e220f95c43a0ee00fbb63d05e47a2b0b13cf85b8ffe10147604d0e2b"}, "1.15.3": {"name": "@types/serve-static", "version": "1.15.3", "license": "MIT", "_id": "@types/serve-static@1.15.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "2cfacfd1fd4520bbc3e292cca432d5e8e2e3ee61", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.3.tgz", "fileCount": 5, "integrity": "sha512-yVRvFsEMrv7s0lGhzrggJjNOSmZCdgCjw9xWrPr/kNNLp6FaDfMC1KaYl3TSJ0c58bECwNBMoQrZJ8hA8E1eFg==", "signatures": [{"sig": "MEUCIQDO5dfWKrOlMe4GT2mHKsxEuKSmq1fc0QorhzVLB7yFGwIgZ0m1fj1wpIhUuZ6qI8Obpe5kfhjur+vYrQ5z1VN4ZO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8155}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*", "@types/http-errors": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.3_1695651821728_0.5788208970549873", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ae4eb8674b4f3fa11e70c81350f46476e7c4cd24a5d5ddcb016c20042273168c"}, "1.15.4": {"name": "@types/serve-static", "version": "1.15.4", "license": "MIT", "_id": "@types/serve-static@1.15.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "44b5895a68ca637f06c229119e1c774ca88f81b2", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.4.tgz", "fileCount": 5, "integrity": "sha512-aqqNfs1XTF0HDrFdlY//+SGUxmdSUbjeRXb5iaZc3x0/vMbYmdw9qvOgHWOyyLFxSSRnUuP5+724zBgfw8/WAw==", "signatures": [{"sig": "MEUCIB3UHDEefyhFZ5pX9wOP9GcdjRYJW8HNIy0Rny/VD9mIAiEAs66yE3Db01gGf176XHYIVXB7jmY8TmOGGXkDMHw4nT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*", "@types/http-errors": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.4_1697640147095_0.7897020579802669", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "16f0c5e308078f86abd5125c49639cdff2db5951d01e0c3106801483bf787f98"}, "1.15.5": {"name": "@types/serve-static", "version": "1.15.5", "license": "MIT", "_id": "@types/serve-static@1.15.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "15e67500ec40789a1e8c9defc2d32a896f05b033", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.5.tgz", "fileCount": 5, "integrity": "sha512-PDRk21MnK70hja/YF8AHfC7yIsiQHn1rcXx7ijCFBX/k+XQJhQT/gw3xekXKJvx+5SXaMMS8oqQy09Mzvz2TuQ==", "signatures": [{"sig": "MEUCIQDhzEGDb4g/DXXdXKtsEpcmmCiGhQlBdc/rybOoMY+2YQIgNBiFiCxrwl9Y1OdyfBt/Yca7f3ne6KyilW9W8/b0LoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*", "@types/http-errors": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.5_1699374061175_0.9805805374144461", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e5de8981add43a6c4ecae03e64d05e59248a932c097212e43eca3fcbc675e68d"}, "1.15.6": {"name": "@types/serve-static", "version": "1.15.6", "license": "MIT", "_id": "@types/serve-static@1.15.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "9cacd9b0b0fc5183ff0d5b27c1b1cad398113673", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.6.tgz", "fileCount": 5, "integrity": "sha512-xkChxykiNb1X2YBevPIhQhNU9m9M7h9e2gDsmePAP2kNqhOvpKOrZWOCzq2ERQqfNFzlzHG2bdM0u3z5x+gQgg==", "signatures": [{"sig": "MEYCIQCLzjzvtqxWxIbgRwYt+MkyMT8rkP1ZZsEapgRX2tT5swIhAPnY6WUzr2Z4pMU8rXDt8DLpv8hacEwv9GgazIkA0F/X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7795}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/node": "*", "@types/send": "*", "@types/http-errors": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.6_1712077697780_0.936377564961961", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "734dddd1130e06f77a90356f38cd94a6739806e09925700640155a291f12a830"}, "1.15.7": {"name": "@types/serve-static", "version": "1.15.7", "license": "MIT", "_id": "@types/serve-static@1.15.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "22174bbd74fb97fe303109738e9b5c2f3064f714", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.7.tgz", "fileCount": 5, "integrity": "sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==", "signatures": [{"sig": "MEUCIQDelB1SdD55uCPmgY/ZVQVJq1HDuFcilKyX0/+3A67zpgIgNdLWxSQpCE8yJJ7JJtAdar2E8mRE5ktKoL0pRunWW5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7790}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/node": "*", "@types/send": "*", "@types/http-errors": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.7_1712124473354_0.29129893585826894", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "781872d0ec274d5e3360acd4087e25d9d8440401b99181fe8cd1fc968aa78b14"}, "1.15.8": {"name": "@types/serve-static", "version": "1.15.8", "license": "MIT", "_id": "@types/serve-static@1.15.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "dist": {"shasum": "8180c3fbe4a70e8f00b9f70b9ba7f08f35987877", "tarball": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.8.tgz", "fileCount": 5, "integrity": "sha512-roei0UY3LhpOJvjbIP6ZZFngyLKl5dskOtDhxY5THRSpO+ZI+nzJ+m5yUMzGrp89YRa7lvknKkMYjqQFGwA7Sg==", "signatures": [{"sig": "MEYCIQDPOwJIrNgcwc/ydNjrMcQdSatleKqb5lNc0uTpkXzL0QIhAK9IHUaqQSbqrp4FPvxSSwdqh9ufTmPrB/w4pEMhHCHC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8030}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "directories": {}, "dependencies": {"@types/node": "*", "@types/send": "*", "@types/http-errors": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/serve-static_1.15.8_1749262738083_0.9745446304506704", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "9ececac158c4f4a101c67a8445ba5ed874de4a77e2e20fae569f6553b86ad082"}}, "time": {"created": "2016-05-17T18:54:52.462Z", "modified": "2025-06-07T02:19:05.426Z", "1.7.14-alpha": "2016-05-17T18:54:52.462Z", "1.7.15-alpha": "2016-05-19T22:21:46.414Z", "1.7.20-alpha": "2016-05-20T20:40:07.774Z", "1.7.21-alpha": "2016-05-25T05:55:38.970Z", "1.7.22-alpha": "2016-07-01T20:36:29.448Z", "1.7.23-alpha": "2016-07-01T23:54:50.042Z", "1.7.24-alpha": "2016-07-02T03:26:43.667Z", "1.7.25-alpha": "2016-07-04T01:20:10.639Z", "1.7.26-alpha": "2016-07-08T21:25:46.444Z", "1.7.27": "2016-07-14T16:02:07.959Z", "1.7.28": "2016-08-02T16:10:39.973Z", "1.7.29": "2016-08-19T15:42:00.990Z", "1.7.30": "2016-08-25T18:59:14.520Z", "1.7.31": "2016-09-19T18:12:09.989Z", "1.7.32": "2017-08-21T22:04:12.302Z", "1.13.0": "2017-10-25T18:51:05.568Z", "1.13.1": "2017-11-09T15:18:06.824Z", "1.13.2": "2018-04-30T16:21:22.860Z", "1.13.3": "2019-08-19T01:15:22.538Z", "1.13.4": "2020-05-19T18:16:44.855Z", "1.13.5": "2020-07-28T07:07:19.466Z", "1.13.6": "2020-10-19T20:09:54.557Z", "1.13.7": "2020-11-11T01:03:14.930Z", "1.13.8": "2020-11-18T00:20:43.724Z", "1.13.9": "2021-01-18T14:35:36.532Z", "1.13.10": "2021-07-06T16:59:46.982Z", "1.15.0": "2022-07-29T22:02:27.685Z", "1.15.1": "2023-02-23T19:02:41.434Z", "1.15.2": "2023-06-23T08:02:40.011Z", "1.15.3": "2023-09-25T14:23:41.946Z", "1.15.4": "2023-10-18T14:42:27.309Z", "1.15.5": "2023-11-07T16:21:01.495Z", "1.15.6": "2024-04-02T17:08:17.965Z", "1.15.7": "2024-04-03T06:07:53.535Z", "1.15.8": "2025-06-07T02:18:58.268Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/serve-static", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/serve-static"}, "description": "TypeScript definitions for serve-static", "contributors": [{"url": "https://github.com/urossmolnik", "name": "<PERSON><PERSON>", "githubUsername": "urossmolnik"}, {"url": "https://github.com/LinusU", "name": "<PERSON><PERSON>", "githubUsername": "LinusU"}, {"url": "https://github.com/devanshj", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}