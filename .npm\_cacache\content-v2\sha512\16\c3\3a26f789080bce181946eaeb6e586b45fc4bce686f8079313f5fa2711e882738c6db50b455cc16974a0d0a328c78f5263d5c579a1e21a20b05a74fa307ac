{"_id": "lodash", "_rev": "2791-68ee02e06113eef115d46c6094d1dc49", "name": "lodash", "dist-tags": {"latest": "4.17.21"}, "versions": {"0.1.0": {"name": "lodash", "version": "0.1.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/bestiejs/lodash", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "dist": {"shasum": "392617f69a947e40cec7848d85fcc3dd29d74bc5", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.1.0.tgz", "integrity": "sha512-ufIfwX7g5obXKaJhzbnAJBNf5Idxqty+AHaNadWFxtNKjGmF/ZO8ptSEjQRQRymBPZtLa0NV9sbrsH87Ae2R1A==", "signatures": [{"sig": "MEQCIBB7pdqPfBFUsZQhVr3woDJ7/bbRWV3tlXQZNp3ivosbAiBMhwfq9fqaJvFFX1/scqPbIywUUZCQkfJaISqaJbZX2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://mths.be/mit", "type": "MIT"}], "repository": {"url": "git://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "A drop-in replacement for Underscore.js that delivers up to 8x performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}, "_nodeVersion": "v0.6.11", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.0": {"name": "lodash", "version": "0.2.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "dist": {"shasum": "efaa16fb4614bdce9a7a9fdda487125d3fab38a2", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.2.0.tgz", "integrity": "sha512-WUvXFMYqweevRJzqnj5G8pJ2wOjNHTnP4+x+qBdoGstLRo3oWob98Oydu7fMD3bZJ2oXrzx5AJBZoc/jlBfLeA==", "signatures": [{"sig": "MEYCIQDqI9ix1b2CvBRIEAmBw9599iQB+RG4/Ns71Hix0xA9aQIhANODxwn1tYviwWSPBsWgtYH46txfftzT603ZjIzeljMY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "git://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.22", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.1": {"name": "lodash", "version": "0.2.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "dist": {"shasum": "fcdd734c401af6215d1ac511dc30e8003dde4607", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.2.1.tgz", "integrity": "sha512-rF+K2OphXlCLxiBFZHXnqXfP3xfbaF5VxS+sFZRLvqxHzHWPI7ZjN4HW2EkvoMu9cC3mVQ2HZPrC/4qYh3x3/A==", "signatures": [{"sig": "MEYCIQDwqRt3zWu86t20PxfrTJBEa3OiX00S/Dg1z+kjvpBGxgIhALb+MZEIxvw9e2d2CfTKMpTYlUQlxeWvsQ0pASscWy+T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "git://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.22", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.2.2": {"name": "lodash", "version": "0.2.2", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "dist": {"shasum": "d79277dafa86346a181dd924365215b126fdf2cb", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.2.2.tgz", "integrity": "sha512-OPzZZb4xiAJMdQiiX0+lNENfJi2Q81d47msW92eAO53WsGvfSLPNuH3Y5rj16X95LyDJJFZI5oTyZH8hqw+YAA==", "signatures": [{"sig": "MEYCIQDTxiOdJ8JtYNG0T9CK9nVkXuR1nZRrDh0KMLAsjI02eAIhAIcfL4aBSB3DNMeiaxh6L07xYrV/APe28idWpjUx2hGc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "git://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.22", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.3.0": {"name": "lodash", "version": "0.3.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "dist": {"shasum": "e7db413e8b50d48834e86f82c52fd64f0a9e5d6e", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.3.0.tgz", "integrity": "sha512-J/aUWOfASTdkrHQTrPFv15Z3jKM3YfRi9iw5iv3hro5A2yiPlDQONmhe/MFz5OR1l6gMgPz6iaTZP46NC6sacw==", "signatures": [{"sig": "MEQCIG0RYm+Cmnh7wriDFJiG5ubPysJh+fd1fcwNZl70bcvrAiAcZpGSbMoiuguQPu9ITbHYhzAHNrTsS3xXnel4oIB5XA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "git://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.22", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.3.1": {"name": "lodash", "version": "0.3.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "dist": {"shasum": "4449723baa6d2ff0ab710fd5e4ea70e06ad5e319", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.3.1.tgz", "integrity": "sha512-x6Ulr3iXKfQrRw0hgPDQK/KhIbxZFN5QDoxCYpGdm5hVAfXKncylMDLUCQIOlM49JNgJD3mFhGQNx/QjdUNqfw==", "signatures": [{"sig": "MEUCIHxASEWCv60aSrzbxhtxauIwQQ9CAVMcgdk4yaN1c4KCAiEA/ghAF3SQNHtkTcFDZN3tluHU8+2NHJvdi+xCxZuKD0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "git://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.22", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.3.2": {"name": "lodash", "version": "0.3.2", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "dist": {"shasum": "b28474cacc670134c856b24ad4183ff20dec7b8e", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.3.2.tgz", "integrity": "sha512-v7NeemI3apXsXmkaOvSw5bqHBCH2uvVH1WK1YY0Jgs4QYvRLp0fd+Gs1quMxb8rjI42NhhsaHh2I8fLMuRXH/A==", "signatures": [{"sig": "MEQCIBu/p3zhWMr0gx2Qr8G6qJMFRjRPrPnZiPlfe7Te0FbXAiA+eVd+96XZjfkpyQe9tCkHSFH5Hf/EvBKt7pfgHI+HxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "git://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.22", "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}, "_nodeVersion": "v0.6.18", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.0": {"name": "lodash", "version": "0.4.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "dist": {"shasum": "9305c807119214f9d9d7728d1f3c86c3881431f5", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.4.0.tgz", "integrity": "sha512-oQcT6m8qkgaNSP3s6+ysJD5CV7J7XPTD37AUor2osl+okrfZMj1mi7uBPkeSEWsBuxXHEm4GCftH94e+44lLCg==", "signatures": [{"sig": "MEUCIQCiNr3NpcLddGfkXG5wFWvjSUzKHiZaPGVES4qhxhDemQIgIPC9LaJGOY/s0b9zN9VbZRPrL/VOnLpmqi6ei50VOy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.4.1": {"name": "lodash", "version": "0.4.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "dist": {"shasum": "56dda6e88d947361d53c075da64084be4b2b9907", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.4.1.tgz", "integrity": "sha512-V6kjvvv1Axc36Q73Z7Bq6fvGFD6fm7RVm/yhS1AWpxQHyqq/plg7IEVYU/uoswNHlkG6vNoDJek5lFKXA6aS6w==", "signatures": [{"sig": "MEQCIFNwGI3kpsrzD1Zz9w3vzxNX6UeTWtgqbba9Fjtl0Lj3AiBFYHCjn9CAzRVg+nh/BMSyx91RwnXsiNvMcOk3h9Zhhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.4.2": {"name": "lodash", "version": "0.4.2", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "dist": {"shasum": "e152362a0e7c5ef8c35894e58d99a520c822aed6", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.4.2.tgz", "integrity": "sha512-T3HW30V48FSIMUbHQ1241toUKtuedXjdlMGwikYC3sypCr/hJ20INyEqu1NmLy1Ki+c1BWifVeA0VrnpCeGacQ==", "signatures": [{"sig": "MEYCIQCytyWE0ZWurVMPO2KF1LHO/T7EY1MDKgsUp8LO96ubGAIhAKd/5wBltMOSsv23AcnxyZXxVYAgQsoHGmEcnsJQqEQt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.5.0-rc.1": {"name": "lodash", "version": "0.5.0-rc.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.5.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "dist": {"shasum": "b3084c61e5515d86cf680aca01a5840c51e15e6a", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.5.0-rc.1.tgz", "integrity": "sha512-BLGWug79CJFou7y/WPR6cE8jPMkIgah/af9fTvs7MK5qiIUCcmeFH6WL1bkFCl8G8yMUuIID7aaujehdyp6wmg==", "signatures": [{"sig": "MEYCIQCCnBFCYV41jzZVYZ/CYUfnhH+9lh/V+XCs+IxhNR6V+gIhAKnOH2UhbvxKwu6go0kcfEvansWkU75/KGHxpznf/7Hq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "description": "A drop-in replacement for Underscore.js that delivers performance improvements, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.5.0": {"name": "lodash", "version": "0.5.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "lodash.min.js"}, "dist": {"shasum": "37f7912579b92139e9b657f2e2b80ab5c8ca4643", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.5.0.tgz", "integrity": "sha512-DE5+P5UOOeTIhLolU1V6qylAv+WnqNXieXjdw0HdKFhhn4XGifI18UiZWd1da1ZtWrXqhnhDpLQKQNBj/Gukcg==", "signatures": [{"sig": "MEUCIHzisxOCnXFwcfsiTAobTiLX2HUqvAsRxpSZU5maFwsfAiEAnEIHsuZ4q2riB5vaL74HpfwLjTMbZx9q0Kikf1AvUIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.5.1": {"name": "lodash", "version": "0.5.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "lodash.min.js"}, "dist": {"shasum": "70a60e7ae06c7a0838cf44b96dab9d8cc1454838", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.5.1.tgz", "integrity": "sha512-F0Br1/oIhUjch7aa7WHrkz3i3Y1bJ4Io3vQ8r5L0KkW+DGDzabORXMjTlwmfMtUCQQC8pACRNm4LPcnE3SzNww==", "signatures": [{"sig": "MEUCIB4GRKz/3oBftnCAbxv2ixV7mVMfPUd3OuGk7LufIY9dAiEA8kxsNVuFnNNAgRAWp13ipXTApDKkWjjtKBo4X9LGQ84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.49", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.5.2": {"name": "lodash", "version": "0.5.2", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "lodash.min.js"}, "dist": {"shasum": "4c873f93bdddeaa07401a675f8e17ded5af9a827", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.5.2.tgz", "integrity": "sha512-fCK8wz3zQ8rmj9elvA8MsdA5IgmuaASVlL6kuYBF+2/GDYMNodIrzl7QKeHFiYxUhgMpDXtSohBpKEHw2l6ERg==", "signatures": [{"sig": "MEQCIDDXVdK2jIKtxgfLXf6cKNrfa4UxxLCTTlVSakbkA8zdAiBuoGdzbt236A0gtB036+1LQujVpFeOL1kYNUjdDaSFYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.49", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.6.0": {"name": "lodash", "version": "0.6.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "lodash.min.js"}, "dist": {"shasum": "78712a5c31fb040f4396e3c4d42884d4c75a3ecb", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.6.0.tgz", "integrity": "sha512-ifOsEB21qdwE/X4j0Icw5Ot6jdFrJVJQKqsgSI2nTTrsr1157ixUgTD0j7rxDTl8mraya/DqxqUKzxVSbpLFdQ==", "signatures": [{"sig": "MEQCIBScxE5svCEwrF5D0P3M7ZEW/Sy99RdABldHNJYHSWD2AiAB1zhuqXh4+CH2jQlYrzWPdkJPxvg9/K042vl+L2Kbfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.6.1": {"name": "lodash", "version": "0.6.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "lodash.min.js"}, "dist": {"shasum": "9b093d490fd4fc50091609a92f4bbb5d10396357", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.6.1.tgz", "integrity": "sha512-OPb6TH8ycWKgocyPsP3KEP6q/2bqqGa0/Tm+BKpKE1M83hmFpI/2bmN/TYSAZLtXm4D8qFWQKjeOkeEn3+pw8g==", "signatures": [{"sig": "MEUCIQDfHmKAtLFIxy8MheA28i6sQGsYq+oMxcdVrdjl4JZTfwIgWlSYL9jP3r4bqOnQkuStJ/+lYxJw8K1eBdDIZEfLhkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.7.0": {"name": "lodash", "version": "0.7.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "./lodash.min.js"}, "dist": {"shasum": "8d0649e1fcb58a546ae9cb9f86f61d442571ae83", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.7.0.tgz", "integrity": "sha512-agVE2mFWg/GfSdeIFIs8l5Mq8sh4rNBvDfTOqOPRkqqrT25z9bcTZemaSr+V9SS0IVHKNq28Gl3WYJrRQPFFYw==", "signatures": [{"sig": "MEUCIARKv1cKFxayhFTMI9huapWa/tfF9aHeGf9/Apcmjf7VAiEA5jiddYWIYy65LvWW3I4DUbl/b8YpKcntZ0PnOl48HS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test", "build": "node build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.59", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.8.0": {"name": "lodash", "version": "0.8.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "9c66ca5f6a886087b1f980eef2a3991a98a22f7a", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.8.0.tgz", "integrity": "sha512-KvKW++QipwbsGkAJ1z0hGM4NgfCKyltT71GyF7tLyymD3BKTcrxSuQA/gBx5kwKji10SziRP8j3Oul3jcCroXQ==", "signatures": [{"sig": "MEUCIQDwUfsRVXcYAP8iiYbEWB4GWAJ6ecKU1yFGN+l0bdpVyQIgHf6LDDxBQhNYHksQSUYbSd1L0QqLk8NG4nm1PqeyT0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test && node test/test-build", "build": "node build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.8.1": {"name": "lodash", "version": "0.8.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "e4948649217958a8413600fa3a63941cfb8f4bb6", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.8.1.tgz", "integrity": "sha512-bDYGM5Mgyx+00YUAJjcIoi4QOtTSP64KwtRpsoDSUkptiQfpjuIAldD5oR9W1tZmU8BNiPonD4CxtfQ3FVy4lw==", "signatures": [{"sig": "MEUCIBejANzfeHDwMVybGWBDiaMDyTpkf3l0bOKu67Y0ZVAdAiEAj4ZS0mJQP3wsUjZkOkfDmgrjt0L+CRDL9KpVvgYbsSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test && node test/test-build", "build": "node build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.8.2": {"name": "lodash", "version": "0.8.2", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "lodash@0.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/bestiejs/lodash/issues"}, "bin": {"lodash": "./build.js"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "4c59d6b23ce894412a13dab22304772d0cb11ae4", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.8.2.tgz", "integrity": "sha512-M+DpqmTu6ynuy13IGw1062/jOE8lNuAWad5X912Y4/l2EmXXlzKZu7KH7LPAETMllWQypknIlQPcIwILVLS5+g==", "signatures": [{"sig": "MEQCIBmuE6MT4/qgLBRUYrvDSV1FFU+szQ0nCeGUSsq8kX5yAiBHMOM1AdBKBiMim7zNAcvLXxqAdm869HqSgo0BZTjZzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash", "engines": ["node", "rhino"], "scripts": {"test": "node test/test && node test/test-build", "build": "node build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "http://lodash.com/license", "type": "MIT"}], "repository": {"url": "https://github.com/bestiejs/lodash.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "A drop-in replacement for Underscore.js delivering performance, bug fixes, and additional features.", "directories": {"doc": "./doc", "test": "./test"}}, "0.9.0": {"name": "lodash", "version": "0.9.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@0.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "5647e17bc474a1903f4873616d5a2bc615d6620a", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.9.0.tgz", "integrity": "sha512-9GKocE3Wxer2MpDZvyC502zdGct3HplIfhCAxstyFqXuDhnD8bzsDKrulssxCZ0XtkZOois3f8Xu9RLTJ6tzNw==", "signatures": [{"sig": "MEYCIQCwKivrLIEofa6VjbprFfQ/C7bKguiIzHJAlaEJTxzcWgIhAP2uOs5bCpvIKjBcrjmEIrnQtCurRkrolmFwrlNNvv23", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "0.9.1": {"name": "lodash", "version": "0.9.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@0.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "9009df00e6e3166935312b3a3edfd9dff06e3562", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.9.1.tgz", "integrity": "sha512-TwMDfvu57jxxYuFmj39eW5PKYNtC1+vQ8MDawNVC/OwyAiC910EszLl7tIq4L7kcsqJa48Wz85YIWv52DXfCyQ==", "signatures": [{"sig": "MEUCIQDHzCG2amKxitLW6ElkYAgBgbIjtw8h7lNUwoN+l4wwJAIgVphhbvEj1VmcqScbIKjmI/286crY1ZswO990aFAV3mY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "0.9.2": {"name": "lodash", "version": "0.9.2", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@0.9.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "8f3499c5245d346d682e5b0d3b40767e09f1a92c", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.9.2.tgz", "integrity": "sha512-LVbt/rjK62gSbhehDVKL0vlaime4Y1IBixL+bKeNfoY4L2zab/jGrxU6Ka05tMA/zBxkTk5t3ivtphdyYupczw==", "signatures": [{"sig": "MEQCIErIqxXrE921GKcSyIT9tZR5R2N0wi0GhNjwT21KkzDxAiAGiBped/hhARRL+p2vLglrM3MTgEwEkEm+fTVBr1rCFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "0.10.0": {"name": "lodash", "version": "0.10.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@0.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "5254bbc2c46c827f535a27d631fd4f2bff374ce7", "tarball": "https://registry.npmjs.org/lodash/-/lodash-0.10.0.tgz", "integrity": "sha512-v56iCcHwqxrkkFW08F6GFAEYjL/E7Exbk2HZKhaZJsogEMSqse9as4Zmx5fmci4ZCQ/UNVoFHGudYzONfGxANQ==", "signatures": [{"sig": "MEQCIDhEoqF/QW4H5iussTXNUoKRDh6b0wKJbN8zBn/TE8EzAiBUD6obxpVt/sGFxfVBAlgd8T9GeY353YG7Wl9vGrp1+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.0.0-rc.1": {"name": "lodash", "version": "1.0.0-rc.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "4d25e7a3471e8c79f21a53bc9548ff4c3c751246", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.0.0-rc.1.tgz", "integrity": "sha512-RU2HVROwIiC4i0GxJXd+p8ZEOVIA2G30fA0asPT4OQhagGB/IqebmZHTebFoWmp/H6bwFLD3aF7pHh6NdqGvTA==", "signatures": [{"sig": "MEYCIQDHesT7kzqB11TWID6sBjasLFp79QFKFzuFg6358V/WSQIhAKoHYU0bWX12RGjcaJiknB2/aCz2+STaknGfXRi3yiOy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.0.0-rc.2": {"name": "lodash", "version": "1.0.0-rc.2", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.0.0-rc.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "58c819a059ca25d52c289f7a01b419a4f3b945e9", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.0.0-rc.2.tgz", "integrity": "sha512-cOgWHORUWnM0YhviZKqaoTNZOp8t6VCBBfuFhPXIvff2jVOD+bCCJuWpu3+u3rfdyG5ur828hnxvWjnsyZh+Dw==", "signatures": [{"sig": "MEYCIQDtACXMj9F+6D8gVARmB6kGVp+drF8mjNjWPGuGE8GOlwIhAJbc453EwJ46RIyOI2KmMZkepWEaoRQh5ENZFBJz9JXF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.0.0-rc.3": {"name": "lodash", "version": "1.0.0-rc.3", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.0.0-rc.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./lodash.js"}, "dist": {"shasum": "807ec29d0b768c7bb1025a796b8dec137fb06957", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.0.0-rc.3.tgz", "integrity": "sha512-NJD0CmjjmQfqJgny4TwIqwJjuhD7QgTbMvhjG/tbEP5xzxUVyhBe7VR5J2AxX2ZDjIHBy1xFBcjcPMflaJiApA==", "signatures": [{"sig": "MEQCIGWqq4JN7sztJS9AiKsdZSVy/DajJF1KWLMA4aBci28FAiBk60Bwnnhtvs0NZXbmm1Iu5vrYPiVsNYu7RgsnF9OpJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.0.0": {"name": "lodash", "version": "1.0.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "2535c2f7dcb6937cee8d2672c5f7138eee0df6a9", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.0.0.tgz", "integrity": "sha512-WHDYO0qSDoEe4PJznK52IoMBj2szpaTUnG6iJarwjeW0LymFN46KNDoiOLOt4OS78e1e+A9jQrfbMNxue6l3GQ==", "signatures": [{"sig": "MEQCIFV/EJ9wWTdurH8MSzL0ncFov+gnTrRaMDJddmzmcNmlAiBOQ3Ls0r4vOT4QNrXNsc7JqtQZyBtfQtt7OC7wCMARsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.0.1": {"name": "lodash", "version": "1.0.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "57945732498d92310e5bd4b1ff4f273a79e6c9fc", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.0.1.tgz", "integrity": "sha512-FgX9IMPSwJM589qAZJzLArkmvkqkZIAvgYVuFwXO/UFls4/CzTJEBECNAYkGXnfU14m8nptUlOl7bQpeWNmEbg==", "signatures": [{"sig": "MEQCID1pbjxjWT27ElDo5O2ogBDib+zyQvu5tFDuX8Slz6qkAiAQCMwr+AuzLB/QGFN9FKoBFbOH7f2tasly75jN1dhoCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.2.24", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.1.0": {"name": "lodash", "version": "1.1.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "bd0bb7f858f6c09ae140f23869261e555bd6ec1d", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.1.0.tgz", "integrity": "sha512-KXV9Vye0OKVjfJYpkXYIx3Q8xa2c9rYPufqhnva6ck4PNK5WR65n9JF9a3ikWBS/M4rZjXVfWRqiqUcVYVIBiw==", "signatures": [{"sig": "MEQCIFNjUZy2p6z1WzrtgKRAjNJMjJuL4bOOeJNIzGOP1/YKAiBwr7UrHm2x78Y0/LUUETVne9qX+kgvinm4FPyXMPTPOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.1.1": {"name": "lodash", "version": "1.1.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "41a2b2e9a00e64d6d1999f143ff6b0755f6bbb24", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.1.1.tgz", "integrity": "sha512-SFeNKyKPh4kvYv0yd95fwLKw4JXM45PJLsPRdA8v7/q0lBzFeK6XS8xJTl6mlhb8PbAzioMkHli1W/1g0y4XQQ==", "signatures": [{"sig": "MEYCIQD+NB60W8Eh4nLn9ImVjHjVSAwloulxkuR5ljGi1e8qDAIhAKAyqWs90p9Ke7ZWit63lGg+I0CV8l+rV3I8pHnYWy3e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.2.0": {"name": "lodash", "version": "1.2.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "5f16a3318ab3bf680c7b41b5bb6327e9e1086ec4", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.2.0.tgz", "integrity": "sha512-ExQAVirH2oQGyLAJv8jODSh25/hP2Fz24nn/7Yu6XeYcJKs5l/AY/9N1CZ85nykxoRkJBB6CyWrv+AxO3eSXyg==", "signatures": [{"sig": "MEUCICaB5HmzSYcLUg8dLEOQqzf2XAZYVEQ9QKPUaYr+F96pAiEAwqAFHgbaJMyiQc+95lL8TLK9/9EvqsB/fyHm8c+x9hA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.2.1": {"name": "lodash", "version": "1.2.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "ed47b16e46f06b2b40309b68e9163c17e93ea304", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.2.1.tgz", "integrity": "sha512-EKI82Edm8glH3FUu17sQIF+Ly1cW2ROPc0qgf1L4DBUysBlQVL+/b+WtufJw0O8FtMo7Vq9KTrzUboyWSgW/tg==", "signatures": [{"sig": "MEUCIQDSjTW29K3t8UFQyE9zIxPXhH//YqU+l1jfAVs2XUbyfgIgXNJjkIczAOtDE/bQ7nK4iJJ53CuyNmnqPkv6/naq6JQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.3.0": {"name": "lodash", "version": "1.3.0", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "09f6f55bceac513ee5681665947630fa8ae7593f", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.3.0.tgz", "integrity": "sha512-hvb4OS6A2bHKEjqLNb2B724q4Q+B5z0yEX2/RstbFJXTZyx27aculQfs8v27wUehdWBSydJ+kCVSzJaXJY6dlQ==", "signatures": [{"sig": "MEUCIHwVFvdtMVv90JJsKyusv6LTB7AsWRsXeLBJDQ7cu9urAiEAwR7lMOOrxD+3lZnynxQNE4R3kQ62cXFEztont/ii0ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.d.ts", "*.map", "*.md", "*.txt", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "1.3.1": {"name": "lodash", "version": "1.3.1", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "a4663b53686b895ff074e2ba504dfb76a8e2b770", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.3.1.tgz", "integrity": "sha512-F7AB8u+6d00CCgnbjWzq9fFLpzOMCgq6mPjOW4+8+dYbrnc0obRrC+IHctzfZ1KKTQxX0xo/punrlpOWcf4gpw==", "signatures": [{"sig": "MEUCIQDu7OBjj+3FqRl5/9zXbXe4s35eGL756qFwtJuursRtRAIgEVAENfyPJltEsYSNAcwDKl0zdjRRFKym3NiGkMQmBt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.d.ts", "*.map", "*.md", "*.txt", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "2.0.0": {"name": "lodash", "version": "2.0.0", "keywords": ["amd", "browser", "client", "customize", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "dist/lodash.compat.js"}, "dist": {"shasum": "9dcf9e3fac04ad0a38c9e2db69c9fb7ce26dabc2", "tarball": "https://registry.npmjs.org/lodash/-/lodash-2.0.0.tgz", "integrity": "sha512-6Ebs0FtZaSrJX8Kr+yFYBlWpv5yoRWiaVQCeiSQiJxouECdp3JXvWiWymwsv3uK3qTmV3OB72nFc8w8Fn76imA==", "signatures": [{"sig": "MEUCIAf07yHSldOvpMnlSPbgqO2dYNNOpjUfzu2Igo67+jsmAiEA8Dx/xwRmAEJ7EoBCTXyCfseLK9rB5JeWJiVXzhNpdjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}}, "2.1.0": {"name": "lodash", "version": "2.1.0", "keywords": ["amd", "browser", "client", "customize", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "dist": {"shasum": "0637eaaa36a8a1cfc865c3adfb942189bfb0998d", "tarball": "https://registry.npmjs.org/lodash/-/lodash-2.1.0.tgz", "integrity": "sha512-5BzuEUmAfrZX28Zv3p6KKyIl3SnswGZblKJA3O20ZUDfBkqlXv87oMvmTsiQnM9g0dVXVOtKTuIIg7cnAGZICA==", "signatures": [{"sig": "MEYCIQCVPQGpIF92vnN+QDNn3K+x643C+5mwbfa3mQftFmbf2AIhAKJbBejQ+4nFpk4K6cdQ2wCzCW0D5JSwfvYYXyZ87NgX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, & extras.", "directories": {}}, "2.2.0": {"name": "lodash", "version": "2.2.0", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "dist": {"shasum": "6bd68cc1c37a3885c15211bbe1dce6d69f01e01d", "tarball": "https://registry.npmjs.org/lodash/-/lodash-2.2.0.tgz", "integrity": "sha512-2arP4V6OWGU82q5hCYAfjuN0mizPZV2c65j2aEgwjabPLYIuSfaJFoytcjR8I8bbDVYh1HMfm5OoGdv0BqzT6w==", "signatures": [{"sig": "MEUCIQDO4jzTd48BBtsqpruikR8DGx4j/GpDKh8G7NUDN/fs5QIgZknscfF0ACPULdXqyeL4Y7Z5u7oaYXdFyIF+DJoWt/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "A utility library delivering consistency, customization, performance, & extras.", "directories": {}}, "2.2.1": {"name": "lodash", "version": "2.2.1", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@2.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "dist": {"shasum": "ca935fd14ab3c0c872abacf198b9cda501440867", "tarball": "https://registry.npmjs.org/lodash/-/lodash-2.2.1.tgz", "integrity": "sha512-rGaKzxe4Biu8YdCPD/tUkBF4/fnAqgj63A6PeAyQnH/NEKNUHgppGZUgYBYOmAZsBKwwAb343Q1Zew0RDB2jIg==", "signatures": [{"sig": "MEUCIDewUY9jOPnx1AehLo/Qefc9iMH/sAIYPaXUAR+lepisAiEA9AB6+A1vkwwGYw1uMnoDvi/MrVg/MhxzmmpqiFTCZKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "A utility library delivering consistency, customization, performance, & extras.", "directories": {}}, "2.3.0": {"name": "lodash", "version": "2.3.0", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@2.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "dist": {"shasum": "dfbdac99cf87a59a022c474730570d8716c267dd", "tarball": "https://registry.npmjs.org/lodash/-/lodash-2.3.0.tgz", "integrity": "sha512-1cKXXnLDXhXBP8H1ZECyE1znMFLUjMiV9WrVx6/Mi8Ocm/wwjuhWNpLFefQdv05+uMtIAdOVPnG3SFxcOUZzBw==", "signatures": [{"sig": "MEUCIQCtMSfmC548FcEB97d08924MF6zm2MVK3lI+hVgM2sFRwIgWem5Aae/siMGmOVvnPwTzpoZ3sCtskMLgv+ZOSuFtGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "A utility library delivering consistency, customization, performance, & extras.", "directories": {}}, "2.4.0": {"name": "lodash", "version": "2.4.0", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@2.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "dist": {"shasum": "55074982883381b6b7134b742a5900bbbdab6b09", "tarball": "https://registry.npmjs.org/lodash/-/lodash-2.4.0.tgz", "integrity": "sha512-nIjaweI+LHygGWjtPBi4E03eBE36hme8t0mgkl2tDJVOMBIH8P+m3FB/vdX1Tw5bmRqyRQzE0HBQ6uvlysC1xw==", "signatures": [{"sig": "MEYCIQCaLW/Bj66Vs6zlcpbt+j2tT7zMQXjghkBQoKOtv9AwzgIhAKewSpNo1aT4/C1+r+zR2F7NHhCOGkxBC/YgSUNRO3EB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "A utility library delivering consistency, customization, performance, & extras.", "directories": {}}, "2.4.1": {"name": "lodash", "version": "2.4.1", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@2.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "dist": {"shasum": "5b7723034dda4d262e5a46fb2c58d7cc22f71420", "tarball": "https://registry.npmjs.org/lodash/-/lodash-2.4.1.tgz", "integrity": "sha512-qa6QqjA9jJB4AYw+NpD2GI4dzHL6Mv0hL+By6iIul4Ce0C1refrjZJmcGvWdnLUwl4LIPtvzje3UQfGH+nCEsQ==", "signatures": [{"sig": "MEYCIQC0wNQrE8Afpp7YnJM9KIHsQzspnni97l/xxjRr8WJtQwIhAP47PcnLvvnEdLqCpBlk7jHf5YNlqFIlwUTvxSQ1FfWu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "engines": ["node", "rhino"], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "A utility library delivering consistency, customization, performance, & extras.", "directories": {}}, "3.0.0": {"name": "lodash", "version": "3.0.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "493364b7183a37104d65255f38b0fe5869cbe0fd", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.0.0.tgz", "integrity": "sha512-GFMDPshMSMkDX/6y0h60FED+G00qMjVGpi+obx0feBDpeWUC9jXDAA+OmnXa4sWDvgpY7ogaaHLDJz8jXyRLqg==", "signatures": [{"sig": "MEUCIEF1fZIQKmroUUkq/ZTyP1I4yINLf5B5M1gJpf/3/IPhAiEAiyMz6VSIDEqTyAo59SaEmykRi0I3wPNJWgOjxoODWn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "493364b7183a37104d65255f38b0fe5869cbe0fd", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "1.4.28", "description": "The modern build of lodash modular utilities.", "directories": {}}, "3.0.1": {"name": "lodash", "version": "3.0.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "14d49028a38bc740241d11e2ecd57ec06d73c19a", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.0.1.tgz", "integrity": "sha512-Hw4nFcX+LaJVkNNhjLgAlK82Q1/8DgCdaXQm2ZDQsZlqC6DiZcGhNSfguEesBBrITt8ZOgoMBUzUyWi1TaqLkg==", "signatures": [{"sig": "MEYCIQDZevJbY6d7y/hz72VBEQaMDHSfUV9jTDqv28oxUFtluwIhAInZljemszFgao8j44tEcbcXlX3piPUcJ+7LjeygcmcF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "14d49028a38bc740241d11e2ecd57ec06d73c19a", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.3.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.10.35"}, "3.1.0": {"name": "lodash", "version": "3.1.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "d41b8b33530cb3be088853208ad30092d2c27961", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.1.0.tgz", "integrity": "sha512-kH7H/6T8ut2DMWAHIKJWZoDzYdvy+echL6u2VZbhYYkEjr6Zp15Yjt9pPvIPQwAT/5QaQYdaVPwCcLMP9qeu0w==", "signatures": [{"sig": "MEUCIQDL848azpgFY14bVmNTXvaUoGsCoNOQvDES19i62mXJeQIgHEOU6my9LnJvalVFb9vqD11bg+KEQ4gK4VLLy1WbBdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "d41b8b33530cb3be088853208ad30092d2c27961", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.3.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.10.35"}, "3.2.0": {"name": "lodash", "version": "3.2.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "4bf50a3243f9aeb0bac41a55d3d5990675a462fb", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.2.0.tgz", "integrity": "sha512-pg876Sz2B6iut18K5PlFGZUxagOP9LACajQ4nzQHPZ8JcU15tviPV6q/u/dEQMV64TzKu8yJM3NprFxqRb3oHA==", "signatures": [{"sig": "MEYCIQDn+GWJfeAYuRioVUtR8Aw+OgSYnhAeKOBoSaxGaDB/JQIhAPx+tVbbLqrgF5DDsd4TNb7D0zU0yySmdAA98bgxALAO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "4bf50a3243f9aeb0bac41a55d3d5990675a462fb", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.5.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.0"}, "3.3.0": {"name": "lodash", "version": "3.3.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "011ce52d01b14e60eaeea76acb9b8ba42c70581b", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.3.0.tgz", "integrity": "sha512-gpux6tVfBHsUdUIciz5HoV0ChAxUTvi0ChpQMIjAsKtg6FTYFtd1B1G0JlqHvAio3teaMVGPDPk2seVq1INwOQ==", "signatures": [{"sig": "MEUCIC1JNl+rJDo9XNQ+hHRGKbLAvZ38IpZVybmx+OdBJFRyAiEAoZIZlgdP9oumqOQM6LhD/EWGdIvdroC1v+DWHtHSsyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "011ce52d01b14e60eaeea76acb9b8ba42c70581b", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.5.1", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.0"}, "3.3.1": {"name": "lodash", "version": "3.3.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "3b914d4a1bb27efcee076e0dfa58152018e2042e", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.3.1.tgz", "integrity": "sha512-LwPGU4nXJqOmsJvDDwFl2vuaecFFqX1I8PMJJWbAAGIfhNK5+CFtd6fgldg9VFuOmy6WLi09sbBguMBAxaJbOA==", "signatures": [{"sig": "MEUCIF/Hr4FzZDlltvTHomJigz/WltiAOWf/usEIDCkRjLI8AiEAwSndTTcSe/lLxVSYVWN3oQtrdDyE0izICaAbNJXXPKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "3b914d4a1bb27efcee076e0dfa58152018e2042e", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.6.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.0"}, "3.4.0": {"name": "lodash", "version": "3.4.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "7c39c1336faf5f96e6409f8355f26ea192f41821", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.4.0.tgz", "integrity": "sha512-nOn1D2yGLp11KdIHF/Ww1CYuB1LVaK4+n3/eQbFN5eyfqVHPiCFgJUrWoRoPdoDw4C+XJzycLHK8fLTCyM7MqA==", "signatures": [{"sig": "MEUCIBC8z8hG0gxd7S3KewADCfuj+/ceR39zqZLcA2l1gJZ8AiEArMnkXDgcQTEC06+w/2lgK4O7m1ghI5vqHXJD3yr1HZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "7c39c1336faf5f96e6409f8355f26ea192f41821", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.6.1", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.0"}, "3.5.0": {"name": "lodash", "version": "3.5.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "19bb3f4d51278f0b8c818ed145c74ecf9fe40e6d", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.5.0.tgz", "integrity": "sha512-eC2gi6pEoXXAWKz1fKvWnA0wOdBPsvTVFT71szjM6dFoDE9llinK81xW4EH4QxF+wNZoVeLIxUp21BDPniMn4g==", "signatures": [{"sig": "MEUCIQDnVG3YjiBN9DDsq+UbPrRivf85cXyjdPVDsWqW2sS7gQIgKyi0SwnKZ3aenzKjY3Y34jQnGa+UTI03QMvcmUWeEA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "19bb3f4d51278f0b8c818ed145c74ecf9fe40e6d", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.7.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.0"}, "3.6.0": {"name": "lodash", "version": "3.6.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "5266a8f49dd989be4f9f681b6f2a0c55285d0d9a", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.6.0.tgz", "integrity": "sha512-fysFKsJtaOtRGZT/b3Xx03iyEmO0zjU+d1HBH5NcEaUjtg7XO0wDY5I7IJFfr2rguJt0Rve2V32426Za3zYyRw==", "signatures": [{"sig": "MEUCIQC6sgZGcY+taxwfcuI1gB2t88QQMksDdXm2/yoA5e6niQIgTBNdxWgWt0pwrk5qHKLcgoRrQzLxkHXzl9t32Yk0Uls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "5266a8f49dd989be4f9f681b6f2a0c55285d0d9a", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.7.3", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.0"}, "1.0.2": {"name": "lodash", "version": "1.0.2", "keywords": ["browser", "client", "functional", "performance", "server", "speed", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "./dist/lodash.compat.js"}, "dist": {"shasum": "8f57560c83b59fc270bd3d561b690043430e2551", "tarball": "https://registry.npmjs.org/lodash/-/lodash-1.0.2.tgz", "integrity": "sha512-0VSEDVec/Me2eATuoiQd8IjyBMMX0fahob8YJ96V1go2RjvCk1m1GxmtfXn8RNSaLaTtop7fsuhhu9oLk3hUgA==", "signatures": [{"sig": "MEUCIQCACyYgVLYs/EuO4VvRj2ymK4QwA2xkhxCUPcwsjYOMtwIgFZtn3EOMBOlHVI6XHVF3+3gQ9eHRsw5W6Vw3/W4Lkoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/lodash.js", "_from": ".", "_shasum": "8f57560c83b59fc270bd3d561b690043430e2551", "engines": ["node", "rhino"], "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.7.3", "description": "A utility library delivering consistency, customization, performance, and extras.", "directories": {}, "_nodeVersion": "0.12.0"}, "3.7.0": {"name": "lodash", "version": "3.7.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "3678bd8ab995057c07ade836ed2ef087da811d45", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.7.0.tgz", "integrity": "sha512-73GDDlioRJOCHV8N9gnBEpjdWI34+e9AvMnS4qdqdMfl8/yH/dJP1tfuqUFccZ/deZQlHuJiRSuKXjKIfDwBOg==", "signatures": [{"sig": "MEQCIFwJRskSs1/74Ib9D8kEmybWgCczWrILAu1Oo8zlihchAiBW8TwGFUAX3FwIXxgNAb8vOnPf5JedWCRLd7tR4CMqUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "3678bd8ab995057c07ade836ed2ef087da811d45", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash", "type": "git"}, "_npmVersion": "2.7.6", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.2"}, "2.4.2": {"name": "lodash", "version": "2.4.2", "keywords": ["amd", "browser", "client", "customize", "functional", "server", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@2.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "jam": {"main": "dist/lodash.compat.js", "include": ["LICENSE.txt", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"]}, "dist": {"shasum": "fadd834b9683073da179b3eae6d9c0d15053f73e", "tarball": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz", "integrity": "sha512-Kak1hi6/hYHGVPmdyiZijoQyz5x2iGVzs6w9GYB/HiXEtylY7tIoYEROMjvM1d9nXJqPOrG2MNPMn01bJ+S0Rw==", "signatures": [{"sig": "MEYCIQDaAf5aD9/+mIS+6J7zxOTzl7xQzVcOIVI0gDarhgeBpAIhALFvoBlTaWvfval/HNXf5vUUmbRUfPCWWlZ5DrpCmyB8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/lodash.js", "volo": {"type": "directory", "ignore": [".*", "*.custom.*", "*.min.*", "*.template.*", "*.map", "*.md", "lodash.js", "index.js", "bower.json", "component.json", "doc", "modularize", "node_modules", "perf", "test", "vendor"]}, "_from": ".", "files": ["LICENSE.txt", "lodash.js", "dist/lodash.js", "dist/lodash.min.js", "dist/lodash.compat.js", "dist/lodash.compat.min.js", "dist/lodash.underscore.js", "dist/lodash.underscore.min.js"], "_shasum": "fadd834b9683073da179b3eae6d9c0d15053f73e", "engines": ["node", "rhino"], "scripts": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "A utility library delivering consistency, customization, performance, & extras.", "directories": {}, "_nodeVersion": "0.12.2"}, "3.8.0": {"name": "lodash", "version": "3.8.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "376eb98bdcd9382a9365c33c4cb8250de1325b91", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.8.0.tgz", "integrity": "sha512-u93G/0wg7ukZx5nAxzzxRvVsCnBATHNDbYLego2eokz4fvL/+nzJOIJS48ru7NlXnjthNw56mFpLlbp9SjUEgg==", "signatures": [{"sig": "MEYCIQD0nHAC9Bjy55/iRYyPo89I3qCHR3l2NPN+r53P5mJr4QIhAPsNIXq1bZdLzdZ4mdd6mY7ywBwEoB/w+wzMEOM32zqI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "376eb98bdcd9382a9365c33c4cb8250de1325b91", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.2"}, "3.9.0": {"name": "lodash", "version": "3.9.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "5bf547f9fcce37f78678859ba11854dc75ffa21b", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.9.0.tgz", "integrity": "sha512-ad4kdm6L5dPTQAx9hcmz6fX8c3oKMlRWdMAquKfLx4WN4jDQcXCdDQ6qLEszjpWUfYzfpw+aASTLsQJEArBDIg==", "signatures": [{"sig": "MEUCIQCqcIhqM1B4egn1SqXRmKzhp41yxbd2Ajsf9SHZhez3zgIgFDmF6TAhtTZ+7jFhzsAA6QYjutBn8+tIWqRwBr7yG4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "5bf547f9fcce37f78678859ba11854dc75ffa21b", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.10.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.3"}, "3.9.1": {"name": "lodash", "version": "3.9.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.9.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "5102372ecaa2ccf71fec63b55b44642eeb77b59a", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.9.1.tgz", "integrity": "sha512-UQIAfzv9CTa+meu2L6aYg14hocAeeRLoNn4YfEWr842Qrp9wgWkpiW8V+/xwDHsMgSCWU/DNYI2FeYzhBwlCMg==", "signatures": [{"sig": "MEUCIQDj8dMxcq8QerjxSX+CfFef32k/lvjFhXi+lZtaTOn9OAIgTXSjhNwP0zVtA1CM3RnO7J/MWeu92Y/tv8KDiN/sk5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "5102372ecaa2ccf71fec63b55b44642eeb77b59a", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "2.0.2"}, "3.9.2": {"name": "lodash", "version": "3.9.2", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.9.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "e86b404e374a7787fd272be086991b7bed2ce06f", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.9.2.tgz", "integrity": "sha512-dVjBoiZBcGClNGpTaW8CiL6Xn06hHp9qwT455o/QZhkUPjOSPWyW0F1fPJUU0u6prVRrqP80iq9bAatv7c+zXQ==", "signatures": [{"sig": "MEUCIQDh+flnTqKw7tum6UCRuYQfPFbkWJSsVMkMY4gO3SkfLgIgWUAZBs4lF5qNyY5TBj1c/WdFgOsyAsqyuqRmSx8fxkM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "e86b404e374a7787fd272be086991b7bed2ce06f", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "2.0.2"}, "3.9.3": {"name": "lodash", "version": "3.9.3", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.9.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "0159e86832feffc6d61d852b12a953b99496bd32", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.9.3.tgz", "integrity": "sha512-v5SKZhnCUujcTpFpHEIJZDVcBM2OYjROx732HyJ6kzKZtwStTb4LG6noqmK9etHqDNhf6X7itXx5s0hTpAXPpQ==", "signatures": [{"sig": "MEYCIQDaUoBVFAGBV8tAzpbJ67qUP1naxYB/jDeEo1SuEK2yaQIhAPaLQO8srBCW7xwOiAkkd14U9ilMqD6DqBH9kpdScak/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "0159e86832feffc6d61d852b12a953b99496bd32", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "2.0.2"}, "3.10.0": {"name": "lodash", "version": "3.10.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "93d51c672828a4416a12af57220ba8a8737e2fbb", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.10.0.tgz", "integrity": "sha512-y4sq/rWWfUsEaOR6VYulMCC6QzL7mqb5wV8R09xGUJZ84UoBkn4/nNqZ04YSqXkEQOQabBRKxk3xgzgig2h75A==", "signatures": [{"sig": "MEUCIQDpR1NyV6+AJO/5nh67YmA1nOfwjg/E5qGeGR8E3Enw0QIgOB9yJyCzOduewLuctUfI5TfOIEo2w2mSwWdjFtCgE3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "93d51c672828a4416a12af57220ba8a8737e2fbb", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.5"}, "3.10.1": {"name": "lodash", "version": "3.10.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@3.10.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://d10.github.io/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://www.iceddev.com/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "http://kitcambridge.be/", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "5bf45e8e49ba4189e17d482789dfd15bd140b7b6", "tarball": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz", "integrity": "sha512-9mDDwqVIma6OZX79ZlDACZl8sBm0TEnkf99zV3iMA4GzkIT/9hiqP5mY0HoT1iNLCrKc/R1HByV+yJfRWVJryQ==", "signatures": [{"sig": "MEQCIBxAQzodSNS2eFAV7mOwTrdTd3gonBDAtt+RCWtWm7VMAiA//VanNTbFCQfgRvcnhb+AOtzYjqGWHio9zEP7Sn0PhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "5bf45e8e49ba4189e17d482789dfd15bd140b7b6", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.13.1", "description": "The modern build of lodash modular utilities.", "directories": {}, "_nodeVersion": "0.12.5"}, "4.0.0": {"name": "lodash", "version": "4.0.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "9ac43844c595e28d30108b7ba583703395922dfc", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.0.0.tgz", "integrity": "sha512-bWpSlBobTcHYK9eUzcBYHhSBGzvSzEsxocnW5+v7p6wCRlY1icneTe2ACam3mGdAu82+RLL32cmyl7TRlJHqZw==", "signatures": [{"sig": "MEYCIQDmLVxGDydgXRr6XOm4n+ttpLWEyOMT9LEHVyzI9hkRZQIhAM5gnqk2BxUYewBS/6tpGpe4YwdrtvKlSUb7yIBaDRal", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "index.js", "_from": ".", "_shasum": "9ac43844c595e28d30108b7ba583703395922dfc", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.4.0"}, "4.0.1": {"name": "lodash", "version": "4.0.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "cd8c902c9e03f2e69ce3e0e456d505ab89eb98f4", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.0.1.tgz", "integrity": "sha512-jA3EFncTLSpNOJLgZUlI7Xrsg3t/DYbgPuRVD1qbLoyUXgzUEvB0EpJimh+WBM6CUNtAyKJ1CVPD+7wE+LrraQ==", "signatures": [{"sig": "MEQCIFCoyoaUXfT7mfszfUU/ZlyjHft5BXpQ+x7jRlWePYjHAiBPk72ecwXfvzVLApibIaGc8U4NYcaEwk8VKEzRBxg+og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "cd8c902c9e03f2e69ce3e0e456d505ab89eb98f4", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.4.0"}, "4.1.0": {"name": "lodash", "version": "4.1.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "299894283de01a9eefbedff4c4b9b00a6a2e6e96", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.1.0.tgz", "integrity": "sha512-B9sgtKUlz0xe7lkYb80BcOpwwJJw5iOiz4HkBDzF0+i5nJLiwfBnL08m7bBkCOPBfi+0aqvrJDMdZDfAvs8vYg==", "signatures": [{"sig": "MEQCIFF5nQ7MjsJ7TKgq5ux3fONhTXBjjI1y+t5ApPWMswf/AiBqkOpdCRhZSNy3m/CXPEiuClXygxIH0u7Cw5bvQKK9MQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "299894283de01a9eefbedff4c4b9b00a6a2e6e96", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.4.0"}, "4.2.0": {"name": "lodash", "version": "4.2.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "0506f4dad737c79cb2602ac11f8b74b7136464e8", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.2.0.tgz", "integrity": "sha512-snufRc058TNWSy4Y0drZ8p6/ntrD0UnJHe+NNwLQaJW7dk1Ig9rnKwFmdWwP9ObKJzO2bvc0wWcbeL9pnKyhBg==", "signatures": [{"sig": "MEQCIBoh1EYQjFjjCFdkyJ+Kro8b7sOMpmB7IP/gqmJDaW+MAiAJBtuZ3vdI1YtQnbMKM6eSyU2+FK/5PnXpxqUPEhAN8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "0506f4dad737c79cb2602ac11f8b74b7136464e8", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.4.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.2.0.tgz_1454403016517_0.5934681817889214", "host": "packages-6-west.internal.npmjs.com"}}, "4.2.1": {"name": "lodash", "version": "4.2.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "171fdcfbbc30d689c544cd18c0529f56de6c1aa9", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.2.1.tgz", "integrity": "sha512-G<PERSON>ttIEgpAXIRPojhmAjK8qs5sDIYi7Usv7vp0l2O6ccLU9fxZB0TtvkemdjHyljjMFQt02w4lhKLyvB5YH2C7g==", "signatures": [{"sig": "MEQCIA6K9Vl5DwBuKjKhCbcQNjv+a24NO+kWLnp28JEDRA60AiASYiJH37gigMaC1p+kyDjK1mVYgzCjjqLvOUCHNoE2hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "171fdcfbbc30d689c544cd18c0529f56de6c1aa9", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.4.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.2.1.tgz_1454515215050_0.8410219918005168", "host": "packages-9-west.internal.npmjs.com"}}, "4.3.0": {"name": "lodash", "version": "4.3.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "efd9c4a6ec53f3b05412429915c3e4824e4d25a4", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.3.0.tgz", "integrity": "sha512-JDXk2rxAUY3cHGG9OJfRbhd7zc2feQRdMthkMdqmK19l0+ojybg9ISylGUnmqHtteg/wXH8QudOLN+RKgKNKIQ==", "signatures": [{"sig": "MEUCIQDo+pQRjuSrkIymrTFJ5ZLMFhJ5fMSGy6rAT5mtIqszgAIgNe/MlCfb9h0M+Zk7raXkSbhnkqSc38XclD1bL0Ej5WA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "efd9c4a6ec53f3b05412429915c3e4824e4d25a4", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.18", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.3.0.tgz_1454921838929_0.49659619107842445", "host": "packages-9-west.internal.npmjs.com"}}, "4.4.0": {"name": "lodash", "version": "4.4.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "bc5c6b741d04d53111b26cf1a2feab86c268cd7a", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.4.0.tgz", "integrity": "sha512-yE9fdlwq2UjPVJphtZBgIJaF7a6Y9gK5B5FCHPcZiOQ694DQ28pAN5jQTcR21g/P1O5RjpX8+nryVpRgnHrQHw==", "signatures": [{"sig": "MEYCIQDJkt9wpBY8TVQiMtoXpdHzo0Tbb1wKjqfzaK7MBa0b/QIhAI9PYhw8VohUEx1GcOyh+lBPzMjIq3jZo47Xf+ZTLClf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "bc5c6b741d04d53111b26cf1a2feab86c268cd7a", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.18", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.4.0.tgz_1455606613194_0.7996933336835355", "host": "packages-5-east.internal.npmjs.com"}}, "4.5.0": {"name": "lodash", "version": "4.5.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "2284aa06f5b136adbd954b903511b62fc39d1f59", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.5.0.tgz", "integrity": "sha512-QX5CQiKEsYsnEvcTGDRDcq75WePSnzrptmplm8BIARgWrDmdjg051fpi3FGhDdwGhlv6Q4HZKwNYrUb3cQ8Gtg==", "signatures": [{"sig": "MEUCIQDieqM6tZFXRpyANVuESVEESRV4Ofubvq2ejfPNRVSCFAIgQa2HOf2NUN8vCQdUuvKHLTetgGKwkDWq9e4ZqQbfsa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "2284aa06f5b136adbd954b903511b62fc39d1f59", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.18", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.5.0.tgz_1455698378277_0.6987434786278754", "host": "packages-5-east.internal.npmjs.com"}}, "4.5.1": {"name": "lodash", "version": "4.5.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "80e8a074ca5f3893a6b1c10b2a636492d710c316", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.5.1.tgz", "integrity": "sha512-N6UtfG+knq6dx13HVBHXsSagpSAQsTM8GHAv//TNcEMPc1ayGEb8wcAUvNWx0x3Xpd7PTP6hhe6AoNeIiq/IYA==", "signatures": [{"sig": "MEYCIQDM4TKjUK82qHHZO1yFQTDWzsxtfFXoDlpyluVh7rF/gAIhAO69yq8/h8zkc9XX1zx7RY8RTK89E2YHoVFtPRtndl/2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "80e8a074ca5f3893a6b1c10b2a636492d710c316", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.17", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.5.1.tgz_1456123341085_0.7924484650138766", "host": "packages-9-west.internal.npmjs.com"}}, "4.6.0": {"name": "lodash", "version": "4.6.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "c6926922ed75379993f0212a4764c18a760a2198", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.6.0.tgz", "integrity": "sha512-Lh9Djp+57UEqdXgLUyQmqaAKZyTzcMLVsNAfWYeZ/EKyfS8/43b84BIAto6MPG32lc0ziKHZopbPiGlxl2Iudw==", "signatures": [{"sig": "MEUCIGYIayTcV7BOWzTw5qBqIBeDLh9rXBGN0STpGNffSZ6AAiEAtdni+5WvKNWxqKCSBAr2JSGD/WnYA/7rYdZeNrv7qR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "c6926922ed75379993f0212a4764c18a760a2198", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.17", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.6.0.tgz_1456889076647_0.5880196213256568", "host": "packages-12-west.internal.npmjs.com"}}, "4.6.1": {"name": "lodash", "version": "4.6.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "df00c1164ad236b183cfc3887a5e8d38cc63cbbc", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.6.1.tgz", "integrity": "sha512-85DFrB4TYPwVZbZw/HCGnmRUdDQOxGJuDmSfZ9OQEESdFJ1NxlYLoQObfHJGr+UsWwmVQhwG2iPmUCi6+eKzUg==", "signatures": [{"sig": "MEYCIQDHW1x9DophfHHC2iPswM97cq0yQVVI9rgFzfeHWZ9igQIhANJjukIEA/UR1VVmx/zgwXwgarYyy7IoXJt80eJm8UtS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "df00c1164ad236b183cfc3887a5e8d38cc63cbbc", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.17", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.6.1.tgz_1456942179866_0.5565699376165867", "host": "packages-12-west.internal.npmjs.com"}}, "4.7.0": {"name": "lodash", "version": "4.7.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "b8088876bdc290ca1f34f90350f7b5cb71810c05", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.7.0.tgz", "integrity": "sha512-d4bJYO5ctbajdv2vz64ECUWJG8rgo73FeF6j64bGT47I0krA0DMQSoQW1EOPGibkJB86MoNvZVUwJBHxiwmFDQ==", "signatures": [{"sig": "MEYCIQDFrSBIsiqdSBSkAEnxw1YKJiHYIbWe4UW/05iOvlVjZwIhAOIvl9e+SQHcWiJfDH5U9kAJvb63hg3mxWKGFfOF63ZT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "b8088876bdc290ca1f34f90350f7b5cb71810c05", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.7.0.tgz_1459439192794_0.5509570257272571", "host": "packages-12-west.internal.npmjs.com"}}, "4.8.0": {"name": "lodash", "version": "4.8.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "297fb94a164c02e7eb8d37cde135be62456df446", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.8.0.tgz", "integrity": "sha512-le8DgAtRoZUyhKrjm6O9zEXbBHR3XPtT2siu8cNTTQ8GdiBPlTsYOzkQuPf9xBQRSlkxRwNNOXVK9r8N0hGJCg==", "signatures": [{"sig": "MEUCIF/6bVxsDgq0o3x64xm/F+6mkp12cf8jSZSJWB5/MMx4AiEA6Lyu2JYuj8IlmQUxIE5jsVi7GxsUP4gxjq8tAi7BZ00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "297fb94a164c02e7eb8d37cde135be62456df446", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.2", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.9.1", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.8.0.tgz_1459781672956_0.8200249187648296", "host": "packages-12-west.internal.npmjs.com"}}, "4.8.1": {"name": "lodash", "version": "4.8.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "97797b4684d238bad1a2d0a2e53108fe57e5f95f", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.8.1.tgz", "integrity": "sha512-izVINTxb821BWw4WBV06xd6qfr7oWRFUyZHUsKNeyVRp+KURAvLLMUTRwzqwAsLaJ5duFMtnEeDpSgiQz71F8g==", "signatures": [{"sig": "MEQCICO+Sl2YnH211FwOqBtq9CU9f/QTLN1T9EhHMDotjYUgAiAZuRFUjZWwyjdqh+ccjkQBkCVEasYZpLKDRDXWhjGn6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "97797b4684d238bad1a2d0a2e53108fe57e5f95f", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.2", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.9.1", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.8.1.tgz_1459784628521_0.6884815152734518", "host": "packages-12-west.internal.npmjs.com"}}, "4.8.2": {"name": "lodash", "version": "4.8.2", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.8.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "478ad7ff648c3c71a2f6108e032c5c0cc40747df", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.8.2.tgz", "integrity": "sha512-/uqqdn0gpGl3QldtTG79iyo2ao2zOsFce+GBxbE4rWmU6EtEWl4pPRp7Xw61DHge1vlfDfjq3zkytObW6RV0zQ==", "signatures": [{"sig": "MEQCIA3dYxsyTzYpVWZhaQ0SxT2nsaI1cP8MWHlJiIckE+lTAiBpJzHA2hlvOKoiCcoO1bmxXqumEPTuVCynVsyfb1eYUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "478ad7ff648c3c71a2f6108e032c5c0cc40747df", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.2", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.9.1", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.8.2.tgz_1459822516091_0.773343451321125", "host": "packages-12-west.internal.npmjs.com"}}, "4.9.0": {"name": "lodash", "version": "4.9.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "4c20d742f03ce85dc700e0dd7ab9bcab85e6fc14", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.9.0.tgz", "integrity": "sha512-UGUdLhDm+6tXViVGFOiDt+3HXNxVpLPaEcqzoTowq9XtmsHTq1nskdpr6UR1XV3aM1eJaiFtwg0DarxFW/ypsA==", "signatures": [{"sig": "MEUCIF8KU4GKDA6rXDKTGmLi0g8wHxwb1IPFw9nyZBiuDzg5AiEA/lMx7eHTzSgk1YtFPM00zSHdSdV9xrV/xDUBkD9Bmgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "4c20d742f03ce85dc700e0dd7ab9bcab85e6fc14", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.3", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.9.0.tgz_1460128953630_0.9709006256889552", "host": "packages-12-west.internal.npmjs.com"}}, "4.10.0": {"name": "lodash", "version": "4.10.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "3d8f3ac7a5a904fdb01e2cfe1401879bf9652c6a", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.10.0.tgz", "integrity": "sha512-uDkT/I1Y2X0gvxCdeosY9/XImqohEKRrvj/NLXA7H1NKWdu61lHnrIHiQPNlvIXCzsbnBqwdheW0AbKMxMQF+Q==", "signatures": [{"sig": "MEUCIQDAtQVMEGhMwEDG5nQraeMmSpCX9GDcEac3MjAnGR8lzwIgUXyO4R5MYxg/47RR7otf7OwvA2gR+z38X11ZoGzU1ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "3d8f3ac7a5a904fdb01e2cfe1401879bf9652c6a", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.3", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.10.0.tgz_1460385833529_0.7400152564514428", "host": "packages-16-east.internal.npmjs.com"}}, "4.11.0": {"name": "lodash", "version": "4.11.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "428f7172a5e9a82e9a459b543816489810ecb8af", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.11.0.tgz", "integrity": "sha512-18uE0HVAF7WGaXB37G3yJdx1M39bez8eCHPP+NCfOPzDbcP1XqqrVz6EuF2Ip2Ye/Hcmhvy1bGUGUDOGNzsHPw==", "signatures": [{"sig": "MEUCIQDd7J4jG6u0XRlqwlkQZmjPpeQQeX2qo6f0e5IjfC1tAAIgXnz6V5kJb5tRB7dBqT2JcYGBU6ek09SXtYt7FjomX4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "428f7172a5e9a82e9a459b543816489810ecb8af", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.3", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.11.0.tgz_1460561550034_0.7776633501052856", "host": "packages-12-west.internal.npmjs.com"}}, "4.11.1": {"name": "lodash", "version": "4.11.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.11.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "a32106eb8e2ec8e82c241611414773c9df15f8bc", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.11.1.tgz", "integrity": "sha512-zQXZjpw5m367A/MrkEIx1xEJISQmZykAJ5EzCPznYZiVLK211R1hDiye+TU4eyWKDfsaBsLW6/7gH5juuZ4J6Q==", "signatures": [{"sig": "MEUCICW18b6F28NbCzL3k+tW0x7ArUiAoZv5Z+5IqBFI8vLWAiEAgOi5xriJxC8O9WCfYNOBkyaGqLsp4Swjc1Y5DVNdJno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "a32106eb8e2ec8e82c241611414773c9df15f8bc", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.3", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.11.1.tgz_1460618480099_0.40750555554404855", "host": "packages-16-east.internal.npmjs.com"}}, "4.11.2": {"name": "lodash", "version": "4.11.2", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.11.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "d6b4338b110a58e21dae5cebcfdbbfd2bc4cdb3b", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.11.2.tgz", "integrity": "sha512-kzYAjjUS0vKRLVcNZgK2k7NJOT5cQoFO3w8ddED6mDBdgu3AIL9xhAktXJ5Dm6GD1x+eqqyu1wKAzEt8Uq77NQ==", "signatures": [{"sig": "MEYCIQDmrElBwUMcXMXod4OmARBGxIT1lB6u1bc2kZ0ke/VWqwIhALDQaxzuP491MTDo3n2Om9w0+rOX36FbQSHgEy6FBNSB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "d6b4338b110a58e21dae5cebcfdbbfd2bc4cdb3b", "gitHead": "ddde027fd9bd5d25416b6a788e273babc959f307", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.4", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "6.0.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.11.2.tgz_1462201261706_0.9612307662609965", "host": "packages-12-west.internal.npmjs.com"}}, "4.12.0": {"name": "lodash", "version": "4.12.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "2bd6dc46a040f59e686c972ed21d93dc59053258", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.12.0.tgz", "integrity": "sha512-H7Y9y0h/WibZBZyt9IOEEDaNJCzmpEoUQNB6d/+sHuS6fKFWCRuYSAf5s2mfK3hYrPqHbosJI4/bv0HUf2OvVw==", "signatures": [{"sig": "MEQCIHJkxJg7jZ1MQMUm2ZOMDz3tDaWJDBwHbYI9UrpBhHKNAiBLBnxQ8Y1Ch7M7DBo/uAX1dRLZv6VwmWbwZ4uGqO8keA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "2bd6dc46a040f59e686c972ed21d93dc59053258", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.5", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "6.0.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.12.0.tgz_1462735540938_0.36508488352410495", "host": "packages-16-east.internal.npmjs.com"}}, "4.13.0": {"name": "lodash", "version": "4.13.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "6172ad9cfaa5db21268b23b6da6c3a42442aab65", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.13.0.tgz", "integrity": "sha512-Ab4JJMgGNOpKsBaR0UtUXIjym7eclpVKVaK6QGmxT9f8ODHmn+fhp12KhRKgIeposBlrzfFsN/ilBe/Et1BRog==", "signatures": [{"sig": "MEQCICoVa83Yur7TpjvsKCNiQC/4da3Z3XCp4Mr3AqJRO3GeAiBNpnvSkOQDvs4oDJYe+qzbvSNSNG5JR/8PP2E6OKBKXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "6172ad9cfaa5db21268b23b6da6c3a42442aab65", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.5", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "5.5.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.13.0.tgz_1463980042961_0.5916273870971054", "host": "packages-12-west.internal.npmjs.com"}}, "4.13.1": {"name": "lodash", "version": "4.13.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.13.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "83e4b10913f48496d4d16fec4a560af2ee744b68", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz", "integrity": "sha512-j/GRONYpkXt1aB1bQHzkq0Th7zhv/syoDVrzCDA3FDMntIin0b7TjXi62q9juDC+QfhRs9COr0LFW38vQSH9Tg==", "signatures": [{"sig": "MEUCIASq/p12GhNYrbkUoZVnAJJddTQRch29MyB022km6qIaAiEA+r0Lfqdoi7eY3vUqU8G7+pZ55tIAH8kB7cWI/jHNpfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "83e4b10913f48496d4d16fec4a560af2ee744b68", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.2.4", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.13.1.tgz_1464019142054_0.5244540225248784", "host": "packages-16-east.internal.npmjs.com"}}, "4.14.0": {"name": "lodash", "version": "4.14.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "b742d6a80b5ee87e7c1a35c143c5b19bd966c10b", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.14.0.tgz", "integrity": "sha512-/dOeXHnVxZOSQ9di7dLSTCZqZH4fpzep1ATkJ4D3k8/gmeMLzhEQiartyb2s61Aaw3kiORDu6jzKCj6Ftgo01Q==", "signatures": [{"sig": "MEUCICtm4tt0M2oT/DgFLd2NxaaZslddntlheJ5WpJh7xnzaAiEAhBY8NbRbh4PHk2LFSlqGJ8eZfN2zPrR40vvRK+WkcDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "b742d6a80b5ee87e7c1a35c143c5b19bd966c10b", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.2.4", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.14.0.tgz_1469385658233_0.2943053827621043", "host": "packages-12-west.internal.npmjs.com"}}, "4.14.1": {"name": "lodash", "version": "4.14.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.14.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "5b5d0a516ad3d46e12012d7f59ad66a5659bf408", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.14.1.tgz", "integrity": "sha512-G7KWBIFduJHp0Nnbhsanp15Yr5Cp9oW9EiXG+SmyZ7U4aG+leOCLooJK/TT9IULKDZXmcIQwAvMFXmKvHzlmzw==", "signatures": [{"sig": "MEQCIAwURzQoWg153BWl0pA/PNZp7p6FkXelbXxgwmh6taGAAiA7Xv4B4xi0jZMHoV/sIWGoc5n7zItcINniMhseGSsKeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "5b5d0a516ad3d46e12012d7f59ad66a5659bf408", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.2.4", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.14.1.tgz_1469803745646_0.5584656686987728", "host": "packages-16-east.internal.npmjs.com"}}, "4.14.2": {"name": "lodash", "version": "4.14.2", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.14.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "bbccce6373a400fbfd0a8c67ca42f6d1ef416432", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.14.2.tgz", "integrity": "sha512-L5PieqD7phyya3Uave78zpkVE5uc022V1h5iAWt7q1z71SS7Rtw5OX8Q30OZ4L8GVtRLKxI1mn76X288L7EdeA==", "signatures": [{"sig": "MEYCIQC8BN6pnfTodDf2TpBvBXxPQClDpiHNPUK3ORZ7erHA5wIhAKEJov2i0F4LYm6QAl6lAGu+6cLLChqABIBmuOorz6vG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "bbccce6373a400fbfd0a8c67ca42f6d1ef416432", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.14.2.tgz_1470670544899_0.8695246241986752", "host": "packages-16-east.internal.npmjs.com"}}, "4.15.0": {"name": "lodash", "version": "4.15.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "3162391d8f0140aa22cf8f6b3c34d6b7f63d3aa9", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.15.0.tgz", "integrity": "sha512-/XM2P+O3xDTOR/CrI4ZqqzgC3tFOLfn2sUIIKN9CYI3YmZnZ9QcElpYh0MHiHXMNJLWwbVxkCaO6zFGS97Xj0g==", "signatures": [{"sig": "MEUCICXA+4UYDvhdv+EDb84dPI8naaJ+Dvm7QJb8Ls3VyvtWAiEAixQwgniFAVX/5xJUrv4fyHWj/4Hs13rkW5eGFScqKPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "3162391d8f0140aa22cf8f6b3c34d6b7f63d3aa9", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.10", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.15.0.tgz_1471012765624_0.7570270872674882", "host": "packages-16-east.internal.npmjs.com"}}, "4.16.0": {"name": "lodash", "version": "4.16.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "834cb785057157032242beaf101bb3d99e55d0d8", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.16.0.tgz", "integrity": "sha512-Hcf3oefWvjGo2dixw7g+prUj/8A2YbRVe5IirVtTLQzW/dMojxXGvi7NXEa17q4vhz4KbRwLPi6NFLK6cYOh+w==", "signatures": [{"sig": "MEQCIF/+OlOFbK8W4FeESVDVJZlN4G81qF6WD72fad6+tc+WAiApc1Cm+Y0vJl+fBP+Oh12gpVyZJ6bEtpv63frGMpes4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "834cb785057157032242beaf101bb3d99e55d0d8", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.10", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.16.0.tgz_1474297151529_0.16563124535605311", "host": "packages-16-east.internal.npmjs.com"}}, "4.16.1": {"name": "lodash", "version": "4.16.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.16.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "75e15fcfe2721adbd6a7c5985f855288a03fc36d", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.16.1.tgz", "integrity": "sha512-qK+vzI6bQ91q3gkfBKpO0rgR0qV6ECjT1+ZBprnFejldHGi74AQ9MPWfSreqqzaF0aLRC7qnnYb5AjTwLXSNKw==", "signatures": [{"sig": "MEUCIQCpf9ZLMuFdhch3OqUy1eZWEhCNOFT+ou1AzpNQJTBeGQIgLe7ZGrbjumw7TqEtaSpsYoTNBfTwl8RnSt2Luvw5TiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "75e15fcfe2721adbd6a7c5985f855288a03fc36d", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.10", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.16.1.tgz_1474390790200_0.7545584393665195", "host": "packages-16-east.internal.npmjs.com"}}, "4.16.2": {"name": "lodash", "version": "4.16.2", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.16.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "3e626db827048a699281a8a125226326cfc0e652", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.16.2.tgz", "integrity": "sha512-t7POblJ+wdC7jto56BrMLpkyS0/ryvQxhmIgN3x+RBZIQCPszPpbxDy+ZcSTmYFDBrgDkwAc20P/C14y7xXWMQ==", "signatures": [{"sig": "MEUCIFMWyYv0hEsYM77ExXj1sOgLDDlmAcz2XyN69VOCG0ZiAiEA5W2xCbKvRUhaOyugFANCbzqV/0w0ZlTTdEuJzJvpWvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "3e626db827048a699281a8a125226326cfc0e652", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.10", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.16.2.tgz_1474859465050_0.057903866516426206", "host": "packages-12-west.internal.npmjs.com"}}, "4.16.3": {"name": "lodash", "version": "4.16.3", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.16.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "0ba761439529127c7a38c439114ca153efa999a2", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.16.3.tgz", "integrity": "sha512-XHD7mGAORGJeO5f32yHhWmS/9Fb13bjKedNt9EjV0tMl5dXplIWbFgLXHRjpZ41pVFdGMj8H/sdcuetZyAJ32g==", "signatures": [{"sig": "MEUCIGvfbYHttZwUYWG/PWTqOa4ju3y4oeqEEFYgAFiJbJ3SAiEAwujp99VLB7ZBw+NNRXc6LqkF7R3WBkGHqgtJ5A7OW7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "0ba761439529127c7a38c439114ca153efa999a2", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.10", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.16.3.tgz_1475513008878_0.7973488194402307", "host": "packages-16-east.internal.npmjs.com"}}, "4.16.4": {"name": "lodash", "version": "4.16.4", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.16.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "01ce306b9bad1319f2a5528674f88297aeb70127", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.16.4.tgz", "integrity": "sha512-nnaYhZqDvVKdmBJYqTAxIXxVUlRNwr8yfDKjKSpBEwoxi0Os+e5pU+/GtSO0mtTd06g49RCINYTmuixJR1Tz2w==", "signatures": [{"sig": "MEQCIFWOqANPl17D3rsBUhnWB7Y8MloiG4TOR7zVGxTm2R/ZAiBKxg7WlUYM8o/AttlcxdGMZe1/ZrBQr/TkX3J24+XOKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "01ce306b9bad1319f2a5528674f88297aeb70127", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.10", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "4.4.7", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.16.4.tgz_1475766806071_0.6463651182129979", "host": "packages-16-east.internal.npmjs.com"}}, "4.16.5": {"name": "lodash", "version": "4.16.5", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.16.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "77d88feac548009b1a5c4ca7b49ac431ce346ae8", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.16.5.tgz", "integrity": "sha512-DZbgdZTnZw9hGKYyvELBSE5P++SNV95Jk1B04G5mesp9f2xzwKyyy7u+lsvBWOOg9vxcbQ+OgimaB0ZLecOx3Q==", "signatures": [{"sig": "MEQCIDlu8FZ8JnJFFUzjhQfJL8+gmQhuQSuNRWoG9+zUpArOAiA67NY2MRBDClH2hWm+Z0WEm/K/qmAXhr3+ge+89r8SXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "77d88feac548009b1a5c4ca7b49ac431ce346ae8", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "6.0.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.16.5.tgz_1477896554541_0.9171323315240443", "host": "packages-12-west.internal.npmjs.com"}}, "4.16.6": {"name": "lodash", "version": "4.16.6", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.16.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/phated", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "d22c9ac660288f3843e16ba7d2b5d06cca27d777", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.16.6.tgz", "integrity": "sha512-QXrLkYI2gXjL0QoQ9j932ca+Oh/wCUBeZULjqsJy78KjntrohXawEoOfgA2fXwy4vKh7OTD00p757/pUROtv+w==", "signatures": [{"sig": "MEUCIQCwS7nwuspFwlaTzVl7V38yeta3dw39AGHEitn8/SFCIQIgYhnzhKedVoem5UEDZmNoHNRc3oFD9gQgBrz57krapBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "d22c9ac660288f3843e16ba7d2b5d06cca27d777", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "7.0.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.16.6.tgz_1477982285913_0.34612850472331047", "host": "packages-18-east.internal.npmjs.com"}}, "4.17.0": {"name": "lodash", "version": "4.17.0", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "93f4466e5ab73e5a1f1216c34eea11535f0a8df5", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.0.tgz", "integrity": "sha512-wXJoUnY8v5rCurNIL6tM0TOv/F6B2NzDzLzStNOQoX/i88s8hPGkYSRPMtcdjORIx1dHUB1lQVhsNyC9yjD/7w==", "signatures": [{"sig": "MEQCIAHoGqXEgU3qu5+USfkk2eDbe4EbW0P/6SrXJjLgQQLJAiBIh15ebz1AtUpIb+kpByhu/FBGBlUDGLSfvr+4iGvwmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "93f4466e5ab73e5a1f1216c34eea11535f0a8df5", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "7.1.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.17.0.tgz_1479106806125_0.49467423441819847", "host": "packages-18-east.internal.npmjs.com"}}, "4.17.1": {"name": "lodash", "version": "4.17.1", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "e75eaf17a34730c6491d9956f4d81f3a044f01bf", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.1.tgz", "integrity": "sha512-TU8moLfqGOx1RJogO9uU2d90q0L5J/1g/i4fkdB5ZmQ5qsjwVnCvg5XWuiwqqPNnyBQlQp8ukd9ADTY27dGsiQ==", "signatures": [{"sig": "MEUCIQDgHyDUTIAsZk9IgP7NJ9e8hApsUFIwmhXvq8XmmE6wmQIgCh3Hq4d4pcNHUtyoiVkbZG5/7r0n7ok+CV56o53TfIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "e75eaf17a34730c6491d9956f4d81f3a044f01bf", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "7.1.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.17.1.tgz_1479193405702_0.2180279449094087", "host": "packages-12-west.internal.npmjs.com"}}, "4.17.2": {"name": "lodash", "version": "4.17.2", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "34a3055babe04ce42467b607d700072c7ff6bf42", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.2.tgz", "integrity": "sha512-8mozooKYfrbhO6eMFCjiieXZalOKHpwENKTqMn/g0TrH1j4MLw8qwivmVwfcX5/+3LQJmXAWRXWhWUL+COdYNg==", "signatures": [{"sig": "MEQCICDiS+1DQBV6vXfxDJGrX1zUnANy1PfDFZevGNJLvdAAAiAAiyjIlcd0/JVDW18HGkgvOmUnmFbJWawjSYOsY0NFmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "34a3055babe04ce42467b607d700072c7ff6bf42", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "7.1.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.17.2.tgz_1479280899250_0.8391377755906433", "host": "packages-18-east.internal.npmjs.com"}}, "4.17.3": {"name": "lodash", "version": "4.17.3", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "557ed7d2a9438cac5fd5a43043ca60cb455e01f7", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.3.tgz", "integrity": "sha512-H+sg4+uBLOBrw9833P6gCURJjV+puWPbxM8S3H4ORlhVCmQpF5yCE50bc4Exaqm9U5Nhjw83Okq1azyb1U7mxw==", "signatures": [{"sig": "MEQCIGMRs0qnxT5XSnO0C8yCvCDrZovHtXvmckLZRkSHCb1RAiBXQrKizvJr+LuOgnA7S/gm/z4dDzEGQA+Us0MMOL9b8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "557ed7d2a9438cac5fd5a43043ca60cb455e01f7", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "7.2.1", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.17.3.tgz_1482589537076_0.02431614836677909", "host": "packages-12-west.internal.npmjs.com"}}, "4.17.4": {"name": "lodash", "version": "4.17.4", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "78203a4d1c328ae1d86dca6460e369b57f4055ae", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.4.tgz", "integrity": "sha512-6X37Sq9KCpLSXEh8uM12AKYlviHPNNk4RxiGBn4cmKGJinbXBneWIV7iE/nXkM928O7ytHcHb6+X6Svl0f4hXg==", "signatures": [{"sig": "MEUCIQCd5Wybp8d4FBD3p5WJ48vMIylw+fL9JH7z7uY/GZqWugIgCCmD5egZCtg9S9LY78zf/xEuygRZq/qhEskB59nvB9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "_from": ".", "_shasum": "78203a4d1c328ae1d86dca6460e369b57f4055ae", "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "7.2.1", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.17.4.tgz_1483223634314_0.5332164366263896", "host": "packages-12-west.internal.npmjs.com"}}, "4.17.5": {"name": "lodash", "version": "4.17.5", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.5", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "99a92d65c0272debe8c96b6057bc8fbfa3bed511", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.5.tgz", "integrity": "sha512-svL3uiZf1RwhH+cWrfZn3A4+U58wbP0tGVTLQPbjplZxZ8ROD9VLuNgsRniTlLe7OlSqR79RUehXgpBW/s0IQw==", "signatures": [{"sig": "MEQCIHr3DTM3iq+I4zN1izVIuuAO632jbU4sOTMiQzsFCI7pAiBOn6gSw1WHZx7ac3BsNGjLldEHI1vbYmP81heSnjctxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "9.3.0", "_npmOperationalInternal": {"tmp": "tmp/lodash-4.17.5.tgz_1517704479112_0.7115055937319994", "host": "s3://npm-registry-packages"}}, "4.17.9": {"name": "lodash", "version": "4.17.9", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "9c056579af0bdbb4322e23c836df13ef2b271cb7", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.9.tgz", "fileCount": 1049, "integrity": "sha512-vuRLquvot5sKUldMBumG0YqLvX6m/RGBBOmqb3CWR/MC/QvvD1cTH1fOqxz2FJAQeoExeUdX5Gu9vP2EP6ik+Q==", "signatures": [{"sig": "MEQCIBvKyF4ftU6PtXUMgP5sujJ2T3nOGoRawK6aei/H2eAVAiAuwPOEFW6Or0Hcj3YV18nqs+Z+wWE0iljIQR0HzIWdeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1399757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa320ICRA9TVsSAnZWagAAI3wP/2kAIG2fiZ1ZoBp0rBW9\n6RKVBF/amCaKuDwrFjS25IDUm+tZn9kD5sIVoqiSwvym1GeLGfq0wAaSahb+\nQ8nYhEOYOXrKeaOW6cH8pYyg/phoKSXj0+3CEpLqh4a14HA2RXj5XigJsx9U\nQbsZqT+vm0kJ2UNRwUUj9304zVXIvtaTRPUKE8gwb3bHFclRsmxS939UiIha\nrD0kSEoPsCwZBtn6Ha6hsN+XzKTpa7I4vLLN91h4NaBbmkN7quwBu8FkzXfK\n2WJZp5l46LJOq7qGiogIVX8G9KWV9ncu5+/K0gj8eGPRf4om27NB3Tj8SR+S\nNldDeg2QnMCqA+nsuObr+rHjiErgJ8bdzhxE+H/CxFPE83qnp4W8Re0Usnyj\nn8b+z7gzzqccvZ4DfUp5hZnNRoe4X/p+JbsWOIahheglI11EmYrm87VM1EYi\nyE/Y3BvjgWLB3uJTwYgj7t9AwRy2Hfg64S8SLqTiMUIcDn1n167oWqdr5sJm\nn+LoaykuWBSz9Biq7NTM3msOojXDErHBTNnPqOsqoXLhhgfTuDujJMIc8Aaw\n5jfXPzEdJHXlHN/imys/hjSd0rI2XIY+gFN6/V7BB1/sYLsEhw8S/8eS7GGc\nBgc3UJ7n+TViiODXgLg6pbwOPvGJT9OyJ2qFiXivs3yK2QPJQQEoYAjJNM7L\nB1Ef\r\n=5Cw+\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.0.0-next.2", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.9_1524591880132_0.5972035908632127", "host": "s3://npm-registry-packages"}}, "4.17.10": {"name": "lodash", "version": "4.17.10", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "1b7793cf7259ea38fb3661d4d38b3260af8ae4e7", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.10.tgz", "fileCount": 1049, "integrity": "sha512-UejweD1pDoXu+AD825lWwp4ZGtSwgnpZxb3JDViD7StjQz+Nb/6l093lx4OQ0foGWNRoc19mWy7BzL+UAK2iVg==", "signatures": [{"sig": "MEYCIQDmYAZc0JLH87ouZ5y5kBPVtVpLJ3KMB88legfbcAHcuQIhAJkHkVzYRD4VxSNDsOsbZQiT9i1pPui6est4r+1UZXGo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1399800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa33JqCRA9TVsSAnZWagAA0v4P/j+STtIL2K020wYEwPab\nWcDg2zwjWib+2mePvIwCylNI8YZs9vQXD9HvhWDD7/RH9bem/0sF45AgUk+w\na3bQGG1h5vFDHKftolS2fsa1HoT8ECEK2iVHQrS/DyCBcWYjfFUwErpd/NLx\nUpl9vW3Ba5clHUlq9rccOZ4Ld2gVQV8b2+p9V1IJeN9rkIOfRAg16FxAZjQZ\n9mj/XBR/Qw0XAEy31J63Rj+815CINYihHwpPdkaOTXDzouIn1/dP8jFp9oPY\nSGvwi4q6HBVbuuXmjcmMXIHRWz0drK3uex/3944yNzLlcu2Qn2rPqpQiD2rO\nYfIkwOd9TYfKfjvVGqEMzZHtLX408bm2kGtdyOLECuKAMSd5U9pOWfbVEeLN\nrydQQyGRC475Y15G58hSP0XbNM27dlEB+lfR6xtEcTLT75B/lxW2KxwxOff2\nLOTRfpd6Hx9d+5K80vAu1Ms8G69h2HPTDobJRmSPU+w/UTjeL9hrFy9x3hV8\n6xFgYOj8VZDiDpq0H1b0TH4GRGV0YvfVZ1V50Jzc3GZsKzSqk0i+UKDLuWVT\nYiTpQOPBlKysD3gGRjWMDd7jRMbf2b2B4Ajl5LhaB6mVjNnK8IMkTIegrwHP\nIOuE39Y1Vr1xBRlCBYcUdDrkPSCxlxzsToRPpo/1uWH1CI7HIBQsCJ2/h+n6\n6zBR\r\n=aCN9\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.0.0-next.2", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "9.11.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.10_1524593257507_0.03186110402277009", "host": "s3://npm-registry-packages"}}, "4.17.11": {"name": "lodash", "version": "4.17.11", "keywords": ["modules", "stdlib", "util"], "author": {"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"url": "http://allyoucanleet.com/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://mathiasbynens.be/", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "b39ea6229ef607ecd89e2c8df12536891cac9b8d", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.11.tgz", "fileCount": 1049, "integrity": "sha512-cQKh8igo5QUhZ7lg38DYWAxMvjSAKG0A8wGSVimP07SIUEK2UO+arSRKbRZWtelMtN5V0Hkwh5ryOto/SshYIg==", "signatures": [{"sig": "MEQCIHmNHdCN0DakUOXb7LxyfZOiLR1OqvwaJom6Kd6tGt9sAiBMb725nE4/3CYwfOPdxz7GLJwyAf/jhOlfO2bAEiwXcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1399577, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmVuwCRA9TVsSAnZWagAAqa0QAI92r3JGIJd0+pwZv30y\nOnUXbuHXryhRrlMLFy3jmfGPoEDa7moz3UF/iJvUHet19ctz4/RZKIK5jhmW\nW7d1RiMUh1PcQ9wfeiLmp1E4xvg92tG+l+k/4H5URDFqlmXbbP7hvI4cVXdX\nTH5knUONoKavtC73HfvMhxpJgEtgWrY5KYpjp4Jb+A9iQrJweIpnPx7GAYzW\n5vJ4eJahlnX5l+ikJRZoUxJIr0NbZQlBIr7jK/KbMcINmxEPKr3OO3M9h9md\ncHMGmnP/J9IpQpxkm9fzks5LEOs1Gm20nA01g2JlsWkiIJCWHLXLG9p1if/b\nt9QV0I2bTMfQwpM8twC8ub6+u9r2dj/gP2cmJCH97wyIRpeSxNYXkFjT34ll\nDvtYcde6YW8O28mmCndj+e71On1cqEo0laahuDeDU5vdKVCs4X+RJongDpz7\nXwTP4TCG4il4JBWvyXbDWe/717wcCOye01STklRgAi+l44G+Kbi9fqUhHInj\nruVW9I7IjRJuVTj32Ud6losqdy0O0qOr2lAoCqc1Zx7E2KnP7EHnMHBd8zL7\nQq+DKpsWgG/RFsZJdu09Yl2Oa992so3gHRy+afTkyciUH/CWYEi4dv3DRIIa\ncUZCz4OJ1N9UZpbIIFypFmGEUvd1liwx+SJwGf4zzJ3LkTdwjW5WPREx0waQ\nMdj6\r\n=JzE4\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "6.2.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.11_1536777135932_0.2638891224123521", "host": "s3://npm-registry-packages"}}, "4.17.12": {"name": "lodash", "version": "4.17.12", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "a712c74fdc31f7ecb20fe44f157d802d208097ef", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.12.tgz", "fileCount": 1050, "integrity": "sha512-+CiwtLnsJhX03p20mwXuvhoebatoh5B3tt+VvYlrPgZC1g36y+RRbkufX95Xa+X4I59aWEacDFYwnJZiyBh9gA==", "signatures": [{"sig": "MEUCIG/wZJZ0RElZ99nZS3jmDbXS0svVrhubgM0p6vnOWkj4AiEA0MdQLGu6u3pIh4Awe1IVZH3GY+aq/svmIE8f3LUQAmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1402075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJQIoCRA9TVsSAnZWagAAEh0QAJH1VEkgLAtn1tdzmoCP\n8chsAtmZ2Edu7MS9+GMfSkqz/j9AZT9n0AVHPc8r0Db44X/+OB/3v7jArGY6\nP0YQYYfU2g3fEn8yAv1ACK1YqFevNLwbSFllHTl88EbW8Z47U2iqURuL/DS3\nlZseyS49Tt4AYptAih20jRbZAjs7laUUgWeaR8XIljVgg3OJr6AtAf97UB3J\nFryn8ytaTgUxrBUUHc6eC6IH9ma80J67lR15EQJ7g5ZtPxfPpA9/Y8KRSQPK\nDOVr/F2Uo8ocllJRZym1CWVNBPXkmIPtrxlStADxa7Dm5SYUo2ZnF8ljYia7\nMIMsEYWkcrOKcD1Gqx4R3TQOWrHdAQJLbVc7EFZyGzLs8iNidSYNS3v6N8hP\nA9urKyjsUdBYwomeK+Oek406cehQmHKB/tDxSPOsHIT083PC00Al5kwS2NSW\nDasll6IiLxFTLOxP0Ry5pmi0cRUy63klIPLb9gk8muXv9/QKRgV7uzBANrD3\njzI0q49j4Bl2l1iHdRTMYv1IEmjH80CmUZu8no/XdMyscc5U9PF5Mzy4iaVb\ncRki/wHZUnstfhfl8tY4sKZYIlJKkZ/u7aAUv5AqA0Dmkx3k8oSbNdMvdBhz\n7sw8E47kf3blJNekt5SkWhUC9F4h0aPKpHr5mAudfiWMCVzShKk7E4caznaJ\nazfp\r\n=oQCS\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.9.2", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "12.2.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.12_1562706471399_0.7178658287459723", "host": "s3://npm-registry-packages"}}, "4.17.13": {"name": "lodash", "version": "4.17.13", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "0bdc3a6adc873d2f4e0c4bac285df91b64fc7b93", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.13.tgz", "fileCount": 1050, "integrity": "sha512-vm3/XWXfWtRua0FkUyEHBZy8kCPjErNBT9fJx8Zvs+U6zjqPbTUOpkaoum3O5uiA8sm+yNMHXfYkTUHFoMxFNA==", "signatures": [{"sig": "MEUCIEf1DR/DXeINza9JNBBB0jckdeQQiyXK093i7cF2V72lAiEAhpYvd0ONVHZ1mZJAFXeZ4tNMCRwV5QeJkrBkSTHZIRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1402106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJRQmCRA9TVsSAnZWagAAEs4P/0PLmRJNSdqRke3QMgyY\neXUmg0xuTuMrvJ5/sL+m92y0J0SDY4prWomQPtYlKlyKoiMnuQAxmHrzKPBL\nNK8ihO2raHjEQqYco/Sz0D3aTbU2UIqC/q8zR3Rr8FbvUIdUHH3INxvRV7H9\nvS/Rab874XzDz49iKHP5kozDoYEyyTBBtbCWDa/cQdpwIZcbugH2bsF1eD0l\n9SmD0eBHYjmBNSyumGM7Gz7jRNSbsfvO0mhFIcyKAQjG7VSNbk2nplhzDRT6\nK7pnHPiQgnHqqp8Q20aXwdh2un1JypHd09F1zTYin0HZ7N6XuJW8QfRgSB/C\nwn783x5+IACPSSd3GHBufNR7rwGYTWlJnDQwfXzuhjS6pOMW5476+nAboZTo\nBfFS28kTJ3wPCZgx47F+3OCXyMbzUAUhfvFqLn9FKBLqCt22CWZa18BBfU8/\nb43RO0EogUjn1J9nuJuLmTIUbLTRtFzbKR00I6GIdgmKa4b8bU/ElKHv2bxr\nC7q22/oJc5vh+N5WmlmKBgHYZFDXODw7lCsmQb2xSDK+ruQQ5VgQ1FQ/1bcV\nQp85/GugrlLngZ8nVu22D8JVZXsHGMO2ebqUHHTZEKFEKTznpUNUZRsIpa5L\nbp53ChE62TOjNnfhzbCLLocZ6FVh17IK3gf2XBl1vW3vrM3ogdEpScYGsoyc\njdnc\r\n=u9y/\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.9.2", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "12.2.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.13_1562711078256_0.5295983360095642", "host": "s3://npm-registry-packages"}}, "4.17.14": {"name": "lodash", "version": "4.17.14", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "9ce487ae66c96254fe20b599f21b6816028078ba", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.14.tgz", "fileCount": 1051, "integrity": "sha512-mmKYbW3GLuJeX+iGP+Y7Gp1AiGHGbXHCOh/jZmrawMmsE7MS4znI3RL2FsjbqOyMayHInjOeykW7PEajUk1/xw==", "signatures": [{"sig": "MEUCIBn8YIEA7hiPg/wWBZ6hCsCoVZ6QvjTUcI55qS3/WirxAiEAvdRg/9jEGxdlDE7k1o3Os9Ag8pGpcOl24BKBpKV28hY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1403248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJgfnCRA9TVsSAnZWagAAIekP/id0qFzyGAg/X1h0jFNU\nE2A++zPr6a/PkI9Ri33ODt+k4+S/2i9UiaJvzOyKxR8CgQ/IC6wlpCqZyyH0\nCQ8E8jrccKPc2EnRSxUIRe9ypMK2SwRuLEnDh0cF65q+gdj8qmUTWpFv3fFv\nSLYkDg9vG1Bxkcm5tk9DxM+ge2Ra3Hv257w5CtVWNsjW6ndVrdj2jmLF5Pgz\nL2B2+DIRoRISGXjqNXKRZ0rY6DR9ceQIoez3mHExCUAwCb6EXlXYPtzCi/Ap\nugZrrLYMYagB42NQ9B2DmtV6IQSIvjmpmAzhTXMafp92BJUGX9SizQENNNkx\nok1Jlsb4D0FjuJuXohzrXKKosRUt1kNDtVETzEUAyK761bDOucAwhUVTy/X+\nPoPfQK8eAoeTK6Vy70v/lBqrlCa6PMrqgFdPSmJZUGRdqIL4jX1zUsoeumTv\nrpgVlDhKWxPj5eJVq/uypXr5t52dxgXwY11OxMa/nvtCMZFZwUQ4j83DnAcJ\n6cxPKZDKv+2Nuo1OoKYY6SEgiyvcGDJqSTDH1yRTG33SjodQFggLFvFy2l3Y\nFmjAAaCksPawgPz303NAeAWPAbT2sytinl4treWBUec3gmDqRWGimP5Ygp/R\nbxrjI5oHXt+1giA3BMr3or4eQ9vitIhD8ki1wWLUKmsnMoUfZyw9AmYh1TPI\njRkX\r\n=f1r9\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "8.16.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.14_1562773479015_0.866734273135471", "host": "s3://npm-registry-packages"}}, "4.17.15": {"name": "lodash", "version": "4.17.15", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "b447f6670a0455bbfeedd11392eff330ea097548", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.15.tgz", "fileCount": 1049, "integrity": "sha512-8xOcRHvCjnocdS5cpwXQXVzmmh5e5+saE2QGoeQmbKmRS6J3VQppPOIt0MnmE+4xlZoumy0GPG0D0MVIQbNA1A==", "signatures": [{"sig": "MEQCIFbxnpX3GT/QFjnCfbrXnvHQn6m6hAQozAKbkZ08lXA3AiAqcTSxmEMqNHzpSpZrzp3rwW0Imc2nMNB6lTSMmVnukg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1401029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMSrfCRA9TVsSAnZWagAAwdsP/RhUWg82MeFBHu0zHeQd\nhslgH/+xAkHR3SGuT5q9t6qqkNsqgSl25RdtEJqqlV69Ni2W8y5YgNfrQTVq\ntcmjDoJejlU4qnNiUNP/R81Ph+bNKh0+DcCCo+2YKMEk5XVCNxTWxETyasYR\n0PNuRR6dbvhWij0AEL6Uk2VF0vI9T/hFje7NeNnNf9CLk+mfKtxydB62cTtZ\ntojexG52iH5w4Lk35gjyOgAoWCFSk09GRgOW3RmmiPU8Cg7gizcW/7ynimbV\nPXY/GpdZ4EcmZSb7F47TBl/Ymv4TE2+H8LEvdxdtFfsaCyu0i/9wyi34j6ja\nujYyWCiie2IkJYSONWc0UxIBtq6tjLrXanuhsL7caXZWyljvqLRPYcfWJVWQ\nheL1YGLBOWtZtM51h7uHfnz/3YViQxkT55z5eziAWFGKX/x8ByUO2LU+LGjv\nwzRb8fqi5l7Wz3BjNbDxLyA8X8FsSu2rzPogz9bHvqc5WYknpoPJp/aRUADz\nlLoO+fwQRA9PYKhTcLMDMJQ+NIxaA++6eJ8ZIokp5Qx9RvhIbr3pMgg13PbN\nuiV1LT6I2H7y9NC6yucgE4/siQmcRSi5y5XT3G1jx6SLOvTFa5d4Ae5CSk1L\nSUqRZ+/x8uDI7fr/DZP9gCUgKrhd/ATikrR2wloqOyv0x27KB1WzDsNie3am\nyCB9\r\n=KaV2\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.10.1", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "12.2.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.15_1563503326395_0.45150483878351166", "host": "s3://npm-registry-packages"}}, "4.17.16": {"name": "lodash", "version": "4.17.16", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "34fa1571037ea4b35b2726932961eafedad3058e", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.16.tgz", "fileCount": 628, "integrity": "sha512-mzxOTaU4AsJhnIujhngm+OnA6JX4fTI8D5H26wwGd+BJ57bW70oyRwTqo6EFJm1jTZ7hCo7yVzH1vB8TMFd2ww==", "signatures": [{"sig": "MEYCIQCwV9DurwdKKyl5V8nfN8DLsHVvlh6MtSJivzZj1ek4cAIhAMeYZI28bAbqpyQJ4udBsOb+GZOz+hUHwF7TPIz2hV3x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 588290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBaT+CRA9TVsSAnZWagAA9u4P/jAuES9/sBN84GqMeL7R\ndqJFTnRLJo0NbdcKVYKk1nM/rwVrcLcKpg0DmFRR3xcmvzJgor+JXODedxRa\nUmiewjPAS7fQ2JyZVwJoF5xAXGcP+F3djWcZN5hR0UZ/HdJqTRWYtMd5OMo+\nm+eB3rGzWFIPglSNZaFAfqlsA1P8roZAER117tRb+NaL9jRwUorki275vasd\nZyp/+E0myLimIPwMhgMzHgd47qRDZ3tKq8KKXCe+M/Hr9WBJ1xuGVhFM5I9K\nuBeN5oWCpFEsuhHhzE26vmdvRuPcBna2M+XvHdLTTj2vN4sVY6qJ8Y/xnoQI\nk1+zfjoJs15yXIjP47el6CLD16pR+L2ahC7gyooZK5sa8mffcBEaEdUj6pe5\n2vhnGjUMFVDgcaU022ZBGxucOwgbpnDqPY2qczBsln3WJUtCuJoKgIsn52p4\nOpQvm0o/zV/S6Scjf06ociKdPxNtDOXUGuruzBsNeicxZf0fZ8qYaoAd+RvD\n635py/CDoi6pCRqkvz7uNcq/1Cn+rlp0LLai8LKO2vtqsaaTh8iUE1JXXdF0\nCR6wulyTWVIEtXaZzLOjKgZbz6l8fe8pntntc3cP9ey5VEFqtjGmg+nOwUe0\n5tx267GXMtx6zljdfNCP7vYt6gsXCFIB4hDK/49fMnCpwmqKUT/aK50n2C6L\nWRyG\r\n=T2TM\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "12.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.16_1594205437715_0.8538345908356488", "host": "s3://npm-registry-packages"}}, "4.17.17": {"name": "lodash", "version": "4.17.17", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.17", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "d9018b3acc57a95c9dcf4a45c6b63b877b6c2d45", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.17.tgz", "fileCount": 1044, "integrity": "sha512-/B2DjOphAoqi5BX4Gg2oh4UR0Gy/A7xYAMh3aSECEKzwS3eCDEpS0Cals1Ktvxwlal3bBJNc+5W9kNIcADdw5Q==", "signatures": [{"sig": "MEYCIQDqkPVetz/b5+H8cOtM1BWlnQEZOPO1NoEgEpH5tzQXzQIhAMa1S/HDtLcya9ugQCJjqY4ehNuM05TaQhqLCSkN1y9C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1200157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBbdFCRA9TVsSAnZWagAAIiEP/0iFd9QyeRo/CCPhVFfl\nzaJgX24nDn/BGbHXz/U0rjcSOPzzefO8dWHmba0okkOck5+M2SWmgpfzCGVo\noOjsFqDyK5DgVzPRLiDUqjcJ4BZJ3CoFAjNMzSOu9xSWQnUUGHy86sWX1HYg\nWoRnPl0NOV0k7IyjtqhaQA6c0jZSyFEQvoMnQwUoJQmvEAsiJj5dEabzrNS8\nWwDg0ApevRSPAjGccvVE4AV5b5f3WoldFrZrnd23E7yvoEizxk9OgHgSA+0f\n+B7icggQAhJdAUUrmQgs5e+x0HKsW8aywAv0w9SAlGWHsOueBRNqbuYH1sZC\nTtCG8XovQ1h6ZGsewKB8crIa0Mi7gwnti3CmWDXSUJM2LCQ9YoS3cNRPxhBR\nxfWRrKnBgeMdj69iGJFlE5TJ3iwXR16d8B3SDc18UQqvBp6wdrIIfjjGI1OD\nJLosllG2q/l+ohImPjyZtSS/gO++COjEgOjOcPaeuOlfM8oU6bhipoRi4R04\nqOtqRan/6/w6ZOnLGhOFOxXeQl85d1LhDFVoERLE5i5T10zGMYNrUClIOAYy\ns6IEA+zLBgKoVTgoMu5+vxVEWzmAqZcXJwjnH0LGeiqgxk4JxaUgECAtnErb\nMtBS70+3LGmCGxIEd9iyYqpw63dEVgy8pQ7WWOyekmD8Sm3WZZoCKoDGDKxE\nmJNN\r\n=awDN\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "12.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.17_1594210117119_0.3839963776911002", "host": "s3://npm-registry-packages"}}, "4.17.18": {"name": "lodash", "version": "4.17.18", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.18", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "5c5f072c5c02f386378dd3f6325b529376210427", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.18.tgz", "fileCount": 1046, "integrity": "sha512-au4L1q0HKcaaa37qOdpWWhwzDnB/taYJfRiKULnaT+Ml9UaBIjJ2SOJMeLtSeeLT+zUdyFMm0+ts+j4eeuUpIA==", "signatures": [{"sig": "MEUCIQCteJKlBD9e3XGMzpkKDF+2WqIwxYG9YXKuBAiaYgAZlgIgWxSgyxvCXBBQqI3lD7GdCTep30i3KjiZSOnEA2WN/uM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1273063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBe8/CRA9TVsSAnZWagAAXdYP/2CPIiURBifbQYycCKIJ\nzekkOQVmon37K9nM8iGmv2fMfckUdtAoGHBjByTkBQWIinBG4/CUvyEM7vJ0\n2HhUbwYgxbXMfXbaUY0ouG1glrKp1BrzcVTzZ1PkMmAFO6JTjI0jPydiqkj2\n7kaDntbt6fwCp/mq85RbOOXAdyIdIIlhqsn4tgJeTEcACqqIYJ//XtlY7jHu\nkfANUPZff1OppUGYrOnPC6I78xzUt4YnzXIDAvopW/yfA2AG1ZyEEWVxquXJ\nGfJiRG5SoBcRdNglBrs+7FnXuGPhRkHIByArEVJWfJKTKM9U5AK+1gV2cS6f\neU2sste2dbunen9fO2WyvIGZkBPPjLTTOgEjCFo9YXvGwfWu94fXhrOkBjFg\n/6zTnS3EnljRk2s25Iv6eEsRJPJbXV9HFr/mai/wOcwE3A4vncmDiuM1Vux6\ntNRfKngGvbnJYC2bVx90vjZx+w6v6W8b45eI8Nm14fOqL0w38FbpSmUmPWur\n/cgXnNtNWF7QJEpEtFkiZJIUk5IEJv+cAYVwCYXwfqj4g95aaWt/QMaE4D7Y\nHPtZEfDIxULkr9pK706Jr2DRFaP1MmovYZHPGxKsFHfyTm2kmRV/k2xeSO5c\nr4lDJKCOAJ3sOp+vutz0VIiLdXuFsXuGr7JHtVSuF43K2TSH232ricGZ9R3s\ny0rq\r\n=NwT4\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "12.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.18_1594224446899_0.7199383586819958", "host": "s3://npm-registry-packages"}}, "4.17.19": {"name": "lodash", "version": "4.17.19", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.19", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "e48ddedbe30b3321783c5b4301fbd353bc1e4a4b", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.19.tgz", "fileCount": 1049, "integrity": "sha512-JNvd8XER9GQX0v2qJgsaN/mzFCNA5BRe/j8JN9d+tWyGLSodKQHKFicdwNYzWwI3wjRnaKPsGj1XkBjx/F96DQ==", "signatures": [{"sig": "MEUCICs6pM7PPuZgT54ctLv0vwXHWCCTtlNQlbuHzd2XgNWKAiEA5tsHIdnePfY5htqpDQW6NWHD1fEqljJ5Qv6cUpxEfIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1401746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBf8BCRA9TVsSAnZWagAA3lsP/jPzJr6UMCnUQX2Pqc+a\nWYWORh/94RarO7QO8MUjwyokeKRG1OvN0ATY4CK1NEBw8zxHhyUXAuC2BT20\n6gaExYAT7N2w22RVojgfsn4O74dIa5IgPiqR0VPHdjzXONvLA71Hu09LhCiO\nw/1s9KG1BrUR4D2gdvi9KApq1kWVNKhPKm0FIeVuGCPzGM0yq0DujBgcnXq6\nkq8FLoMqlGeV1OHi5ARyK8UyIuPtymZ4MfVkjb1ZBDstxvVGxPh1NZPcfEPx\nzimmiYrdldeJKU09Eb2gql0GB5pHZX5uZsnQZdZWyrsxJMj82ve9pYH2aoSM\ngftJTMGTL4ysww09+RvlBVIE2e0jFPcMc7L9UqIxnfas4VRzgJNNXfErl1vl\n7cu8VyTVmWIJQcehSq9VwHxKlEOzA32sVNUq7GVZSE1oUxODF1vSOfq61JlL\nyOFje4VqQRT5AKTAZntQjz9xyLXOVdeamT+qOrKVyCUgnpo/qoucEp4Wbn4W\n0lnOMBmzJIKHwunR6/sTMMfFxxrhs4IChtnJg5gDr+pD9hFpeLKXUXOsIrZW\nBgB7UJopWgxFveyoa9fgeuoMO2RZ3HmG4ro8opIgCGwE2W2UQv+Tn2HvHJlE\ncX3TdNeKxDn/x96X9FhP0EnJERRJTSDQ3RLXdc0+3GGSTr7lze3HOseiM4jq\nUXtl\r\n=Vw6m\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "<PERSON>ias", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "12.17.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.19_1594228480690_0.8185161063468651", "host": "s3://npm-registry-packages"}}, "4.17.20": {"name": "lodash", "version": "4.17.20", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.20", "maintainers": [{"name": "bnjmnt4n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "b44a9b6297bcb698f1c51a3545a2b3b368d59c52", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.20.tgz", "fileCount": 1049, "integrity": "sha512-PlhdFcillOINfeV7Ni6oF1TAEayyZBoZ8bcshTHqOYJYlrqzRK5hagpagky5o4HfCzzd1TRkXPMFq6cKk9rGmA==", "signatures": [{"sig": "MEUCIQDPF8qtGU3yxHj4Yw4h9hpt+DCVT9dlk9ApvKxVXPwv7AIgSLC2TkL6ONbFlhyV2ZcE4v52ANJMoA/HwKY+zKt9Kh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1406354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNXAiCRA9TVsSAnZWagAAtmYP/2G2ijVcDDyRacsKXn8Q\niX5zNGG+Od+xSuOXrMRG32hjB1giuXR2t8mlsJLQpFyQVgAexcr22J0oq0Kb\nUouNSYrjm6qfK/u5ZUg8lR/Q3L+QiaxsfNnS7FWCO4xqUB0FlI5rKtnq4zhH\nWpPscWw1S/0vV1tz9OvibtfiMDWw5m9AYHk7ckISHmiEMBWURWyDjStxVjmn\nIupfRuCjJdNoxdFyRMaXktbFaCqdMoaT00x5ImxTbIR2ZQdTQT7fA8l3FvvZ\nX/avXT8sqsY0gMCJqZGZITWI/6jIvoMLeE8IqPvAweX+rjHpTFbj9u+SjhNI\noLRY2Ya3bCCUM/T7ZeShMDOeNCyqaU4p3s5VWBQ7PG8FRUjtRdJXpmBf44B4\n6ew+lh2qK+P7FcIcJ4NDpo0/pek4keOpKmUyOYEsrXbnFKdXyzMxAztydu2U\nYR20ePPVsAh2dxwnTVW+jxoutB09gmM3YhgtuOEf16dSEsu47Tntd039li0A\nOSwvLK2jEiyyaeGh7nZSaNPr2Sgj5uq1DrOjR/eKOhY0nFUzdcDAjm8ocVSe\nncavD4t+VFnABdnog5Ub11luRNoOTBvIX+c/9DshlfRzGKIu6pq8SirU6P2j\n3y40bR5yzjhxVM/E2RsqAsPgSPl/1BLyO4+4MKIxhVTh259tWf3YxbWbuXo8\n5EAn\r\n=w2Gq\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "gitHead": "f2e7063ee409ff40a60b14370c58dceee1a2efd4", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "bnjmnt4n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.20_1597337634014_0.4491479741207298", "host": "s3://npm-registry-packages"}}, "4.17.21": {"name": "lodash", "version": "4.17.21", "keywords": ["modules", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "lodash@4.17.21", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bnjmnt4n", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://lodash.com/", "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "dist": {"shasum": "679591c564c3bffaae8454cf0b3df370c3d6911c", "tarball": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "fileCount": 1054, "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "signatures": [{"sig": "MEUCIF3Yithbtmy1aEBNlfNWbLswAfPIyQUuNUGARD3Ex2t4AiEA6TlN2ZKJCUpS/Sf2Z6MduF1BNSvayHIpu5wAcICcKXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1412415, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMS3ZCRA9TVsSAnZWagAA8+4P/jx+SJ6Ue5oAJjz0L7gw\nLDD5YvP8aoliFq4GYkwUXfVQvOwomIPfa+U5Kao/hDfuwFQ/Bq5D5nSsl2bj\nrjJgvlKXna0SId8AgDgY2fB7zSfninuJvalY4iTWMN8DFSpG0XE2QFfoKpd3\njDmuzcNtgr79QV6DgjOVkHiP1IGNDlLTc1QEKiwo/5CdGQi1q/iCj6dViQMJ\nByuuuV2Qzi3f/FI25cG797WZar1MHhhlcnB50HiVBGp54IZOyuqdqWPduZQo\nvhONtonxPGBm3/J+uAkeUSSyL3Ud+FzLvdg8WEI9gDL0yvU4k0FcsnOONEYn\nngLaKEsw2xAnPBYW3Lf73Jnpwx6FAT3k49kgzxiNYSxEo7x4wiuNtBoDMyNw\nEKj6SZ0bUNmaJgiMfDnnDjCKjI3JrO1hho8z6CkwuvxuWLlW9wSsVayggzAI\nEhfeTeISugVHh332oDY2MI/Ysu8MnVN8fGmqeYQBBFj3aWatuA2NvVjACnX/\n54G7FtCU8TxZpm9shFRSopBx8PeI3r+icx1CT8YVFypY416PLnidHyqtME1G\neuRd1nWEz18hvVUAEHmuvHo+EPP3tITmTTUPQcZGMdBcZC+4UBmPMWX466HE\nbHw4aOnUWMa0sWfsERC5xzRZAb4lgMPEoTOnZyN4usMy7x9TzGZKZvU24HUE\nmpae\r\n=NOmG\r\n-----END PGP SIGNATURE-----\r\n"}, "icon": "https://lodash.com/icon.svg", "main": "lodash.js", "gitHead": "c6e281b878b315c7a10d90f9c2af4cdb112d9625", "scripts": {"test": "echo \"See https://travis-ci.org/lodash-archive/lodash-cli for testing details.\""}, "_npmUser": {"name": "bnjmnt4n", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Lodash modular utilities.", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/lodash_4.17.21_1613835736675_0.01913912595366596", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2012-04-23T16:37:11.912Z", "modified": "2025-06-30T15:00:05.999Z", "0.1.0": "2012-04-23T16:37:12.603Z", "0.2.0": "2012-05-22T04:06:24.044Z", "0.2.1": "2012-05-24T21:53:08.449Z", "0.2.2": "2012-05-30T07:56:26.644Z", "0.3.0": "2012-06-06T20:01:49.669Z", "0.3.1": "2012-06-11T04:12:51.792Z", "0.3.2": "2012-06-14T19:19:49.846Z", "0.4.0": "2012-07-11T17:14:20.142Z", "0.4.1": "2012-07-12T04:56:31.883Z", "0.4.2": "2012-07-16T18:49:41.162Z", "0.5.0-rc.1": "2012-08-07T15:08:27.331Z", "0.5.0": "2012-08-17T20:13:07.054Z", "0.5.1": "2012-08-18T20:15:42.131Z", "0.5.2": "2012-08-22T16:22:03.757Z", "0.6.0": "2012-08-28T16:01:09.459Z", "0.6.1": "2012-08-30T08:01:38.808Z", "0.7.0": "2012-09-11T16:24:07.425Z", "0.8.0": "2012-10-02T06:49:38.116Z", "0.8.1": "2012-10-04T08:53:29.540Z", "0.8.2": "2012-10-10T07:51:31.600Z", "0.9.0": "2013-08-31T04:46:20.474Z", "0.9.1": "2013-08-31T04:49:15.754Z", "0.9.2": "2013-08-31T04:52:21.307Z", "0.10.0": "2013-08-31T04:56:09.871Z", "1.0.0-rc.1": "2013-08-31T05:00:28.060Z", "1.0.0-rc.2": "2013-08-31T05:05:31.147Z", "1.0.0-rc.3": "2013-08-31T05:08:51.705Z", "1.0.0": "2013-08-31T05:11:42.645Z", "1.0.1": "2013-08-31T05:16:47.715Z", "1.1.0": "2013-09-04T14:23:45.728Z", "1.1.1": "2013-09-04T14:24:07.907Z", "1.2.0": "2013-09-04T14:24:34.140Z", "1.2.1": "2013-09-04T14:24:58.381Z", "1.3.0": "2013-09-04T14:25:19.793Z", "1.3.1": "2013-09-04T14:25:40.429Z", "2.0.0": "2013-09-14T04:22:28.159Z", "2.1.0": "2013-09-23T05:57:42.595Z", "2.2.0": "2013-09-29T21:52:47.266Z", "2.2.1": "2013-10-03T18:29:30.163Z", "2.3.0": "2013-11-11T17:30:27.058Z", "2.4.0": "2013-11-26T19:40:30.164Z", "2.4.1": "2013-12-03T16:51:12.879Z", "3.0.0": "2015-01-26T15:09:31.198Z", "3.0.1": "2015-01-30T09:33:51.621Z", "3.1.0": "2015-02-03T16:53:35.795Z", "3.2.0": "2015-02-12T17:01:18.403Z", "3.3.0": "2015-02-20T17:08:28.864Z", "3.3.1": "2015-02-24T16:02:47.458Z", "3.4.0": "2015-03-06T16:44:06.018Z", "3.5.0": "2015-03-09T05:01:51.264Z", "3.6.0": "2015-03-25T15:36:29.983Z", "1.0.2": "2015-03-30T15:58:01.337Z", "3.7.0": "2015-04-16T15:47:35.770Z", "2.4.2": "2015-04-26T21:04:49.443Z", "3.8.0": "2015-05-01T15:45:44.760Z", "3.9.0": "2015-05-19T18:26:55.450Z", "3.9.1": "2015-05-19T21:00:20.625Z", "3.9.2": "2015-05-24T20:57:57.973Z", "3.9.3": "2015-05-26T01:47:44.058Z", "3.10.0": "2015-06-30T15:13:28.602Z", "3.10.1": "2015-08-04T06:05:06.887Z", "4.0.0": "2016-01-12T23:13:20.539Z", "4.0.1": "2016-01-25T16:06:17.924Z", "4.1.0": "2016-01-29T16:33:24.543Z", "4.2.0": "2016-02-02T08:50:17.287Z", "4.2.1": "2016-02-03T16:00:16.046Z", "4.3.0": "2016-02-08T08:57:19.880Z", "4.4.0": "2016-02-16T07:10:16.856Z", "4.5.0": "2016-02-17T08:39:42.533Z", "4.5.1": "2016-02-22T06:42:24.244Z", "4.6.0": "2016-03-02T03:24:37.179Z", "4.6.1": "2016-03-02T18:09:40.696Z", "4.7.0": "2016-03-31T15:46:33.373Z", "4.8.0": "2016-04-04T14:54:33.612Z", "4.8.1": "2016-04-04T15:43:49.109Z", "4.8.2": "2016-04-05T02:15:16.661Z", "4.9.0": "2016-04-08T15:22:34.228Z", "4.10.0": "2016-04-11T14:43:56.586Z", "4.11.0": "2016-04-13T15:32:30.507Z", "4.11.1": "2016-04-14T07:21:23.548Z", "4.11.2": "2016-05-02T15:01:02.189Z", "4.12.0": "2016-05-08T19:25:43.826Z", "4.13.0": "2016-05-23T05:07:23.403Z", "4.13.1": "2016-05-23T15:59:05.944Z", "4.14.0": "2016-07-24T18:40:58.495Z", "4.14.1": "2016-07-29T14:49:10.278Z", "4.14.2": "2016-08-08T15:35:49.019Z", "4.15.0": "2016-08-12T14:39:28.783Z", "4.16.0": "2016-09-19T14:59:14.886Z", "4.16.1": "2016-09-20T16:59:53.967Z", "4.16.2": "2016-09-26T03:11:05.302Z", "4.16.3": "2016-10-03T16:43:31.571Z", "4.16.4": "2016-10-06T15:13:30.196Z", "4.16.5": "2016-10-31T06:49:14.797Z", "4.16.6": "2016-11-01T06:38:07.989Z", "4.17.0": "2016-11-14T07:00:08.291Z", "4.17.1": "2016-11-15T07:03:25.950Z", "4.17.2": "2016-11-16T07:21:41.106Z", "4.17.3": "2016-12-24T14:25:39.754Z", "4.17.4": "2016-12-31T22:33:56.623Z", "4.17.5": "2018-02-04T00:34:41.111Z", "4.17.9": "2018-04-24T17:44:40.268Z", "4.17.10": "2018-04-24T18:07:37.696Z", "4.17.11": "2018-09-12T18:32:16.141Z", "4.17.12": "2019-07-09T21:07:51.647Z", "4.17.13": "2019-07-09T22:24:38.453Z", "4.17.14": "2019-07-10T15:44:39.173Z", "4.17.15": "2019-07-19T02:28:46.584Z", "4.17.16": "2020-07-08T10:50:37.915Z", "4.17.17": "2020-07-08T12:08:37.292Z", "4.17.18": "2020-07-08T16:07:27.110Z", "4.17.19": "2020-07-08T17:14:40.866Z", "4.17.20": "2020-08-13T16:53:54.152Z", "4.17.21": "2021-02-20T15:42:16.891Z"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://lodash.com/", "keywords": ["modules", "stdlib", "util"], "repository": {"url": "git+https://github.com/lodash/lodash.git", "type": "git"}, "description": "Lodash modular utilities.", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "bnjmnt4n", "email": "<EMAIL>"}], "readme": "# lodash v4.17.21\n\nThe [Lodash](https://lodash.com/) library exported as [Node.js](https://nodejs.org/) modules.\n\n## Installation\n\nUsing npm:\n```shell\n$ npm i -g npm\n$ npm i --save lodash\n```\n\nIn Node.js:\n```js\n// Load the full build.\nvar _ = require('lodash');\n// Load the core build.\nvar _ = require('lodash/core');\n// Load the FP build for immutable auto-curried iteratee-first data-last methods.\nvar fp = require('lodash/fp');\n\n// Load method categories.\nvar array = require('lodash/array');\nvar object = require('lodash/fp/object');\n\n// Cherry-pick methods for smaller browserify/rollup/webpack bundles.\nvar at = require('lodash/at');\nvar curryN = require('lodash/fp/curryN');\n```\n\nSee the [package source](https://github.com/lodash/lodash/tree/4.17.21-npm) for more details.\n\n**Note:**<br>\nInstall [n_](https://www.npmjs.com/package/n_) for Lodash use in the Node.js < 6 REPL.\n\n## Support\n\nTested in Chrome 74-75, Firefox 66-67, IE 11, Edge 18, Safari 11-12, & Node.js 8-12.<br>\nAutomated [browser](https://saucelabs.com/u/lodash) & [CI](https://travis-ci.org/lodash/lodash/) test runs are available.\n", "readmeFilename": "README.md", "users": {"184455": true, "29836124": true, "285858315": true, "306766053": true, "326060588": true, "52u": true, "axp": true, "azz": true, "dam": true, "dmy": true, "drj": true, "ffi": true, "gar": true, "hhj": true, "jwv": true, "msq": true, "pgu": true, "pid": true, "pwn": true, "rjv": true, "rsp": true, "sua": true, "ubi": true, "vbm": true, "a1ip": true, "alca": true, "buya": true, "crai": true, "d3ck": true, "dntx": true, "dofy": true, "doxy": true, "dpig": true, "edob": true, "eijs": true, "ferx": true, "fill": true, "fiws": true, "glab": true, "gr2m": true, "hema": true, "huyz": true, "ibio": true, "iolo": true, "isik": true, "j.su": true, "j3kz": true, "jaca": true, "jclo": true, "jiku": true, "jits": true, "jkup": true, "jtrh": true, "jwjb": true, "kmck": true, "leor": true, "lorn": true, "mdll": true, "mgol": true, "mkoc": true, "mkwr": true, "n3gu": true, "nazy": true, "neo1": true, "ni-p": true, "nuer": true, "nyzm": true, "ohom": true, "ollo": true, "otak": true, "pana": true, "plyr": true, "prpg": true, "rsaa": true, "same": true, "sgpr": true, "sija": true, "sunj": true, "tg-z": true, "tims": true, "tztz": true, "usse": true, "vasz": true, "volf": true, "vwal": true, "wayn": true, "z164": true, "zcei": true, "zhen": true, "2lach": true, "ad427": true, "adrai": true, "aipae": true, "alimd": true, "alxlu": true, "anh4n": true, "anker": true, "ashco": true, "avaly": true, "azder": true, "boogy": true, "brend": true, "bsara": true, "chyld": true, "claus": true, "crstn": true, "dazld": true, "dizid": true, "dnero": true, "doowb": true, "eexit": true, "eimaj": true, "evang": true, "flozz": true, "h4des": true, "haeck": true, "hanhq": true, "hanwf": true, "hdusy": true, "hilli": true, "holly": true, "hugov": true, "hydra": true, "i3fox": true, "igsys": true, "illay": true, "imgqb": true, "ineva": true, "inlve": true, "irnnr": true, "jatap": true, "jenux": true, "jmm23": true, "jnath": true, "jostw": true, "jps78": true, "jream": true, "junos": true, "junya": true, "kalil": true, "kewah": true, "kewin": true, "kremr": true, "lacom": true, "lcdss": true, "leapm": true, "lensi": true, "leota": true, "lingw": true, "loglo": true, "lucag": true, "lusai": true, "makay": true, "mattw": true, "maxto": true, "mgduk": true, "mlepe": true, "mmork": true, "mnemr": true, "nbsaw": true, "ndxbn": true, "nickl": true, "noste": true, "octoo": true, "padhi": true, "peibb": true, "pfuri": true, "razr9": true, "reamd": true, "rshaw": true, "s25ai": true, "samar": true, "saoud": true, "sbskl": true, "seldo": true, "shama": true, "slang": true, "subso": true, "suddi": true, "t1st3": true, "t3zfu": true, "tarol": true, "temsa": true, "three": true, "tht13": true, "travm": true, "weerd": true, "whats": true, "whoaa": true, "wxnet": true, "wyght": true, "xrush": true, "xyyjk": true, "yifan": true, "yikuo": true, "yisen": true, "yxqme": true, "zihai": true, "zxjin": true, "0x4c3p": true, "13snpm": true, "71emj1": true, "abijit": true, "ablbol": true, "ackhub": true, "adeelp": true, "agoral": true, "akarem": true, "akarsh": true, "alek-s": true, "aman26": true, "andamo": true, "anorak": true, "aolu11": true, "atulmy": true, "aven03": true, "axelav": true, "benstr": true, "beytek": true, "bhenav": true, "birkey": true, "blalor": true, "bodega": true, "borjes": true, "brettv": true, "bumsuk": true, "buzuli": true, "bvaccc": true, "cannap": true, "celleb": true, "chiefy": true, "chilts": true, "chiroc": true, "citguy": true, "cjc_dn": true, "cquenx": true, "cypark": true, "d-band": true, "dainov": true, "dankle": true, "dekunu": true, "dnedev": true, "dr2009": true, "eabait": true, "eins78": true, "evan2x": true, "feedm3": true, "finico": true, "formix": true, "frakti": true, "fyockm": true, "gabeio": true, "gdbtek": true, "gindis": true, "giphoo": true, "glebec": true, "guogai": true, "hevnly": true, "hij1nx": true, "huarse": true, "husayt": true, "hyteer": true, "hyzual": true, "ijidau": true, "iotale": true, "ipasha": true, "itcorp": true, "jeltok": true, "jfsiii": true, "jimnox": true, "joanmi": true, "jolg42": true, "joliva": true, "jsalis": true, "kangax": true, "karudo": true, "kaycee": true, "kazem1": true, "kcando": true, "kjkent": true, "kkjf02": true, "klaemo": true, "kmm745": true, "knoja4": true, "krassx": true, "kudakv": true, "larixk": true, "leesei": true, "legacy": true, "legiao": true, "lenage": true, "lsloan": true, "lucsky": true, "lybeen": true, "maddas": true, "madvas": true, "makeup": true, "marsup": true, "maschs": true, "mat2ja": true, "mcraig": true, "meluko": true, "merbst": true, "mestar": true, "mig38m": true, "mikend": true, "moiyer": true, "monjer": true, "movibe": true, "mp2526": true, "mpsenn": true, "mrzmmr": true, "mstapp": true, "n1kkou": true, "naokie": true, "nauwep": true, "neb636": true, "niccai": true, "niten2": true, "nmhnmh": true, "noel35": true, "ocombe": true, "oheard": true, "omar84": true, "orion-": true, "orkisz": true, "ovrmrw": true, "owillo": true, "pappas": true, "patmcc": true, "pedall": true, "phixed": true, "phixid": true, "pilfer": true, "pintux": true, "piotrd": true, "planir": true, "plitat": true, "pm5544": true, "potnox": true, "prabum": true, "psibal": true, "quafoo": true, "raciat": true, "raidou": true, "rdca84": true, "redmed": true, "refack": true, "ryandu": true, "ryaned": true, "s.well": true, "s_grig": true, "sampsa": true, "sdove1": true, "seanjh": true, "sermir": true, "seront": true, "sml782": true, "snarky": true, "sobear": true, "songxc": true, "splode": true, "sprjrx": true, "stargt": true, "stdarg": true, "stryju": true, "tarcio": true, "tcrowe": true, "techfe": true, "thefox": true, "tmysok": true, "tocker": true, "toogle": true, "tybenz": true, "tzsiga": true, "v3rron": true, "vcboom": true, "vidhuz": true, "vmleon": true, "volebo": true, "walmik": true, "waspar": true, "webpro": true, "wleong": true, "xlaoyu": true, "xlarsx": true, "xu3927": true, "xu_q90": true, "yangzw": true, "yhui02": true, "yichan": true, "youwin": true, "yuch4n": true, "zetxek": true, "zhoutk": true, "ziclee": true, "ziflex": true, "zoluzo": true, "zpoons": true, "zwwggg": true, "_mrizky": true, "akarzim": true, "akshayp": true, "akwa770": true, "alectic": true, "amaynut": true, "anoubis": true, "apehead": true, "arielfr": true, "asm2hex": true, "azmenak": true, "baldore": true, "barenko": true, "bcawrse": true, "belcour": true, "beyoung": true, "bingmax": true, "blittle": true, "bloom_c": true, "blueqnx": true, "bomtrix": true, "bonnema": true, "bsander": true, "cambera": true, "cdubois": true, "ceejbot": true, "cellule": true, "chzhewl": true, "cocopas": true, "coerick": true, "collado": true, "cooboor": true, "cpilson": true, "crowbar": true, "cueedee": true, "dac2205": true, "dangmin": true, "daninet": true, "dean-xu": true, "dgsmith": true, "drafael": true, "eli_yao": true, "eloscod": true, "evert0n": true, "evkline": true, "firefox": true, "fkamani": true, "flitrue": true, "fullrec": true, "funroll": true, "gakis41": true, "gdibble": true, "geese98": true, "gemini5": true, "gollojs": true, "gpuente": true, "gseguin": true, "gztomas": true, "hao2701": true, "hartzis": true, "herman7": true, "hhsadiq": true, "hustcer": true, "io2work": true, "itonyyo": true, "itsmyth": true, "jackfei": true, "jackpot": true, "jacques": true, "jaguarj": true, "jalcine": true, "jariasl": true, "jdalton": true, "jimmylk": true, "jmorris": true, "joylobo": true, "jsonwen": true, "jyounce": true, "kc15155": true, "keybouh": true, "kimemin": true, "kmanion": true, "kontrax": true, "kparkov": true, "lamquit": true, "laoshaw": true, "lemulot": true, "lhj5709": true, "ljq2731": true, "llambda": true, "mamalat": true, "mamsori": true, "maples7": true, "marcker": true, "mathias": true, "maxwang": true, "mchampi": true, "mfunkie": true, "mmercan": true, "mrmalec": true, "mrosata": true, "mtorp94": true, "nanxing": true, "nilz3ro": true, "njhunit": true, "nogirev": true, "norfish": true, "nunogee": true, "nwinter": true, "offirmo": true, "onbjerg": true, "otorras": true, "ousmane": true, "pixel67": true, "pocesar": true, "prabum3": true, "preco21": true, "quzhi78": true, "rager_m": true, "rapt0p7": true, "raskawa": true, "rcastro": true, "rebolon": true, "rifaqat": true, "ripdash": true, "rommguy": true, "rosshal": true, "rparris": true, "ryanlee": true, "ryugaku": true, "rzcoder": true, "sahlzen": true, "samersm": true, "satazor": true, "satjeet": true, "schm-dt": true, "sdolard": true, "seaseng": true, "sehrgut": true, "sej20xx": true, "sendypw": true, "severen": true, "sfran96": true, "shedule": true, "simioni": true, "sjonnet": true, "skarlso": true, "smrr723": true, "snows_l": true, "soladmd": true, "sopepos": true, "soutarm": true, "spalger": true, "spinbit": true, "sprying": true, "sshrike": true, "stooboo": true, "takonyc": true, "theaqua": true, "timonvs": true, "tin-lek": true, "tonstwo": true, "totemat": true, "touskar": true, "tpolyak": true, "twt0308": true, "tyscorp": true, "undre4m": true, "vanzorn": true, "vezquex": true, "vivekrp": true, "wqhashs": true, "wukkuan": true, "xbsheng": true, "xeoneux": true, "xingtao": true, "xngiser": true, "yakumat": true, "yokubee": true, "yoranwu": true, "zaephor": true, "zard999": true, "zendayu": true, "zlapper": true, "zolomon": true, "zxqgirl": true, "zyy7259": true, "abnerlin": true, "adams_ea": true, "adaszyns": true, "ahvonenj": true, "akinjide": true, "akmittal": true, "alexahdp": true, "almgwary": true, "alokozam": true, "amiziara": true, "andershl": true, "angrynut": true, "antonmyr": true, "avernon2": true, "bahmutov": true, "bapinney": true, "bcowgi11": true, "bdphil86": true, "bkwarner": true, "bmunoz89": true, "boat3471": true, "bonashen": true, "bsdnpmjs": true, "buru1020": true, "caebbage": true, "cbarrick": true, "ceogavin": true, "cilerler": true, "ckross01": true, "cmtegner": true, "coderjim": true, "colageno": true, "cologler": true, "comandan": true, "congcong": true, "corbacho": true, "crowelch": true, "cslasher": true, "dadoumda": true, "danday74": true, "danlevan": true, "daskepon": true, "deerflow": true, "demopark": true, "denbon05": true, "dennila2": true, "deve6per": true, "dgarlitt": true, "dizlexik": true, "djamseed": true, "dlemesle": true, "dolymood": true, "drveresh": true, "dschnurr": true, "dsellers": true, "dzhou777": true, "eagleflo": true, "eb.coder": true, "edloidas": true, "enriched": true, "erikvold": true, "evanhahn": true, "fabioper": true, "faraoman": true, "fargie_s": true, "frankl83": true, "freebird": true, "gejiawen": true, "geoorgex": true, "graubnla": true, "guyellis": true, "hardball": true, "helloweb": true, "hkb06542": true, "honzajde": true, "hpherzog": true, "hricha05": true, "hummatli": true, "hyanghai": true, "iamninad": true, "ilaborie": true, "insentek": true, "jazzmine": true, "jdkenney": true, "jhotmann": true, "jlmorgan": true, "jmontesc": true, "jonathas": true, "joshberg": true, "jweyrich": true, "kaasdude": true, "kainos90": true, "karwanoa": true, "kenlimmj": true, "kingcron": true, "kissarat": true, "koobitor": true, "koulmomo": true, "krabello": true, "kuldikin": true, "kulyk404": true, "kwhitley": true, "leejefon": true, "leodutra": true, "leonning": true, "leonzhao": true, "libaoxin": true, "losymear": true, "lubelski": true, "lumirent": true, "m_oebius": true, "makediff": true, "makenova": true, "manishrc": true, "markstos": true, "masonwan": true, "meizhong": true, "merrickp": true, "mgamperl": true, "mglrahul": true, "mikemena": true, "mikewink": true, "milan322": true, "minchnew": true, "mluberry": true, "monishcm": true, "monteslu": true, "mtscout6": true, "nagorkin": true, "nalindak": true, "nanikore": true, "nchmouli": true, "niksudan": true, "nketchum": true, "nohponex": true, "oncletom": true, "pablaber": true, "padolsey": true, "pddivine": true, "pejulian": true, "person51": true, "pimterry": true, "pnevares": true, "poosanth": true, "pughpugh": true, "qddegtya": true, "rbartoli": true, "rbelouin": true, "realog32": true, "redbe4rd": true, "robba.jt": true, "rrobayna": true, "ruban-gt": true, "sakai135": true, "samuelrn": true, "schacker": true, "seanzawi": true, "shaddyhm": true, "shahzaib": true, "shatting": true, "shiva127": true, "sibawite": true, "sixertoy": true, "slang800": true, "sobering": true, "solenoid": true, "spmsupun": true, "sprybear": true, "staraple": true, "stephn_r": true, "sumit270": true, "szymex73": true, "tbranyen": true, "tdmalone": true, "thameera": true, "the-darc": true, "thekuzia": true, "thorsson": true, "timmywil": true, "tkhemani": true, "tmurngon": true, "tomasmax": true, "tombenke": true, "tonydieu": true, "tonyleen": true, "tosbodes": true, "totoharu": true, "trendoid": true, "tyrionbb": true, "vishwasc": true, "voipnorm": true, "voxpelli": true, "waitfish": true, "waiwaiku": true, "winblood": true, "wkaifang": true, "wongulus": true, "wxh16144": true, "xiaobing": true, "xueboren": true, "yadomi29": true, "yang_hao": true, "yhnavein": true, "yokiming": true, "yonester": true, "zalithka": true, "zemgalis": true, "zeusdeux": true, "zguillez": true, "zhouanbo": true, "zhoushx3": true, "zhuangya": true, "zladuric": true, "a.sanchez": true, "abalandin": true, "abood2017": true, "abuelwafa": true, "adamkdean": true, "aland_146": true, "alejcerro": true, "alexjsdev": true, "amenadiel": true, "amit-avit": true, "amit00272": true, "anaumidis": true, "andrewlam": true, "arcanedev": true, "asawq2006": true, "asereware": true, "assiduous": true, "avenida14": true, "backnight": true, "banbara23": true, "bartmichu": true, "bengsfort": true, "benwyse11": true, "bobxuyang": true, "bradnauta": true, "bredikhin": true, "bryan.ygf": true, "ccortezia": true, "centiball": true, "chriscalo": true, "cilindrox": true, "codemouse": true, "codeshrew": true, "coding327": true, "cparker15": true, "craigklem": true, "crutchfix": true, "danharper": true, "danvirsen": true, "davidrlee": true, "dbrockman": true, "debashish": true, "demon-php": true, "devonning": true, "dezeloper": true, "dhritzkiv": true, "dlpowless": true, "dominykas": true, "drdanryan": true, "eddieajau": true, "edosrecki": true, "eduarte78": true, "edwingeng": true, "elviopita": true, "eric.chen": true, "fadihania": true, "fahmi1908": true, "feferonka": true, "fgribreau": true, "flashpunk": true, "fleischer": true, "fmakareev": true, "fmonsalvo": true, "foreigner": true, "gableroux": true, "gabroot85": true, "goldenboy": true, "golyshevd": true, "grabantot": true, "green_goo": true, "grin_zhou": true, "guumaster": true, "hanyi1616": true, "hcvazquez": true, "heartnett": true, "hehaiyang": true, "henryfour": true, "hosomichi": true, "huqiliang": true, "iceglaive": true, "iceriver2": true, "igreulich": true, "instriker": true, "isenricho": true, "j3rrywan9": true, "jakedalus": true, "james3299": true, "jedateach": true, "jeffgreco": true, "jerkovicl": true, "jhillacre": true, "joaocunha": true, "joelhooks": true, "joeybaker": true, "joeycozza": true, "jorgemsrs": true, "juriwiens": true, "karmadude": true, "kevinagin": true, "klipstein": true, "kulakowka": true, "l8niteowl": true, "landy2014": true, "largepuma": true, "lazycoder": true, "leahcimic": true, "leeovehui": true, "leonel-ai": true, "leonweecs": true, "leq382121": true, "liuxilong": true, "maalthous": true, "magemagic": true, "masterton": true, "maxwelldu": true, "meijin007": true, "mikemimik": true, "milfromoz": true, "mistkafka": true, "mjurincic": true, "mohsin101": true, "mojaray2k": true, "mordenius": true, "msvanberg": true, "nbuchanan": true, "nhuesmann": true, "nice_body": true, "nickeljew": true, "nulpatrol": true, "nycdotnet": true, "oakley349": true, "olson.dev": true, "paallaire": true, "peterteng": true, "philligan": true, "piixiiees": true, "pnhung177": true, "qqcome110": true, "rafarocha": true, "ramzesucr": true, "rbecheras": true, "redstrike": true, "retorillo": true, "rezaeig22": true, "rgraves90": true, "rlafferty": true, "rmartinus": true, "rogeriera": true, "rosterloh": true, "rylan_yan": true, "samishken": true, "sasquatch": true, "schnarald": true, "seekshiva": true, "seerdomin": true, "servel333": true, "shakakira": true, "simevidas": true, "simloovoo": true, "sirkinder": true, "sjonnet19": true, "skyrising": true, "smallhelm": true, "sparkrico": true, "spike1292": true, "squarejaw": true, "stanzheng": true, "starknode": true, "steel1990": true, "stennettm": true, "sternelee": true, "stevekane": true, "stone-jin": true, "sujeet555": true, "sunnylost": true, "terrychan": true, "thepanuto": true, "thomask33": true, "timonwong": true, "tomgao365": true, "trocafone": true, "troyblank": true, "troygizzi": true, "ttsuchiya": true, "tylerhaun": true, "udaygowda": true, "uncledick": true, "valenwave": true, "weekeight": true, "whathejoe": true, "xinshihao": true, "xmflswood": true, "xumakjosh": true, "xxsnake28": true, "yanrivera": true, "zbialecki": true, "zeroth007": true, "21xhipster": true, "abdihaikal": true, "abhinav.th": true, "aditcmarix": true, "ahmetertem": true, "alexdreptu": true, "alexindigo": true, "alin.alexa": true, "amattbryan": true, "amdsouza92": true, "apoutchika": true, "aquiandres": true, "archtaurus": true, "ashish.npm": true, "avivharuzi": true, "beenorgone": true, "billfeller": true, "blackrocky": true, "bluelovers": true, "bruinebeer": true, "cbednarski": true, "cestrensem": true, "cfleschhut": true, "chenkechao": true, "chiaychang": true, "chinaqstar": true, "chrisbolin": true, "cj_joulain": true, "clarenceho": true, "creativ073": true, "daniellink": true, "dannyfritz": true, "dannynemer": true, "darikspark": true, "davidchase": true, "davideross": true, "davidrapin": true, "dccunni171": true, "degouville": true, "delapouite": true, "derekbrown": true, "desmondddd": true, "dh19911021": true, "doublejosh": true, "dozierjack": true, "dudemullet": true, "dwayneford": true, "echaouchna": true, "edgardoalz": true, "ericlondon": true, "f124275809": true, "farskipper": true, "fsepulveda": true, "gaper01093": true, "garrickajo": true, "gerst20051": true, "gilbarbara": true, "giussa_dan": true, "haaaiiimmm": true, "halfninety": true, "haoruiqian": true, "harumambur": true, "hngrhorace": true, "hoibatpham": true, "icflorescu": true, "igabdullin": true, "instazapas": true, "james.yang": true, "jameskrill": true, "jbpionnier": true, "joelwallis": true, "johndorian": true, "jswartwood": true, "junjiansyu": true, "jussipekka": true, "justforuse": true, "justinshea": true, "kentcdodds": true, "kevbot-git": true, "kevteljeur": true, "knksmith57": true, "krostyslav": true, "kryptokazz": true, "kuzmicheff": true, "langri-sha": true, "leonardorb": true, "lesterzone": true, "lijinghust": true, "lisafrench": true, "liuhuadong": true, "liushoukai": true, "loicmahieu": true, "luffy84217": true, "manhhailua": true, "mattsexton": true, "mezeitamas": true, "mgthomas99": true, "miroklarin": true, "mjwilliams": true, "mnlfischer": true, "moharram82": true, "monkeymonk": true, "morogasper": true, "mpmckenna8": true, "ocd_lionel": true, "oldthunder": true, "omidnikrah": true, "pascaliske": true, "pixelcraft": true, "pmbenjamin": true, "princetoad": true, "ptrevethan": true, "ricardweii": true, "roberkules": true, "rocket0191": true, "romelperez": true, "ruiquelhas": true, "sankalp404": true, "sanketss84": true, "saumotions": true, "sbruchmann": true, "seangenabe": true, "shadyshrif": true, "sharnrajan": true, "shawspring": true, "simplyianm": true, "skandasoft": true, "skyinlayer": true, "sonhuytran": true, "spacemoose": true, "spaintrain": true, "stephenhuh": true, "stormcrows": true, "supercocoa": true, "superlukas": true, "teckrookie": true, "temoto-kun": true, "thetimmaeh": true, "tiggem1993": true, "tiggerhyun": true, "tomasgvivo": true, "tomazzaman": true, "travishorn": true, "twistieman": true, "vanessamao": true, "vapeadores": true, "viklakhani": true, "wang1xiang": true, "wangmeng87": true, "wearevilla": true, "werninator": true, "winjeysong": true, "wuyangwang": true, "wwag110465": true, "xieranmaya": true, "yexiyue666": true, "zaphod1984": true, "zhaotianze": true, "zhiquan_yu": true, "zhouguotao": true, "17727491394": true, "18670232733": true, "Scryptonite": true, "abuddington": true, "adeelquadri": true, "adrienhobbs": true, "aereobarato": true, "ahsanshafiq": true, "alaeddine17": true, "alexey-mish": true, "alexey_detr": true, "andrewtlove": true, "andywhite37": true, "arnoldstoba": true, "ashokramcse": true, "bracketdash": true, "brandonb927": true, "brittanyjat": true, "campallison": true, "codedsignal": true, "codedungeon": true, "codeoutpost": true, "codylindley": true, "cranndarach": true, "crazycatmax": true, "crusaderltd": true, "dandelion-f": true, "darryl.west": true, "diegoperini": true, "diogocapela": true, "elliotchong": true, "eminqasimov": true, "emp.justine": true, "eserozvataf": true, "esperluette": true, "everflowing": true, "fearnbuster": true, "felipeplets": true, "fireofsouth": true, "flumpus-dev": true, "frankwinter": true, "gamingcoder": true, "garenyondem": true, "gauravmehla": true, "ghettovoice": true, "grahamjpark": true, "grantgeorge": true, "he313572052": true, "heavenly-zy": true, "hellooimkat": true, "heyimeugene": true, "hongbo-miao": true, "ilia.ivanov": true, "illuminator": true, "infinitycbs": true, "jamesbedont": true, "jbdoumenjou": true, "jonatasnona": true, "jordanskole": true, "karlbateman": true, "karnavpargi": true, "kieranpotts": true, "kobleistvan": true, "krisbarrett": true, "leighbarnes": true, "lesliewatts": true, "lucifier129": true, "lupomontero": true, "luuhoangnam": true, "m80126colin": true, "malloryerik": true, "marcfiedler": true, "marcovossen": true, "marinear212": true, "maskedcoder": true, "mattattaque": true, "maxaldunate": true, "maxkoryukov": true, "mccoyjordan": true, "mdsiraj1992": true, "mimshwright": true, "minichiello": true, "monsterkodi": true, "naokikimura": true, "neaker15668": true, "nicolaslevy": true, "niftymonkey": true, "nisimjoseph": true, "nitayneeman": true, "nlukyanchuk": true, "omaraljabli": true, "phoenix-xsy": true, "poeticninja": true, "poppowerlb2": true, "postcrafter": true, "powellmedia": true, "princemaple": true, "pvsrivathsa": true, "rakeshalhan": true, "roylewis123": true, "sadmansamee": true, "schwartzman": true, "scottbailey": true, "shiyong.yin": true, "silentcloud": true, "skippednote": true, "stanlindsey": true, "stefspakman": true, "thangakumar": true, "themadjoker": true, "thomasdavis": true, "tonerbarato": true, "totolicious": true, "trquoccuong": true, "tunght13488": true, "tunnckocore": true, "undisclosed": true, "xinwangwang": true, "yanqiangnpm": true, "zh770797477": true, "zhangskills": true, "1silversnow1": true, "abdou2021212": true, "abhijitkalta": true, "adrian110288": true, "alexandermac": true, "andreaspizsa": true, "anshul_sahni": true, "arsalandotme": true, "awareness481": true, "berniemaxima": true, "bhaveshbhati": true, "brandondoran": true, "brentlintner": true, "brentonhouse": true, "buzzalderaan": true, "calin.buzatu": true, "cautiousdeer": true, "chesleybrown": true, "chriskinsman": true, "conantonakos": true, "crazy4groovy": true, "creativeadea": true, "damonoverboe": true, "dandingxiong": true, "danhodkinson": true, "darrentorpey": true, "davidfmiller": true, "davidmurdoch": true, "doc.gunthrop": true, "doppelgunner": true, "dpjayasekara": true, "duartemendes": true, "elliottcable": true, "evanshortiss": true, "feibenrenqwe": true, "gaboesquivel": true, "gilvannfilho": true, "goblindegook": true, "harrison_lin": true, "henriesteves": true, "herrherrmann": true, "hugojosefson": true, "igorsetsfire": true, "infernocloud": true, "ironmaniiith": true, "ivan.marquez": true, "ivangaravito": true, "ivanpeng2015": true, "jabedhasan21": true, "jacobmischka": true, "jakedemonaco": true, "jamesmgreene": true, "johnnybenson": true, "josejaguirre": true, "joshdoescode": true, "jricardoprog": true, "kerimdzhanov": true, "keswanikaran": true, "khaihoangdev": true, "leon.domingo": true, "lepetitcrack": true, "lukaswilkeer": true, "madeofpeople": true, "magicismight": true, "marcrobinson": true, "marcuspoehls": true, "matthewbauer": true, "maxiangjiang": true, "mehranhatami": true, "michaelbazos": true, "mobeicaoyuan": true, "mohanraj_npm": true, "mohsinnadeem": true, "mrhuangyuhui": true, "mswanson1524": true, "mystaticself": true, "nickeltobias": true, "nicolasleger": true, "nitin.borade": true, "norbertparti": true, "npm-packages": true, "okumurakengo": true, "oliverkascha": true, "olsiodobashi": true, "oscarelotero": true, "oussoulessou": true, "paulkolesnyk": true, "pedromclamas": true, "peter__orosz": true, "prabhash1785": true, "pretendentas": true, "qinshixixing": true, "ramkrish2079": true, "rethinkflash": true, "ricardwei195": true, "ristostevcev": true, "santhoshbabu": true, "sfpharmaplus": true, "shanemileham": true, "shekharreddy": true, "spenceralger": true, "thealphanerd": true, "themiddleman": true, "tobiasnickel": true, "toby_reynold": true, "travislinkey": true, "vasiltehanov": true, "victorzimmer": true, "viktorbezdek": true, "vinnyfonseca": true, "vivek.doshi2": true, "walexstevens": true, "warcrydoggie": true, "yanndinendal": true, "yinlubin1989": true, "yourhoneysky": true, "yuvalziegler": true, "zhangdousang": true, "zhenglei1110": true, "zhenguo.zhao": true, "zhuanglunzhi": true, "adamduehansen": true, "amazingandyyy": true, "amit_merchant": true, "andyprocesser": true, "arbazsiddiqui": true, "bhaveshrpatel": true, "chinawolf_wyp": true, "chris14dooley": true, "codyschindler": true, "crazyjingling": true, "daniel_mantei": true, "danielsimonjr": true, "davidhellmann": true, "deadcoder0904": true, "derflatulator": true, "duskalbatross": true, "edwin_estrada": true, "emilien.jegou": true, "everywhere.js": true, "fabiomendonca": true, "ferchoriverar": true, "gamersdelight": true, "giustizieri25": true, "henriquejrech": true, "hibrahimsafak": true, "highlanderkev": true, "huaichao.wang": true, "humantriangle": true, "icandivideby0": true, "internalclass": true, "jasonwang1888": true, "johnlindquist": true, "kevinzhang035": true, "kimmohintikka": true, "kriswallsmith": true, "langdon-holly": true, "leelandmiller": true, "liuhuoliunian": true, "madanbhandari": true, "manojkhannakm": true, "markthethomas": true, "mdedirudianto": true, "miadzadfallah": true, "michellekwa12": true, "michellespice": true, "nackjicholson": true, "nadeemramsing": true, "nikhilkumar80": true, "nonemoticoner": true, "parkerproject": true, "piyushmakhija": true, "professorcoal": true, "program247365": true, "ral.amgstromg": true, "richardcfelix": true, "robinblomberg": true, "sakthiinfotec": true, "shauntrennery": true, "shreyjain1994": true, "shrimpseaweed": true, "stone_breaker": true, "strangemother": true, "teckrookie190": true, "tienthanh8490": true, "xuejiao1.yang": true, "yangxiaoqiang": true, "yann_bertrand": true, "zalando-stups": true, "adriean.khisbe": true, "anacondapython": true, "armanghazaryan": true, "avinashkoyyana": true, "beth_rogers465": true, "brandouellette": true, "chrisdickinson": true, "chrisfrancis27": true, "derickchou0129": true, "dimitriwalters": true, "dmitryscaletta": true, "eirikbirkeland": true, "eliasargandara": true, "evilhackerdude": true, "fabrianibrahim": true, "feliperrsantos": true, "fvcproductions": true, "gordey4doronin": true, "greenbud-seeds": true, "imaginegenesis": true, "jacksongariety": true, "joshua.marquez": true, "karzanosman984": true, "lefthandhacker": true, "leviwheatcroft": true, "lizhiqiang1029": true, "maycon_ribeiro": true, "ms-scoped-user": true, "natashadorfman": true, "patrickkettner": true, "patrickpietens": true, "philliphenslee": true, "raulvictorrosa": true, "ryanthejuggler": true, "season19840122": true, "shanewholloway": true, "siddhardhreddy": true, "stephenhowells": true, "stephensauceda": true, "suryasaripalli": true, "thatwasawkward": true, "thebearingedge": true, "thefriendlydev": true, "thevikingcoder": true, "thewhiterabbit": true, "thiagowittmann": true, "tienda_sexstar": true, "trashgenerator": true, "troels.trvo.dk": true, "allthingssmitty": true, "anatolie_sernii": true, "andrewpmckenzie": true, "andrialexandrou": true, "antoine-richard": true, "arcticicestudio": true, "charlietango592": true, "cyma-soluciones": true, "davidgilbertson": true, "federico-garcia": true, "gestoria-madrid": true, "giacomorebonato": true, "icodeforcookies": true, "joaojunior.mail": true, "krishna_kandula": true, "maglearypereira": true, "mechanicalhuman": true, "michaeljcalkins": true, "morganherlocker": true, "pensierinmusica": true, "rahatarmanahmed": true, "sierrasoftworks": true, "thebespokepixel": true, "urbantumbleweed": true, "vladimirvaivada": true, "andrew.oxenburgh": true, "animustechnology": true, "bluejeansandrain": true, "bursalia-gestion": true, "carlosvillademor": true, "chandramanish645": true, "chenyingxuan1996": true, "gresite_piscinas": true, "knight-of-design": true, "lherediawoodward": true, "maciej.litwiniec": true, "michaeljwilliams": true, "piotrlewandowski": true, "rickyrattlesnake": true, "shashankpallerla": true, "theoryofnekomata": true, "ys_sidson_aidson": true, "alquilerargentina": true, "kjfderierwlksdfsd": true, "nguyenmanhdat2903": true, "omkar.sheral.1989": true, "ricardogobbosouza": true, "szabolcs-szilagyi": true, "miccomservices.out": true, "vision_tecnologica": true, "azulejosmetrosubway": true, "evertonrobertoauler": true, "granhermandadblanca": true, "klap-webdevelopment": true, "programmer.severson": true, "naturallypretentious": true, "nguyenxuantruong.dev": true, "daniel-lewis-bsc-hons": true, "nguyenvanhoang26041994": true, "unsunstoppablegreatness": true, "httpwww.npmjs.comsinsaints76": true, "gael23": true}}