{"_id": "jest-pnp-resolver", "_rev": "9-314c10acfece16ad9417f95a6973c140", "name": "jest-pnp-resolver", "dist-tags": {"latest": "1.2.3"}, "versions": {"0.0.0": {"name": "jest-pnp-resolver", "version": "0.0.0", "_id": "jest-pnp-resolver@0.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-CUaxw37oIAZDWHfUHHAXeErSrfHBfZLvgCauh0lnC7tFiOod6YcYpIaxunZihToUnWTEGNxm4jddZ6aX0M9zjg==", "shasum": "127f70b7c216ffeec7fa53c569817afc29191c1b", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-0.0.0.tgz", "fileCount": 1, "unpackedSize": 47, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmnVyCRA9TVsSAnZWagAACN0P/2HqC+sshbyiZOfWKzaR\nxRHRCwq31DL1SjoNcqFJohwg4TGJI2lhxV7DMlQnHr9UzRVIVDGv1w+L8f5+\nNHWchYCRh+5B4YLoRxBNiFduYVc6Okz83fg+ttyKyUZ1l5Xx4NWNwTSU0upj\nDRn3JWbgwe08XeOjjUvqUg2VJ+AgqfR9/uAZNND6cZX8OqxcuZ8s7jhSV0K3\nqy4lO0nju2OqPmLqZftLTOmg+yaUojWMSkEihRv2Xj/X6BoImDrfOT0u0gXE\nAlyFP2GIYg5Ey1Kat5J/5BBX4Om6RNqv/O+dSMEh+l4GNmFJovD0cNIhUKq6\niBYcOQNBJTOIS8vmzMDd1cpVDFBA54sTKl6r5nllgjEqcsSkb57mh8nlrVCw\nHqQXJaLNeMB2SSwmnxEBzQeMHp11aNZCTjKIuv1L+BlaJQWu8xzmkjQVdtFC\nmaBjXLb1vYn1+HDEnkikIT8OOdphjZGgDE6YJfu9edj14h4xWyV4wyg10DTv\nh0cI+d11EysU17/514y0VQOc43EW8E32kYjBzCrImB/zspkRIkGlpTqXukCo\npC5ASm2T/VNRobWo7XscBriQ2IbNsslRA4jFeCOvDpcM+Mfi+BZAQjJYbFvC\nd5dIhJnusX76CwF9A2NlgykA4iYOHteLRN3ZJkXP/vyrD4Kje0vLT01gS510\nC4pu\r\n=PPBC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+RwtnwdddQNKIgAQ+o9YEjSUeg5JLfILA6BS0D7RdFgIhAI2Qd0MAQvUGiOZONccO7We3/CbVql9Oi8Zx9Atc82Xm"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_0.0.0_1536849265783_0.21143935122217883"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "jest-pnp-resolver", "version": "1.0.0", "description": "plug'n'play resolver for Webpack", "license": "MIT", "engines": {"node": ">6"}, "homepage": "https://github.com/arcanis/jest-pnp-resolver", "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "repository": {"type": "git", "url": "git+https://github.com/arcanis/jest-pnp-resolver.git"}, "keywords": ["jest", "yarn", "plugnplay", "pnp"], "peerDependencies": {"jest-resolve": "*"}, "gitHead": "8522a0a086d1712cb621c6e47e70d0cec1ff356a", "_id": "jest-pnp-resolver@1.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-9dPFkOj3y5k/TEHqVfGjMfulBGFSfNr7IZVAbZ5WtxdG7xvl20jOGLAyz0guSRsEHILQUYI2zdsbwsL71Gpd2A==", "shasum": "e05772e2583f3a388999b5e3d1ba3433ad895102", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.0.0.tgz", "fileCount": 3, "unpackedSize": 3074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbqRxMCRA9TVsSAnZWagAAPJ0P+QDCM5kBAa5uLwC68v3u\nut+tqleRVafUfd4B6PQDfPgYu0lh06oNA7ok9qtnYSCOKbwQ689LZ0mlwH0s\nc2oTVvUGCaMwq0CKeJ4Hx/ZT5hKXrAFKR3tFfhbL7PgJKsjI/1ONeZXSdZCX\nCsRlmmNQqkyGC2RalMZ8ZR+0m9gfgk07k7mecf9V75ClARMQfzcskVMc66dP\n9fC0ky7ogD+bMF6gEN2V9dbIhcHCNoCJvoLOj86OsglCmbQTl4kLhnw9/NnX\nvkzUWBADJNxC0nDm2jttC5s3Oj0Jcqo+ROGGdwMEe7TR31da4j3s1xkw8cmB\n5TGuCblSj2/0U1U/tCFt5L9dg2HPxrbqqcNO8VInLcfrZSEmf0CtB1WzkSG1\nszmDZEH2j/YLuqK2J8fsXAvvpdCnB9G2/wz6K/buLvlmy5/Mjs9wKFEUqWIS\nF8Hf5EqFujncp3Ak1fLnAkzFHMcOO4u2CruKDXVYym0ZslKy5zdR2XIDqCuI\nJpq6X0UGddi4pGV+52vzehC09h5UTBXlvd3d0qbirZEFPsdv3l/QFgeXCgF1\nO3tsC85xT6PMKxwFI8jYtXiyso4LPii8plH37gWAShvfe+JEtNgX18YuNwkM\n147sHs93aWPiEiwNQm9LA44A4Rllj8/7Q7YTL9zZ71OPkRBYBOuBGvwGJ6ga\nL+Ps\r\n=VIAJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEChoRVdz7m9ODPpU1WMy4Qiz05/QpDzRth+xIoW36wpAiBXPx8dSuuaoxSTK5ikVkF9SHQSX7vssesSSSE7CYKeSw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_1.0.0_1537809483814_0.7535739737955425"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "jest-pnp-resolver", "version": "1.0.1", "description": "plug'n'play resolver for Webpack", "license": "MIT", "engines": {"node": ">=6"}, "homepage": "https://github.com/arcanis/jest-pnp-resolver", "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "repository": {"type": "git", "url": "git+https://github.com/arcanis/jest-pnp-resolver.git"}, "keywords": ["jest", "yarn", "plugnplay", "pnp"], "peerDependencies": {"jest-resolve": "*"}, "gitHead": "b8e70841b412447f6b95d6b720cdc9ed260679b1", "_id": "jest-pnp-resolver@1.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kzhvJQp+9k0a/hpvIIzOJgOwfOqmnohdrAMZW2EscH3kxR2VWD7EcPa10cio8EK9V7PcD75bhG1pFnO70zGwSQ==", "shasum": "f397cd71dbcd4a1947b2e435f6da8e9a347308fa", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.0.1.tgz", "fileCount": 3, "unpackedSize": 3075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbqR04CRA9TVsSAnZWagAA/lQP/iTXJEHtjap8Lmf2cNTF\nu5/fIjV9IS3GveCdIYFWs1A/JCr2uFFWcLSrIxCwNi6dXx2Rg/1uqgPGffJ9\nwSrnUvi2wJva2wz8xvOICPN9fyGduA1/l/WJE2fnNJ+JARQ32vi3JtxSp4Xx\nDYqYeXKtLHne2TIl/aqCFbqqwPjY3OtHDXVobSinN3v0h369d1AFHUSGHikV\nRL1G6mWhvB/yEkz6gwttuQ2aGVwOs+7dbqNmoxEBif5n2TQxuZFD9vqfnoXM\nHErUCtwTDspPuFFCR+qTSL3fLH4Jyb1TnSb6NJfazj2HVOJMEqe3R8bHR+hv\nbY86CB8jvMRB+q7BtG5kvOxeSYODzAuWcvRZFe3JTZdQJKHYMVXPloUu+IcF\nBVxrJVwLUI8gt8VsxXxFmeoRnSnODIPIiR/KH2HeeIGs3NStlTBjQCQERsvH\nw/+GLbbBXGsSNN6NGxmAPIABELCV7ZhxUuQsHfO6bmOSAjSA/+iHAWIEhdbI\nQCzzHJsO/4C6FSk+ymtDtixazO2nChFL1OkQYm7EZEn/IJXbwivFhu/PgXa9\ncF1EcclYeCgnA4cn6+I26C809CNcgNMYrAWfmkow3ZfktCBFXsvjeuHxNzoU\ndn/P0BCujZwiTOdOD9V5D6ATWTn8p/GuoXieDCg2pekpHkhLxOdgOlhVW4QR\nnE0C\r\n=J5KC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGAUAcrp8TQZgdm3ed2Vbsq9JdvvC/wfTTfFnElgovYZAiEA4uXwYLwYy5zaeUUioPpn/wsE1c240BnR2Ajyl+N99Po="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_1.0.1_1537809720329_0.2153251862746408"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "jest-pnp-resolver", "version": "1.0.2", "description": "plug'n'play resolver for Webpack", "license": "MIT", "engines": {"node": ">=6"}, "homepage": "https://github.com/arcanis/jest-pnp-resolver", "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "repository": {"type": "git", "url": "https://github.com/arcanis/jest-pnp-resolver.git"}, "keywords": ["jest", "yarn", "plugnplay", "pnp"], "peerDependencies": {"jest-resolve": "*"}, "_id": "jest-pnp-resolver@1.0.2", "dist": {"shasum": "470384ae9ea31f72136db52618aa4010ff23b715", "integrity": "sha512-H2DvUlwdMedNGv4FOliPDnxani6ATWy70xe2eckGJgkLoMaWzRPqpSlc5ShqX0Ltk5OhRQvPQY2LLZPOpgcc7g==", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.0.2.tgz", "fileCount": 4, "unpackedSize": 3373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7Jo+CRA9TVsSAnZWagAA7RYP/A2tIpMiOBN/yfTYpCFa\nLtZJEdkw+HY1n5dRALrmTHc02k8gM7tOenJdOv7H4Fv4BBlUwjrUqxI7tTyw\n4UWb2wPfN/b3H07SpLsD98DbQ6D0F8fGWIrNm9aKvLOBW0QLv0zgdhGbZYQo\nAYfi4PCbIjmSBjKUfcKYb/h8sBTXKl+zTjbBJOfoHMKwGYkvB8BLCPvFTHVq\nUIjW2bBNpsYB60lKKpJ6QTbdxY+OfBWWvoENoyycG3O5VbeQTEumHNZvAZuR\nR/utgjGT/4za4O79rTbU/lA+w7c9TXugYP0XGScuC9i5f28fikVDJw2c6b6g\nCGcQ0zgbFjPW35RLobTyv8novyARe2J/0T3xY7h9hgwTDMzYipxKH1ABSVzI\nMB9eJ8R5Kl5tRV5gwBHqFV0TB4h+IBxYuXfa6fwctZtydbs/Vpj2bqrGdrd3\nQdmoXX2x2xoP6fliLqt2jOLL11Iw1cVJfPQfUJdIzwra/FDaBH8Hbq8XZjE0\n+Uq2Yc5WawA71EhASQjviL1MG3CKzT2WEW23dBEpCy+Pffk0Vivt7YHykLEn\n2sqxPhlmliXT4uSR6TX2QI737VXmqU3f0HG8PTpafoMW5Qc/aN87tw57sc2O\nAfGVKF+CKtl0tWXUDgtehk7fjVC6MDUHmGOh6dc11CCKNTnRzqSCjYs9sU5t\nfhhr\r\n=sd2S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDG+kz6NgRCkMqVcGsj4WZ8gHCDK6m+dE2ynIIE3eDktwIhAPl1086bO9vtA+DK5eFGHLN+H2/wo0vaXB8IO3LukkT1"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_1.0.2_1542232637826_0.8989748511388229"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "jest-pnp-resolver", "version": "1.1.0", "description": "plug'n'play resolver for Webpack", "license": "MIT", "engines": {"node": ">=6"}, "homepage": "https://github.com/arcanis/jest-pnp-resolver", "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "repository": {"type": "git", "url": "https://github.com/arcanis/jest-pnp-resolver.git"}, "keywords": ["jest", "yarn", "plugnplay", "pnp"], "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}, "_id": "jest-pnp-resolver@1.1.0", "dist": {"shasum": "b6e2eb14f6ceddfd1cde40119be67ddae1738c2b", "integrity": "sha512-/tuTb5um2nQvybL7Scxy6oJF5cbWWawugurVIMnnXhBSlUjNEwKpPeaYT7K/f/EZupmrKfiwQ95f3J7kShAZEA==", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.1.0.tgz", "fileCount": 4, "unpackedSize": 3691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWfkoCRA9TVsSAnZWagAALxYQAJ+12dD3/5qnMyvsi9YW\n8dY5azYX16TXQiPBCJ65g1ccnUOG0k2Wcyv7+Z2E5xCfTq/gp42EunBuuXQo\nQwHETyY9zQSmQWgHyPE6g0HVxhqrsoHqeKzJ8MUjCdSrhXTKfhUPmCwLj8i7\nNhHiaHGOWbc33t0+3O0O4PeEbawuBJK9wDcdD2S2TrdPu8C5Gh7hZ7a5TTd/\nSPUtXdK5BWGvXimMHnBW3k1mSrjIXYoBOW+8CNKhQUSrc79ijBplKcEbTjhs\nuYjFo7GPVic5swkwww/T8D24WFUhzJawM9sXtVB5OG3Oij1nbIcZ97ORaPl5\nTpS3Dsg0Vjyc5Y18pHldoL+Pxwq25uwNUnqL2LiN2yNAVJp/znVrBhIW/j1W\n0eZxjXfl33c9hoYK3mri+3AsvpHlVke991F9qjwFcfmDm+xyzY44mDLwrzm2\n2DC0JmeVcMnEhJCd1ghpYaTH2ja3DOu3ND7FZx1GTsvIxbvfL4mkorezABeV\nWXJ/xxvoDIPYi3uJEfkHHw0/XfdktsfeX4LWisIjMGA/+laUr+JBDrRqzF3B\nVg+nppx0+duJSoysYodzurBmO35qPnyEnm9r2Qx1tiWtf4uzUbu8hfZw/hiU\n7JnF+NkiNxAzSDCiaDwp6xD9U/2FkOGVpjA11Ltt6/cjjioscrojhaBicCvp\n2h+c\r\n=CANU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtB85LEsdb7jtfadKzu8u/0U2I3ryi3aHO0++kCUrNMwIgGHNeIeGmZwVdUcxnfMJIuNaKw+wyuvWwO8C0kpRltXQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_1.1.0_1549400360316_0.7941348678199536"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "jest-pnp-resolver", "version": "1.2.0", "description": "plug'n'play resolver for Webpack", "license": "MIT", "engines": {"node": ">=6"}, "homepage": "https://github.com/arcanis/jest-pnp-resolver", "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "repository": {"type": "git", "url": "https://github.com/arcanis/jest-pnp-resolver.git"}, "keywords": ["jest", "yarn", "plugnplay", "pnp"], "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}, "_id": "jest-pnp-resolver@1.2.0", "dist": {"shasum": "3e378643176fda5999efe18b61f5221dfe65fe3f", "integrity": "sha512-ol/Eg87R5D1jQ13e0anFV2B6i4AEFyBnY8XQnKVPSxpVBRtGVv8OYaZSbhYRu7bRdFJXj4dDaSSgEoZUj81OYA==", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.0.tgz", "fileCount": 4, "unpackedSize": 3691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccD2ZCRA9TVsSAnZWagAADlEP/1kT5ggmF3pu6uLaODet\n9m1C/BQppfdc4Wl8PShGwcTch9fhx2M3eNeWSFrrRjnHDmQDTcUNhdumaaJr\nq4TTFOzrM+hcKA50ED3FGnJa0USiWp84X2HDlrfgYSyOJEnWe+u/9qDPAG79\nMuL/HjKKRo82ijrXG3GKCFLMShYiQsTdMvPpyK42UVFcuSk8FgAoLBCW3pLW\nekOvVbXY4zMuvWbuL5nkxclwKbAN5fh17fntczek/bodL6i0PBlLiTPD8jL5\nXlPbJYD9QAvu3xm9J2l2l2ayQq4CwOEM7BKcrqiwajmDfArwhY4k2VIeR14Z\nBvmW7bVPuUXh96dKjr+d6fgNmVi7GnDWEdfIKOl19uIl66apc03hm3uou2Ht\npuAjfZiSm0/Y2nYshHxFeafkOSbTI2Hi8K/CDrMG/7xdaEspAOm/UtSiFbkQ\nM91EhYPHOZyZQUq2irkjcZbXHlFEHBtDwprgswxOkd5874mg3h5iys2qOluY\nRVNQnu00arvNmGL0HVrxN9OeZIv1kU1SWR1ltIizEy3xMY+lRp9Hrq0wUS1r\nOOt62J1wKvnC+q+tV3KPKPohri/umTyCdt8vwDod1JnluCyyZ9KnNKYltAsd\n77I21QWP/jt/jgNVknIU1rYzgahQPdwEhbotPCjhRbHd0O6nfVZrEtGw0F4v\nGKzz\r\n=DkNJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXZiBJ5s9Ge1SjACvItfCgxnehjh2w9S4X689BxQN/EQIhALf3LBuInC3nlSObVVFICX4Ra1Mez77Ho4rGglkkrPc/"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_1.2.0_1550859672543_0.4431223382956655"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "jest-pnp-resolver", "version": "1.2.1", "description": "plug'n'play resolver for Webpack", "license": "MIT", "engines": {"node": ">=6"}, "homepage": "https://github.com/arcanis/jest-pnp-resolver", "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "repository": {"type": "git", "url": "https://github.com/arcanis/jest-pnp-resolver.git"}, "keywords": ["jest", "yarn", "plugnplay", "pnp"], "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}, "_id": "jest-pnp-resolver@1.2.1", "dist": {"shasum": "ecdae604c077a7fbc70defb6d517c3c1c898923a", "integrity": "sha512-pgFw2tm54fzgYvc/OHrnysABEObZCUNFnhjoRjaVOCN8NYc032/gVjPaHD4Aq6ApkSieWtfKAFQtmDKAmhupnQ==", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.1.tgz", "fileCount": 5, "unpackedSize": 3927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcg741CRA9TVsSAnZWagAAXssQAJdhTrchk44HsHMNdch+\nI75+VWQFYVUktB9+2fh8LbovofZz0aVFZLxxE91K1h26La+AM4tQIYDOPlcr\nIdtgP9DNngyawzOn0fD/guLScIOt/eRWT3EHZlE2e95mI1UiKz4e4BbMiaTq\nLXK5doOcn8Mcqk2gyHmWS5Z93wjCuXn/nTvEgOyqvJnUb49YoPFM4pUaudOT\n9c5BQ6u5dxnn/MviAfUWiIiab0UvCj+dnYKbCGaEjYq+rj4OlCSPd+OOpffA\nRpzlVIlfkN1l3liYNDazTfzWkO0/nlJmoBz/JVtOadQxuw5xtu2suUj7hZiA\n8Dj7Zyg67ulD2zVPls+58J6jY+IGrX0TIzlwReLcvCmOEOPGzcgVvrljONc6\nNcuCExwbWSvmC/WuFeHRP/oHjqzacOQSFkJPQPjngqDvemGQ8Ny8MkO4lw+m\nIrUWziKgSOPHYiXT+0+6Ng5l1dhvI8gbtR9V5hDojTlwDM1iL2dS8vcfzLhx\nmQX+N/giCjY22qToycl4PX3fI3U9jNaLXINjoFrl8956EJA+ZLpaNvIZsop3\nsR/o/Mjk04Qqx6j6iyCGQILUy0/SRYTUJnoSVc4R7YkHNZfZPt4KO7RnhgS6\nQi2nwqSr0Mr8dSSOSZ0Sxa2fTnUNFZF6QR/yIJBW43mcODbM4d4fFjAUm3PR\nTnmQ\r\n=wgtD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoAYgqUuHzTzVkUq1lxA7V3foR6LzndOqGB4jBCnoVJAIhAIMLWzn+WAIgFptyPbc52qgF5sRIOfCsQUdK+NaJPBqq"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_1.2.1_1552137780603_0.8715833878473485"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "jest-pnp-resolver", "version": "1.2.2", "description": "plug'n'play resolver for Webpack", "license": "MIT", "engines": {"node": ">=6"}, "homepage": "https://github.com/arcanis/jest-pnp-resolver", "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "repository": {"type": "git", "url": "https://github.com/arcanis/jest-pnp-resolver.git"}, "keywords": ["jest", "yarn", "plugnplay", "pnp"], "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}, "_id": "jest-pnp-resolver@1.2.2", "dist": {"shasum": "b704ac0ae028a89108a4d040b3f919dfddc8e33c", "integrity": "sha512-olV41bKSMm8BdnuMsewT4jqlZ8+3TCARAXjZGT9jcoSnrfUnRCqnMoF9XEeoWjbzObpqF9dRhHQj0Xb9QdF6/w==", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.2.tgz", "fileCount": 7, "unpackedSize": 5705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8yW7CRA9TVsSAnZWagAAOzYP/3ohxKrpYfv7LfqApK+a\n0x5GnXA8K1tdGcQHMvwB2cfo5WscWXbsghW7T9CXSJGvcfUXsTridrEeaDNY\nU2g0z3J/ZTC2BGaKMmV96wdVGkmYdIAFhKSL4iQSQpnxCVVD6zx+eQeklMx+\n0xGtimuLUYTpRD8gUa5W7WY7RBIIRuN/5XzPSZg8zkPmTKs27Nn6YyOOpLJn\nT98L1VWSkweSXaLkIjZKakQGF9JGchxlJqeP3OqbyEMRd5YNc2Tddr1O9xFl\nPBQa/UraMD8NecdqQMeA4l3SXVPJ8A8RcsRzu5b78AqCN500CVzDXxjIwTa/\nATsObJs3ci8Cm0bHYapr26EpIRUY97dCoZKq1/hTRyDphCYS3R+s9rqX/WNO\n5OrQYWroy6QLD9X953OZhbKh4YCJScFF3FzZteV/Qe1G3XmXTUHaZHRofUqa\n+5Rg2xPDuN/93CxKrnqdEEpR1PHrsHyEh52BkDK4M6ekk5r1e8PLcHikxLPS\n/fZQQNiRd5io33GZ/ScixoWfsMJYMEn7blZWe0nRw05HgNcM7Jei26bzgNC9\nBE0o41AtxC518PZvPnCY6FBlbGIvYvt8DlpQolSdOLYqK5SjXp1Yg+wZMtgN\nKW8X+gJA2vJzljjBX0Xom0HmKiVN+PDi9NbV59b5aenA3vtyseEzq8hydktx\n03lo\r\n=WD+S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmB/ByH0olVRba/s7njE4L9UV1ZdrAr8S+mm2kPoDemAiEAjoruKSOz9NydoSo/RHRspuuzFxSTnv51JlKaWVWV4iU="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_1.2.2_1592993211202_0.5830860593507845"}, "_hasShrinkwrap": false}, "1.2.3": {"name": "jest-pnp-resolver", "version": "1.2.3", "description": "plug'n'play resolver for Webpack", "license": "MIT", "engines": {"node": ">=6"}, "homepage": "https://github.com/arcanis/jest-pnp-resolver", "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "repository": {"type": "git", "url": "https://github.com/arcanis/jest-pnp-resolver.git"}, "keywords": ["jest", "yarn", "plugnplay", "pnp"], "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}, "_id": "jest-pnp-resolver@1.2.3", "dist": {"shasum": "930b1546164d4ad5937d5540e711d4d38d4cad2e", "integrity": "sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==", "tarball": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "fileCount": 7, "unpackedSize": 5676, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmErbLrkJPwixISL4sbfYJI/duiciluO2p8ha0ZOOpDAiEAzWWJFqjuBM83M4F3fB2EbCKr/TXLu5xEcTWB1UTc7A8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc2DEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoX9hAAmPPum4924svQoaH4H9HUL/XfWJFCJ+9QZAOLv7l+QDBbdMW7\r\nV4bT1PvcsHwQtf3IZ+lqLc5ZQkV0/oTMx7pu2NUFbnVrbY3xb6yfuiro+3Lg\r\nLF9ACUc0Dh84LqdHXEMMdf2paDu1j/wqhm3HKazrP4VSMUQmKxEmHAvTe8xw\r\nC17u8KcUPHkrWERLM+QBoRL/wajSYsacVrGJOArL71Cb+/63G7/5aMDOZwVv\r\ngEe6d7KROnevMLToZ8BGnblMThgkQbJkKHZyG9sHGeAkphLYcUqAslBEZ8w1\r\nyLkEsCKJJGe/+CWP6Qldx3xWYyPoKnnbYl9/cBPPJlCxiQ9wsdae0aoZ5FJ+\r\nhHyGa6dx1gL0kHJkLExwVJmTWya0ZhFDfm1XQ5jlAquc9/Pae+UpwRSnt8Dc\r\nTWPfU1WFD0v27ngnG/a9VIIYYjzSndafI1ReaBbh9fovmDQJwI2IWQ1w/9aj\r\nuuTMaSGBNUUc3/cZ4P2bHeKus+JYgm1nEV6vijS91w5D6re7fw/BuBJTiWWk\r\nWXzRsG8E+H36AQPQ2/GUP8E0NJRtakvSLJ6Pc8hw1TL48eah4E9RzT1YqZhS\r\nCZ6RH6YDpMuXOprL3VcAtSf8r7bMoi0IjzaeslrdAc6S/FtDKJxhrda0dEFN\r\nGF542mX6ZWZg+I2BPEAwy82Cbrtk0wbMg4I=\r\n=DUAE\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/jest-pnp-resolver_1.2.3_1668505796507_0.2971541770683124"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-09-13T14:34:25.782Z", "0.0.0": "2018-09-13T14:34:25.877Z", "modified": "2022-11-15T09:49:56.722Z", "1.0.0": "2018-09-24T17:18:03.997Z", "1.0.1": "2018-09-24T17:22:00.473Z", "1.0.2": "2018-11-14T21:57:17.971Z", "1.1.0": "2019-02-05T20:59:20.433Z", "1.2.0": "2019-02-22T18:21:12.717Z", "1.2.1": "2019-03-09T13:23:00.949Z", "1.2.2": "2020-06-24T10:06:51.305Z", "1.2.3": "2022-11-15T09:49:56.652Z"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# <img src=\"https://github.com/facebook/jest/blob/master/website/static/img/jest.png\" height=\"40\" align=\"right\" /> [Plug'n'Play](https://github.com/yarnpkg/rfcs/pull/101) resolver for Jest\n\n[![npm version](https://img.shields.io/npm/v/jest-pnp-resolver.svg)](https://www.npmjs.com/package/jest-pnp-resolver)\n[![node version](https://img.shields.io/node/v/jest-pnp-resolver.svg)](https://www.npmjs.com/package/jest-pnp-resolver)\n\n*This plugin is also available for Rollup ([rollup-plugin-pnp-resolve](https://github.com/arcanis/rollup-plugin-pnp-resolve)), TypeScript ([ts-pnp](https://github.com/arcanis/ts-pnp)), and Webpack ([pnp-webpack-plugin](https://github.com/arcanis/pnp-webpack-plugin))*\n\n## Installation\n\n```\nyarn add -D jest-pnp-resolver\n```\n\n## Usage\n\nAs of `jest@^24.4.0` you don't need to manually add this package anymore. The default resolver will already use PnP.\n\nSimply add the resolver to your configuration. For example, a minimal `jest.config.js` would be as such:\n\n```js\nmodule.exports = {\n  resolver: require.resolve(`jest-pnp-resolver`)\n};\n```\n\n## License (MIT)\n\n> **Copyright © 2016 Maël Nison**\n>\n> Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n>\n> The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n>\n> THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "description": "plug'n'play resolver for Webpack", "homepage": "https://github.com/arcanis/jest-pnp-resolver", "keywords": ["jest", "yarn", "plugnplay", "pnp"], "repository": {"type": "git", "url": "https://github.com/arcanis/jest-pnp-resolver.git"}, "bugs": {"url": "https://github.com/arcanis/jest-pnp-resolver/issues"}, "license": "MIT"}