{"_id": "path-exists", "_rev": "25-cf4fe15fd3f8a84ce4e73f37e13b6c4e", "name": "path-exists", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "path-exists", "version": "1.0.0", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-exists@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-exists", "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "dist": {"shasum": "d5a8998eb71ef37a74c34eb0d9eba6e878eea081", "tarball": "https://registry.npmjs.org/path-exists/-/path-exists-1.0.0.tgz", "integrity": "sha512-BD2vrQBPFI3VkVKzTrOmaG2WtPQoduNXu1A5tLYMOW8RN6G9CdhdSkmw+ljxUkJcj4pbXQGw0lzl7MFLnhba9Q==", "signatures": [{"sig": "MEYCIQCv6G2BpKXYr2NQMr4cfGPxhp1ICj80QHVBFsHXYwet1QIhAL5EmAeawBlOcHt+ZDpgBlVlpPYeSvTQ7Ip+Rq210sCt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "d5a8998eb71ef37a74c34eb0d9eba6e878eea081", "engines": {"node": ">=0.10.0"}, "gitHead": "be563ef9f750ec4fcbd5cc77603c869bdee05658", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/path-exists", "type": "git"}, "_npmVersion": "2.7.4", "description": "Check if a path exists", "directories": {}, "_nodeVersion": "0.12.2", "devDependencies": {"ava": "0.0.4"}}, "2.0.0": {"name": "path-exists", "version": "2.0.0", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-exists@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-exists", "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "dist": {"shasum": "c4efe37d7fdc792f9a029ce7906e095e169f9be1", "tarball": "https://registry.npmjs.org/path-exists/-/path-exists-2.0.0.tgz", "integrity": "sha512-GU3PONGaZm/z8CVavCLMDzF+esUrTBNn6ZI+AsVIvAPsH1o1eE+ispDfS72gAwrEA/P8FFSADD8df79yDJDR2w==", "signatures": [{"sig": "MEUCIBE5ykFiz3J4cD3w/KOCwPmoj/1CA1QlEHkGdPJKj9b3AiEA1tjtnQgrJr9G010r81SPrSWNs3ZPqc7X561Ah2hE4A8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "c4efe37d7fdc792f9a029ce7906e095e169f9be1", "engines": {"node": ">=0.10.0"}, "gitHead": "a8d30de3a24e7d71ae2c30a057525e603205447d", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/path-exists", "type": "git"}, "_npmVersion": "2.11.3", "description": "Check if a path exists", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"pinkie-promise": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}}, "2.1.0": {"name": "path-exists", "version": "2.1.0", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-exists@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-exists", "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "dist": {"shasum": "0feb6c64f0fc518d9a754dd5efb62c7022761f4b", "tarball": "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz", "integrity": "sha512-yTltuKuhtNeFJKa1PiRzfLAU5182q1y4Eb4XCJ3PBqyzEDkAZRzBrKKBct682ls9reBVHf9udYLN5Nd+K1B9BQ==", "signatures": [{"sig": "MEUCIH2B+717/VJnuQLlyKUbAvUuBoQaXZJkmLVUW4UwfMckAiEAp7imnDsnaRO1vcYEjFIakgaA86rKblhPxyfpJhnx4DM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "0feb6c64f0fc518d9a754dd5efb62c7022761f4b", "engines": {"node": ">=0.10.0"}, "gitHead": "3af423661e78466764f87c5712263c5d7a1ce5b7", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/path-exists", "type": "git"}, "_npmVersion": "2.14.7", "description": "Check if a path exists", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"pinkie-promise": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}}, "3.0.0": {"name": "path-exists", "version": "3.0.0", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-exists@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-exists#readme", "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "ce0ebeaa5f78cb18925ea7d810d7b59b010fd515", "tarball": "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==", "signatures": [{"sig": "MEUCICwpigSb9cfgfaj2KgEwwOrXcBVxFghPHHv52OYy5OMgAiEA8jMv28QKZxxhjfMD9ymZG87RblCMel27cukIRGWhhb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "ce0ebeaa5f78cb18925ea7d810d7b59b010fd515", "engines": {"node": ">=4"}, "gitHead": "4696c60a8b2b9ac61902aa9eab7cb326ab6005c8", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/path-exists.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Check if a path exists", "directories": {}, "_nodeVersion": "4.4.2", "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/path-exists-3.0.0.tgz_1462103091696_0.5805528531782329", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.0": {"name": "path-exists", "version": "4.0.0", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-exists@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-exists#readme", "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "dist": {"shasum": "513bdbe2d3b95d7762e8c1137efa195c6c61b5b3", "tarball": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "signatures": [{"sig": "MEUCIQDyfLbncajlWbMDIPi5XGJseXcr9CBh1zovKHHQXBhJGQIgQ9BTBfVebUAF7DtKGmDzoJ0PTS1AWbuzQtjQKkFSdu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpXoNCRA9TVsSAnZWagAAnosQAI8XSu8kmOriX6qXOsXw\nVCUJHDFHO4M5dLJdrT1o5o0SDkTQUmOSazUR/CvaA/4pE7rtPjOL58CrHqSI\nKfEF3lTitx+L3kEw3br6bPMVTo+JoSH04L+UXq7YdkfwRigrNmZpjMiIychR\ngxcgTzBfsFBYzRRUia8OUAFkIK9kvzpRorfFSMfzGGB9V+vjbMd2V+OM0N+G\nAc48nmGUsrQKknAiJpkixM5PWMd/51kV5SzO13RLCnwNOYnH/vNij/9oseSt\njO2nkixc7PDMZTD/2kOQoRQpYoKju/gUnFXCynw1A4EHxetv6QB4nbIVLt8t\nyGhu+hrhNlTgP4MBuSddj4iqJ2EY6VpVZzUNNSzI7SzF62AWTZRHoNLG80yM\naSDKS9Vu+9lkT+YOTA1dXFMol2a29Lzo6Q1PAj2vHd++SZ5A0QO5iRVQkocu\ncecYKaeP2XCa6iWaPwAfEN2rJRdnKN3LrKKr1Cbttjd9WI3Kr152uNXYLjw3\nQ4KpUo4B9rNSTArMk7AIJ1/71uDxvCB+T0OYkj+uHTEKAB/wllpUcJXE2V4Q\nnRig/oWco1+oo1qBY0b2OgiiUcTEfuYUDEyecojVV+nmDDzC/GmcO/93hFnL\nEFxqhB1yaFHuaK4+ntJRpFg+fry09I8mu145WqhggK3le4DUTyyrESAjcBOs\n6s47\r\n=Jirh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "26b1adf80aa6761a7db35927a20cf59e0fd1a00d", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/path-exists.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Check if a path exists", "directories": {}, "_nodeVersion": "8.15.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/path-exists_4.0.0_1554348556736_0.4131618583175338", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "path-exists", "version": "5.0.0", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "path-exists@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/path-exists#readme", "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "dist": {"shasum": "a6aad9489200b21fab31e49cf09277e5116fb9e7", "tarball": "https://registry.npmjs.org/path-exists/-/path-exists-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-RjhtfwJOxzcFmNOi6ltcbcu4Iu+FL3zEj83dk4kAS+fVpTxXLO1b38RvJgT/0QwvV/L3aY9TAnyv0EOqW4GoMQ==", "signatures": [{"sig": "MEUCIQDd/DvIroPdagiu9k3aKlqYCRidYa/NcD/88ji7RgAYOgIgFarrMcdvJmaON61MGntHQnSL6xvQkpY6pWiZLi4Ywro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFPtHCRA9TVsSAnZWagAA8f4QAIVfOc9y52rfjkwQIHby\nluF+gVwfmS1yR6w7W0Z9Pvutg43I7lED28Z9R0Wlkawdz6zRIJGD5s3Aol0o\n8d4Pnb47xbsiON+cklgW6XA3cz3BUTyxQ0/2bfaQJ06pVX51esDsAyCrW5aa\nBKWBcA5HX9gHlxO9i9fGAUvxI7wdB6AxHbuiB4cM0GFlIcyvFd5XaKbQ/9kK\n1uyQu1Oq3vO0nu3szNDhzQ37paGk1fT/HIZ3CXcQkZAUxYuocITweU3jh9zZ\njZjxt4CZ8BmWHOxjjNG0v+U74JC6hfP/+uoeoSXFIpFLxd8MpycFu3iFgcRh\nisBM7phsDl8WpFby1pAa+vU4Q7irQKqVEUPtfIxdnuvDBOnObd08zKKxSK5j\nKE+8XJH85K0IyoOVTx8wwWt6KC2Jb9kgYwJ4+33j7kV3YAJRQNaMuM3cCGcj\nDmK0f0jpOqgqjnVWXCwL+4cta7j4zcne/uXtSCARL21fn29uuBlcCn8jjslc\nHXPoEHxNwolT5PVCknK5LTwCjeU4P6Scl8u2yb5//is8FFifLv12iv2wX5ED\nfDR4/QnC+5RTiqo3cnlxeMWd2Bia18J2aj3ujzRoDfWvpk7ahj15zuJC1WSC\nPNvDnqvoVshVAMFXTkb+85Ct959OVF6aRlExGBX6MgsqXDTW3wCOwmjZoHJD\n82J3\r\n=KfMy\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "gitHead": "7c95f5c1f5f811c7f4dac78ab5b9e258491f03af", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/path-exists.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Check if a path exists", "directories": {}, "_nodeVersion": "16.2.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/path-exists_5.0.0_1628764999208_0.25633467024338685", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2015-05-02T22:43:43.950Z", "modified": "2024-10-22T17:26:44.383Z", "1.0.0": "2015-05-02T22:43:43.950Z", "2.0.0": "2015-09-01T07:09:33.519Z", "2.1.0": "2015-11-14T13:55:22.456Z", "3.0.0": "2016-05-01T11:44:53.762Z", "4.0.0": "2019-04-04T03:29:16.887Z", "5.0.0": "2021-08-12T10:43:19.346Z"}, "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/path-exists#readme", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "repository": {"url": "git+https://github.com/sindresorhus/path-exists.git", "type": "git"}, "description": "Check if a path exists", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# path-exists\n\n> Check if a path exists\n\nNOTE: `fs.existsSync` has been un-deprecated in Node.js since 6.8.0. If you only need to check synchronously, this module is not needed.\n\nNever use this before handling a file though:\n\n> In particular, checking if a file exists before opening it is an anti-pattern that leaves you vulnerable to race conditions: another process may remove the file between the calls to `fs.exists()` and `fs.open()`. Just open the file and handle the error when it's not there.\n\n## Install\n\n```\n$ npm install path-exists\n```\n\n## Usage\n\n```js\n// foo.js\nimport {pathExists} from 'path-exists';\n\nconsole.log(await pathExists('foo.js'));\n//=> true\n```\n\n## API\n\n### pathExists(path)\n\nReturns a `Promise<boolean>` of whether the path exists.\n\n### pathExistsSync(path)\n\nReturns a `boolean` of whether the path exists.\n\n## Related\n\n- [path-exists-cli](https://github.com/sindresorhus/path-exists-cli) - CLI for this module\n- [path-type](https://github.com/sindresorhus/path-type) - Check if a path exists and whether it's a file, directory, or symlink\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-path-exists?utm_source=npm-path-exists&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "readmeFilename": "readme.md", "users": {"ash": true, "timdp": true, "banzeh": true, "hyteer": true, "monjer": true, "kiinlam": true, "antixrist": true, "rocket0191": true, "deneboulton": true, "flumpus-dev": true, "serge-nikitin": true, "coinoperatedgoi": true, "ognjen.jevremovic": true, "recursion_excursion": true}}