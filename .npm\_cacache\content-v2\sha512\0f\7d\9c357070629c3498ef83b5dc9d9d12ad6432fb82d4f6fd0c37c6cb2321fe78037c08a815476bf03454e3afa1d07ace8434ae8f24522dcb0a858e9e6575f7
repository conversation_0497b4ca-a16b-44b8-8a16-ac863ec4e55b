{"_id": "gens<PERSON>", "_rev": "10-b7037c19f6bf0fbaa650b7540360ae68", "name": "gens<PERSON>", "description": "Allows users to use generators in order to write common functions that can be both sync or async.", "dist-tags": {"latest": "1.0.0-beta.2"}, "versions": {"0.1.0": {"name": "gens<PERSON>", "version": "0.1.0", "description": "Async-Await for the modern Node", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "engines": {"node": ">=5.0.0"}, "author": {"name": "oakfang"}, "license": "ISC", "_id": "gensync@0.1.0", "_shasum": "b0fdff1be1f3c3b786560c9dadfc1edb7e1f0e8c", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "oakfang", "email": "<EMAIL>"}, "dist": {"shasum": "b0fdff1be1f3c3b786560c9dadfc1edb7e1f0e8c", "tarball": "https://registry.npmjs.org/gensync/-/gensync-0.1.0.tgz", "integrity": "sha512-ehuyl9H0/MopqcEmyb57FbIPQIgZBCkaoiySftRgrdnZhlzls9+Ga0Ovgi+MnAHI4QKKrq/+jfTDXSGZBNLulA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEhyLHOQGREIE/Lp+CJypWSGkqCDsQMeiUUkRP+6NXhpAiBNraLaapZsiXIRC0+NeIs+Su5Sr1qkYGVTmAiy9Osglw=="}]}, "maintainers": [{"name": "oakfang", "email": "<EMAIL>"}], "directories": {}}, "1.0.0-beta.0": {"name": "gens<PERSON>", "version": "1.0.0-beta.0", "license": "MIT", "description": "Allows users to use generators in order to write common functions that can be both sync or async.", "main": "index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"test": "jest"}, "engines": {"node": ">=6.9.0"}, "keywords": ["async", "sync", "generators", "async-await", "callbacks"], "devDependencies": {"babel-core": "^6.26.3", "babel-preset-env": "^1.6.1", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^2.6.0", "flow-bin": "^0.71.0", "jest": "^22.4.3", "prettier": "^1.12.1"}, "gitHead": "1667d4e272366eae82dcad1d7c3538be43558dc6", "_id": "gensync@1.0.0-beta.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wPUFeh6xV6+yjPqqj9baE3WWSwFMm0/LcouMNNDyRKIt9CHVWKCkHNXRiMD3PncE0IXKSrxTJheo0yvyb4ATFw==", "shasum": "da411523d08341b10f9f3ccd4d9aeece2d1c104b", "tarball": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.0.tgz", "fileCount": 7, "unpackedSize": 27994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa75udCRA9TVsSAnZWagAAyGcQAIcNJjivTg2SUGDmqw3U\nby3Ba6otnmVUNDYd7pZ3NmEXxNNzUkc1yFPGvRhrm3dWC2DE4nPxnJkv9jCK\nvps5mowjtzvGg2SIKctCS0iGM5yeS4EZs0uIj+HGl8X8UWpMoHXltA0fedJk\nsjCB3Mq8mexfa0eYLDIRJx+RvA9fKzUcQQ59QcCdzuJFDVeBAPTA8mauB2ps\nfplE7/a1IiYI0NjTLXWmfJ1+dUu/EBZnR4WvJmEHZZD5Uazs7hhhkwgw17Qp\nXZ/5gvnsWS20pjy66g/sGxQqaBtKC5hkmnob05YkDMSUbswrm5GeRExUT7v7\nUjWAheWheXSN6p8vyNMZ5zdKFPJ4++2JGW7ANEtsFIH7gJHSZR83/pu7i9kk\nukiAQwumQ2pjTniQM/nxLMwcVt9JkjUT4F1wtfMDaySp1kUtp87YshhVNdRl\nm7s+SK01xbQQ9IQGcIakGzYz47nE0CR33cByQFwy6XcUzLm9vj9r9zAeQoWV\nidjHxkpXjYesSVkg/1AfXsM3hLotaQKmMh85xqaDsYZJ7Bosi8dwlBbQSQWZ\nxAqTRtBpB0AAyUEviJcvSzE6NNYzGVlC23D2z9Bv5OSZDVquEbDkvdChjnPv\nRp56RPdoEz7gqrPSsxUO2QsHvudkNBwP/Q2LEK4nEXund8+9sc4Exu5tjL0W\n8JQK\r\n=p8bJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA9DARM3WAQHhbewyYCKSlDIBiENEZkvRneWshgEIAX6AiAxZPbCUSrFbYi9GA7RE5u+cqfvdMfToDMhdeWF6aD6iw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/gensync_1.0.0-beta.0_1525652379482_0.7643044940139059"}, "_hasShrinkwrap": false}, "1.0.0-beta.1": {"name": "gens<PERSON>", "version": "1.0.0-beta.1", "license": "MIT", "description": "Allows users to use generators in order to write common functions that can be both sync or async.", "main": "index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"test": "jest"}, "engines": {"node": ">=6.9.0"}, "keywords": ["async", "sync", "generators", "async-await", "callbacks"], "devDependencies": {"babel-core": "^6.26.3", "babel-preset-env": "^1.6.1", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^2.6.0", "flow-bin": "^0.71.0", "jest": "^22.4.3", "prettier": "^1.12.1"}, "gitHead": "6846e54e4afb9ecd7e055cd0259e5dfcc65ff4cb", "_id": "gensync@1.0.0-beta.1", "_nodeVersion": "12.11.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-r8EC6NO1sngH/zdD9fiRDLdcgnbayXah+mLgManTaIZJqEC1MZstmnox8KpnI2/fxQwrp5OpCOYWLp4rBl4Jcg==", "shasum": "58f4361ff987e5ff6e1e7a210827aa371eaac269", "tarball": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.1.tgz", "fileCount": 7, "unpackedSize": 28256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkCMnCRA9TVsSAnZWagAAeqsP/2I4Jm8sLZcBIE7DMpg3\nSSXzW+yzGL8uIl/KC29kzA2EgvQcar3V7kW4tm2cRq68xjNPodIqG8XRvCra\n+GH2oqMzpdaFdfVlz/XOAoGs4tShcsUODO9fL28qy9BMJ0kzHaUzKeWao7Y8\n7CKtUIm5zIHSISLIDKmtTcA23O3eNKsK74VoGYM7D9ZjRSGDw9AgDskk+77R\nDVtEFahkkTMdDeusA2FkSkFTkpMdZe35q/n17XKk312DqiSAv1rRWS4zEV8z\nAanoqCvCxVDpufz1mO1FRxc3Oz+V6u3R18rLH93jFHMyP4uG+o9nFC3rDmjr\nc54w4jgioilMtOTidm5LQAajRgBPULIZWgnxhXBZmysUJIPaCEMe/en7xXkn\nme4c7SF9tiKQJdHfT4RfqucXNz3f0l88oYBSG+v8h6KhbYBHjsknU5izbCdJ\naPNFSWv3mDgnvMLVK48+0skA9n3SKT7SUpGyj6Rxc/YHgGvUNj5D9v4FGxsw\nQ948SwNMew5IhnlcGKn4WRcuG6HWeyXc1JVbmtEO7zrWhKqRflZQyegU7Vy2\nLtzmhx5Nxq4edWzWAp9aeqg4AAEiI6i09phdpXrWsf4f77GxrKCnkH/gnJ4O\nvoHcUr7fyPGNNMHcTH49CUe3AHBNusY6X/xMw0zXQ8PoDAXHd5WO/Lp5h11b\nLhAN\r\n=vaWC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD0A/hjBEPkFD1hvvdp14eN6BuDwkuZEJE9lBW+kBJx8gIgLV5PuLUR9hOSWrQJu9OlkH9gauYYdQJBY7UauwDrcSc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/gensync_1.0.0-beta.1_1569727270326_0.4713080055178607"}, "_hasShrinkwrap": false}, "1.0.0-beta.2": {"name": "gens<PERSON>", "version": "1.0.0-beta.2", "license": "MIT", "description": "Allows users to use generators in order to write common functions that can be both sync or async.", "main": "index.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/loganfsmyth/gensync", "repository": {"type": "git", "url": "git+https://github.com/loganfsmyth/gensync.git"}, "scripts": {"test": "jest"}, "engines": {"node": ">=6.9.0"}, "keywords": ["async", "sync", "generators", "async-await", "callbacks"], "devDependencies": {"babel-core": "^6.26.3", "babel-preset-env": "^1.6.1", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^2.6.0", "flow-bin": "^0.71.0", "jest": "^22.4.3", "prettier": "^1.12.1"}, "gitHead": "a182868db47da283a1a044cb15bfd388697a632f", "bugs": {"url": "https://github.com/loganfsmyth/gensync/issues"}, "_id": "gensync@1.0.0-beta.2", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "shasum": "32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0", "tarball": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "fileCount": 7, "unpackedSize": 28891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmIZ8CRA9TVsSAnZWagAAbQsP/A6lZknPlD+2Ao64igWw\nnclQ1bMmEkKh8dJ4lj9b1dKy184kUsCQWN/TyE2ys4NlSoV5U0cK+tJrqH2p\nlwqu4dv7a2TSjrzWVDiyyRl5PPF4r5W9vLLPDSjBYAzFnUGxPjMWFwJk6iCW\nkYGvGlwK7gBtkn3lOuKMaRjG8Nf560pZGZ9AM+IMcfllYqthXDCYra1mYjAI\n+qnudON/4ou32DyUgvX/Jv2pLvJ/FWIwNIqJcRdX3kuc397Dsnz0gyRGuF9U\ncrDX1XoAO0DHuPnR+yT+w9md4S7U+a00hGgPrwX1iNz5dr0O8K9uaaMqxYLy\n514yKD1V6Bni8WCFq4bTDentZHIBlh9fwH+aiIWm9vKHYWpIzloNmZsSpkXx\ncJJCtBK+32JnmWxmOq8R7JY3Vv3mGVvLBJOGv5Sati+oDuL4+h3JeJDTSDg4\npJFJj4nH7yJAcw5O2IgompJVhEiLTaAZBxKOqFoHvZ8WnoiIxbE8YXvDmkod\n17sspBBzUyRXDFnZAEyCOATn9U9WankT/UnwgT4RHmfTy75TmwHicY0Yc8k1\nnTz3cX+BrMTcF3Bp7k4D3UGZDb9g6ewzbkVXx+qZaa0fGtdIP6UrjaaVKrRQ\n7YpOj/vfJIN7nEy+FbYAVpCTd6ipdb55oqeUZKfEYgnxY7dYufxPJEy4Dpln\nkoD1\r\n=ZtZG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHfGsHV56Mp/DwzEt3tpD/gpYMjdzduN507yghyYJyFQIhAPkDalGQofBClvMSR1HsCtvuk3OgPrb5yLlzuWukwSBz"}]}, "maintainers": [{"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/gensync_1.0.0-beta.2_1603831420300_0.46309518305706576"}, "_hasShrinkwrap": false}}, "readme": "# gensync\n\nThis module allows for developers to write common code that can share\nimplementation details, hiding whether an underlying request happens\nsynchronously or asynchronously. This is in contrast with many current Node\nAPIs which explicitly implement the same API twice, once with calls to\nsynchronous functions, and once with asynchronous functions.\n\nTake for example `fs.readFile` and `fs.readFileSync`, if you're writing an API\nthat loads a file and then performs a synchronous operation on the data, it\ncan be frustrating to maintain two parallel functions.\n\n\n## Example\n\n```js\nconst fs = require(\"fs\");\nconst gensync = require(\"gensync\");\n\nconst readFile = gensync({\n  sync: fs.readFileSync,\n  errback: fs.readFile,\n});\n\nconst myOperation = gensync(function* (filename) {\n  const code = yield* readFile(filename, \"utf8\");\n\n  return \"// some custom prefix\\n\" + code;\n});\n\n// Load and add the prefix synchronously:\nconst result = myOperation.sync(\"./some-file.js\");\n\n// Load and add the prefix asynchronously with promises:\nmyOperation.async(\"./some-file.js\").then(result => {\n\n});\n\n// Load and add the prefix asynchronously with promises:\nmyOperation.errback(\"./some-file.js\", (err, result) => {\n\n});\n```\n\nThis could even be exposed as your official API by doing\n```js\n// Using the common 'Sync' suffix for sync functions, and 'Async' suffix for\n// promise-returning versions.\nexports.myOperationSync = myOperation.sync;\nexports.myOperationAsync = myOperation.async;\nexports.myOperation = myOperation.errback;\n```\nor potentially expose one of the async versions as the default, with a\n`.sync` property on the function to expose the synchronous version.\n```js\nmodule.exports = myOperation.errback;\nmodule.exports.sync = myOperation.sync;\n````\n\n\n## API\n\n### gensync(generatorFnOrOptions)\n\nReturns a function that can be \"await\"-ed in another `gensync` generator\nfunction, or executed via\n\n* `.sync(...args)` - Returns the computed value, or throws.\n* `.async(...args)` - Returns a promise for the computed value.\n* `.errback(...args, (err, result) => {})` - Calls the callback with the computed value, or error.\n\n\n#### Passed a generator\n\nWraps the generator to populate the `.sync`/`.async`/`.errback` helpers above to\nallow for evaluation of the generator for the final value.\n\n##### Example\n\n```js\nconst readFile = function* () {\n  return 42;\n};\n\nconst readFileAndMore = gensync(function* (){\n  const val = yield* readFile();\n  return 42 + val;\n});\n\n// In general cases\nconst code = readFileAndMore.sync(\"./file.js\", \"utf8\");\nreadFileAndMore.async(\"./file.js\", \"utf8\").then(code => {})\nreadFileAndMore.errback(\"./file.js\", \"utf8\", (err, code) => {});\n\n// In a generator being called indirectly with .sync/.async/.errback\nconst code = yield* readFileAndMore(\"./file.js\", \"utf8\");\n```\n\n\n#### Passed an options object\n\n* `opts.sync`\n\n  Example: `(...args) => 4`\n\n  A function that will be called when `.sync()` is called on the `gensync()`\n  result, or when the result is passed to `yield*` in another generator that\n  is being run synchronously.\n\n  Also called for `.async()` calls if no async handlers are provided.\n\n* `opts.async`\n\n  Example: `async (...args) => 4`\n\n  A function that will be called when `.async()` or `.errback()` is called on\n  the `gensync()` result, or when the result is passed to `yield*` in another\n  generator that is being run asynchronously.\n\n* `opts.errback`\n\n  Example: `(...args, cb) => cb(null, 4)`\n\n  A function that will be called when `.async()` or `.errback()` is called on\n  the `gensync()` result, or when the result is passed to `yield*` in another\n  generator that is being run asynchronously.\n\n  This option allows for simpler compatibility with many existing Node APIs,\n  and also avoids introducing the extra even loop turns that promises introduce\n  to access the result value.\n\n* `opts.name`\n\n  Example: `\"readFile\"`\n\n  A string name to apply to the returned function. If no value is provided,\n  the name of `errback`/`async`/`sync` functions will be used, with any\n  `Sync` or `Async` suffix stripped off. If the callback is simply named\n  with ES6 inference (same name as the options property), the name is ignored.\n\n* `opts.arity`\n\n  Example: `4`\n\n  A number for the length to set on the returned function. If no value\n  is provided, the length will be carried over from the `sync` function's\n  `length` value.\n\n##### Example\n\n```js\nconst readFile = gensync({\n  sync: fs.readFileSync,\n  errback: fs.readFile,\n});\n\nconst code = readFile.sync(\"./file.js\", \"utf8\");\nreadFile.async(\"./file.js\", \"utf8\").then(code => {})\nreadFile.errback(\"./file.js\", \"utf8\", (err, code) => {});\n```\n\n\n### gensync.all(iterable)\n\n`Promise.all`-like combinator that works with an iterable of generator objects\nthat could be passed to `yield*` within a gensync generator.\n\n#### Example\n\n```js\nconst loadFiles = gensync(function* () {\n  return yield* gensync.all([\n    readFile(\"./one.js\"),\n    readFile(\"./two.js\"),\n    readFile(\"./three.js\"),\n  ]);\n});\n```\n\n\n### gensync.race(iterable)\n\n`Promise.race`-like combinator that works with an iterable of generator objects\nthat could be passed to `yield*` within a gensync generator.\n\n#### Example\n\n```js\nconst loadFiles = gensync(function* () {\n  return yield* gensync.race([\n    readFile(\"./one.js\"),\n    readFile(\"./two.js\"),\n    readFile(\"./three.js\"),\n  ]);\n});\n```\n", "maintainers": [{"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-18T07:31:53.380Z", "created": "2015-11-23T04:44:36.845Z", "0.1.0": "2015-11-23T04:44:36.845Z", "1.0.0-beta.0": "2018-05-07T00:19:39.584Z", "1.0.0-beta.1": "2019-09-29T03:21:10.461Z", "1.0.0-beta.2": "2020-10-27T20:43:40.443Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "readmeFilename": "README.md", "users": {"oakfang": true}, "keywords": ["async", "sync", "generators", "async-await", "callbacks"], "homepage": "https://github.com/loganfsmyth/gensync", "repository": {"type": "git", "url": "git+https://github.com/loganfsmyth/gensync.git"}, "bugs": {"url": "https://github.com/loganfsmyth/gensync/issues"}}