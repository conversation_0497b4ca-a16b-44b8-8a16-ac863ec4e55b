{"_id": "opusscript", "_rev": "16-57d215bb45987d512559f6f992e54764", "name": "opusscript", "description": "JS bindings for libopus 1.4, ported with emscripten", "dist-tags": {"latest": "0.1.1"}, "versions": {"0.0.1": {"name": "opusscript", "version": "0.0.1", "description": "JS bindings for libopus 1.1.3, ported with emscripten", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "gitHead": "58075bd98820f459d10f197da88f3ebad6e4d04e", "_id": "opusscript@0.0.1", "scripts": {}, "_shasum": "c8f61d4301b2942ee9ddf68b075b3e373b7943dd", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "6.3.1", "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c8f61d4301b2942ee9ddf68b075b3e373b7943dd", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.0.1.tgz", "integrity": "sha512-c0oNzCVszwAm4TNB6GSHhj6xMUYrkMeU/sZ2SDD4T3yZ8cyuRBLVmkM3Ny68AGsioUVdhyuKlYBx6DimEsK7qQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCULG++0nR+L7t86gP4Kym7nD4B4BlFYujh8n+ilt0exwIgNA6WU40feOO3gPUZOEZJHSWCnJoV/HhE9lLpV08wQ3M="}]}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/opusscript-0.0.1.tgz_1472367307462_0.14161026617512107"}, "directories": {}}, "0.0.2": {"name": "opusscript", "version": "0.0.2", "description": "JS bindings for libopus 1.1.3, ported with emscripten", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "gitHead": "2c189cc008fd74bcabc39c55f1ae9e1e9efc5113", "_id": "opusscript@0.0.2", "scripts": {}, "_shasum": "5fdd7274c13991fd96f64320e69ad56f5b0b9bcd", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.0", "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5fdd7274c13991fd96f64320e69ad56f5b0b9bcd", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.0.2.tgz", "integrity": "sha512-MEc5MmdZKIbZfU87jgPCGUJMxE9azcQ+a8jwUbPUMSUxgRwCvhx2PSRw8bIevEIB3zs7X6jbc8+1TGLfW6gVZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQDxOPNAUTDPW2XuUt9dxre0a9UREAI1Ah7ZRRsaKzXAIhAPZWWdrY0gQDAaXMgefByyOg1X9ACzFyL+AiI5VfUeFs"}]}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/opusscript-0.0.2.tgz_1485393950765_0.4805585779249668"}, "directories": {}}, "0.0.3": {"name": "opusscript", "version": "0.0.3", "description": "JS bindings for libopus 1.1.4, ported with emscripten", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "gitHead": "4ea577783119a6c963f5589ca7fbdecd69edfd4d", "_id": "opusscript@0.0.3", "scripts": {}, "_shasum": "ce46717fc8d6f901c5191e69485c889a036a8674", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.0", "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ce46717fc8d6f901c5191e69485c889a036a8674", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.0.3.tgz", "integrity": "sha512-sSChJ0YffV31XiIRoIuoe3BYaV9139N7KWRRwLgA5f5AX9RVsGadH4JKykVMysD0XSftr+WxyLi6nGsW8nGlYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMK7DCxkhbcVLXa8fS8m2my/rWQaLWxa+EuWY85+aZXwIgXAvtMQfn/T5KAkoEcX+6zL9Y02VX+4mEV2O1pFZx0ic="}]}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/opusscript-0.0.3.tgz_1489297127417_0.7726756311021745"}, "directories": {}}, "0.0.4": {"name": "opusscript", "version": "0.0.4", "description": "JS bindings for libopus 1.2.1, ported with emscripten", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "gitHead": "07df3c120f316cd496a0de7eb932ef121b709e5b", "_id": "opusscript@0.0.4", "_npmVersion": "5.4.2", "_nodeVersion": "8.4.0", "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-b<PERSON>ZFE2lhUJYQD5yfTFO4RhbRZ937x6hRwBC1YoGacT35bwDVwKFP1+amU8NYfZL/v4EU7ZTU3INTqzYAnuP7Q==", "shasum": "c718edcfdcd2a1f55fadb266dd07268d4a46afde", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.0.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDxvT5fWrbY3ia4oEDTtjclIuCfsoSj7EWwc5aqCoMHjQIhAJleW9q06i0Y4pmwnSLVJ3//7PYWvXNdDXo8sFVq5ugb"}]}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opusscript-0.0.4.tgz_1507073815237_0.11946299322880805"}, "directories": {}}, "0.0.6": {"name": "opusscript", "version": "0.0.6", "description": "JS bindings for libopus 1.2.1, ported with emscripten", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "gitHead": "01fee75902bf50439ac36ee3c4027ba5021f51f7", "_id": "opusscript@0.0.6", "_npmVersion": "5.6.0", "_nodeVersion": "8.7.0", "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-F7nx1SWZCD5Rq2W+5Fx39HlkRkz/5Zqt0LglEB9uHexk8HjedDEiM+u/Y2rBfDFcS/0uQIWu2lJhw+Gjsta+cA==", "shasum": "cf492fc5fb2c819af296ae02eaa3cf210433c9ba", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.0.6.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFumC29Qv3WQIroj9m+IBqNOfE9iGEcJwX1E37b8KBCwAiBhKpx/vxDOaxHU5Zjw3oQs8u0Axt+V8Z9Ft+lwQyf89Q=="}]}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opusscript-0.0.6.tgz_1513196509394_0.31474710907787085"}, "directories": {}, "deprecated": "no longer supported"}, "0.0.7": {"name": "opusscript", "version": "0.0.7", "description": "JS bindings for libopus 1.3.1, ported with emscripten", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "gitHead": "3994e53f9a25e2f80b9d89f77d948166c2a7a5eb", "_id": "opusscript@0.0.7", "_nodeVersion": "12.5.0", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-DcBadTdYTUuH9zQtepsLjQn4Ll6rs3dmeFvN+SD0ThPnxRBRm/WC1zXWPg+wgAJimB784gdZvUMA57gDP7FdVg==", "shasum": "7dd7ec55b302d26bf588e6fc3feb090b8c7da856", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.0.7.tgz", "fileCount": 9, "unpackedSize": 1141853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTJ0fCRA9TVsSAnZWagAAmaMP/jGUbtEE9Dq0ZhWFEIMQ\ntDDsYVwt5CyYb5NMFh4LN+WGfs7RLeH4V38i1w3HxXAVo9QDFXAq4LhGKAsE\nJExBxLFP5ObjRzYX1zBDZP5PzN0iH+Weg6xFSsPnPHtiIKE+RaenOM2ev9pi\nlHvnccYcK3WF4+zMl2UnR4tJyK0twamzLebvGGQy8S42w+tHY00dOE2q00il\nne6iDME4QmDKU8OSFCb47iBZYHttEHvfn4Yv1vYqoGS61c2cxVASF7AxPCmu\n4tZikW6U9vzZeL2jF+ihfKtlcRyPIeXE99CxgSamXvDpw9Hbq9+CahR3tlsE\nhX+e1J8diwXPj5A+qsjXQyHDhaq/oNVFTGLGupBKatauuz9XmR/zNedhQiaZ\ngmq+yU61wvFzmiWYv59Gcn/kXy3WhNntj/am+C+SsBRPb1UP/98rVCH+BVn7\ndwOX4/VF8Pc/5f4/padJ4tQMVYlCJA8maUWdNCoZ7d1QUvnFrc+WWb+EHOth\nDjYoTcIBkbCubftSvzLoQX7RbEHlnUD3minGy4KdBV+VbCx4nU9VxtrH1/lb\nR+n8EzK2suee1knqg6nPXo1KyF+DP7Lw9NgkRLGklIhWSuyX2Ey9JF3zOjdg\ngIcMi7DEUWshPHmKg/Ai4xSFOdQ2EHC3gzM2vCiKvc2LjZ4dHfLtnrbB62JG\nblbB\r\n=+dyg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+6zWYrOlTOI4OaLMcjXdSq0Y0oacdTDtjKz/spO6DsAIhAIMCs1yDCylKbMIcv+/gxyVnlppgIAXOe1cRE5Gt/Vdd"}]}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opusscript_0.0.7_1565302046323_0.6415657446186145"}, "_hasShrinkwrap": false}, "0.0.8": {"name": "opusscript", "version": "0.0.8", "description": "JS bindings for libopus 1.3.1, ported with emscripten", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "browser": {"fs": false, "path": false}, "gitHead": "b9cf55f521d44f531a9faa8823f5cc8ba6f99fcd", "_id": "opusscript@0.0.8", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-VSTi1aWFuCkRCVq+tx/BQ5q9fMnQ9pVZ3JU4UHKqTkf0ED3fKEPdr+gKAAl3IA2hj9rrP6iyq3hlcJq3HELtNQ==", "shasum": "00b49e81281b4d99092d013b1812af8654bd0a87", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.0.8.tgz", "fileCount": 9, "unpackedSize": 840787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNkX9CRA9TVsSAnZWagAAGpMP/1UoqWuqul2mNFEFFeBo\n4oZraHx1k0XgzT+o9m0UsY69xZerWTgughbBBQ3dSABtNLxTEKDZ3WQRGUhB\n8Vn8b6oqGk7/zT43nCYDlueOwA9hOZf4JVY4ArhVRiqD8dzxkABYvTtUeCVX\notqSPhZPuJDMsJ88gPwLg8Zeh+MB0k35kHpgnrJxR3yHMeWvx0tDrXR8bTg8\nxpgkx66Evs0NdOK4HlxSy7VfnGGadodhtYBOkEm/6n4641GngGgHL69mdfpy\n6X3TjISae0/E8HmiNq4Jjvr6g1/PyV9KZZBkXuU0DMywOAUPw+2crhagWPE6\n+rUWth4JiqjBqBAKFH1ZYKtMsAx609XSrkNO8qZb9jXEZcHbukbAbLZ/YTrR\nop8zYbdRzDkVxwDoAOgqfSDMhxAEwhMwCYIx/ovBXhSkZVGGyD23Mpi78XB4\nNbuv5E19ITsYvPo12vgVQSOmK8ulSY2vbCcuz0fFHiQ4hSVUMMkPB4WAfodR\n+y2lrjlWaVmnwBSlhNpSVaSUcDYXsT7jIZ2Ma88iHLqVbXZcNPleQfpLKHhv\nyAt0ZguyJSvgiPe+uginLn9epH/+qLspqJUeOjaQjmXdf+GI9ALiH5jTgsB0\nILryf+A+tm9hDSsVSpAmVWFJbF33ZUEmoqp3IRIcVC9VoCHMlV7ooWC0ufRK\n3vxA\r\n=EoIm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAs2ix3GXwILGJ9k6TkFvofrIPGYPkJC/cRvDju6CWYQIgXvGYjcT29e4AZMNRZsK8fWzOYyN4mN1zRPMD1JnkHTY="}]}, "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opusscript_0.0.8_1614169597279_0.5052963371240713"}, "_hasShrinkwrap": false}, "0.1.0": {"name": "opusscript", "version": "0.1.0", "description": "JS bindings for libopus 1.4, ported with emscripten", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "browser": {"fs": false, "path": false}, "gitHead": "2b7963c759d21eb1879497c1d2b06f85e5217306", "_id": "opusscript@0.1.0", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-Jc1d2u7AutNcUCoSpshhStW4CVAZXqh8n832ZTFUwZowof+xq2JW4umsWwAUOdtczn2z0UJMkUoTYBFT8qto9g==", "shasum": "0d8db7b5202b91c75d9c511f2621c5c93e9b0055", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.1.0.tgz", "fileCount": 10, "unpackedSize": 1325791, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDbON38EQ/mlfz+Irm6xc67nuwroG53ITg9u8X9umPQ7AIgLswg48RHAlfWGNQsNqrrd5m5fmyXoL45UzQp159LGy8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRp3ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQyQ/+NDdYmZqe9NAhJaRbPGQ1+wXU9PR26gOzMkIcUjakzGCocH2t\r\nnfL2xsTdSFeGN+p0OponblaEugBpdo54noCDov662RgIccicRn/soPvz3rSv\r\nllacYY6/t/pQpAOYRmuRpRHMhaQQpbXeULrMwXYdHn4wYobEJnKQgvZqv1gL\r\nB2kwStfZf8XtFSKqaFkEjE1dKhFnmqvQqXIHkPTC/Jk3IHtoxM+zL9oXGEOe\r\nufnlmj8z93LU46uhkatchjosjONgVvhA/JV2giMx0hHUZEuu+3ARG8dHHJG5\r\ndRya15Kr66wCL11r+u74L85xcVTppfKAf1yYJpCP5tuh4IrKJt98+Cixw9Ts\r\ns2SSrorZFBuHSwCpqpyCWl9+WL2gDbGr4sfQG5VBqI/spf6FUE7L0GOsxH/u\r\n/p8X5JNRd2y5X+b8w8D2ehDE5t+nraz8iN39ofUJFWW4PnKPelezq2xmlo4B\r\nRl5j+2PY2CQyk+XQ2w0GSFuNRdRSRzoj4y7PMSLoWoDWvX1V29He6rrBxtzA\r\nY6GLMKH99SMSOT8kSlKBO5IAEF2H54An8PeI29RBr3Kpn6cALQOp16DfbToj\r\nKKxdVZfQhiuqLEI1/007NTcQH+HkDlNSztW4KtTRMKs48PCtQqUMbugfv7dB\r\n1OMLeanoo3A3i6i+yWr/QY3GKV2M2fi4Qms=\r\n=P8zA\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opusscript_0.1.0_1682349529566_0.16499540658059675"}, "_hasShrinkwrap": false}, "0.1.1": {"name": "opusscript", "version": "0.1.1", "description": "JS bindings for libopus 1.4, ported with emscripten", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "keywords": ["libopus", "encoder", "emscripten"], "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "browser": {"fs": false, "path": false}, "_id": "opusscript@0.1.1", "gitHead": "07ae74db7385d6d1be981d929f2f2f2b7e6b8449", "_nodeVersion": "20.8.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-mL0fZZOUnXdZ78woRXp18lApwpp0lF5tozJOD1Wut0dgrA9WuQTgSels/CSmFleaAZrJi/nci5KOVtbuxeWoQA==", "shasum": "322c126b753ddbf9b0b209d0060a432805b6a34d", "tarball": "https://registry.npmjs.org/opusscript/-/opusscript-0.1.1.tgz", "fileCount": 9, "unpackedSize": 947589, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIClQHs+c2XyrAADs5g8+GEW5m/9rTY7UP6m8C2HB+SE5AiBxY5YLDYQGRFfs7eVNS8uv+05eCECJX2PYatoGi371Hw=="}]}, "_npmUser": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/opusscript_0.1.1_1696915582767_0.4713224837299119"}, "_hasShrinkwrap": false}}, "readme": "# OpusScript\n\nJS bindings for libopus 1.4, ported with Emscripten.\n\n----\n\n## Usage\n\n```js\nvar OpusScript = require(\"opusscript\");\n\n// 48kHz sampling rate, 20ms frame duration, stereo audio (2 channels)\nvar samplingRate = 48000;\nvar frameDuration = 20;\nvar channels = 2;\n\n// Optimize encoding for audio. Available applications are VOIP, AUDIO, and RESTRICTED_LOWDELAY\nvar encoder = new OpusScript(samplingRate, channels, OpusScript.Application.AUDIO);\n\nvar frameSize = samplingRate * frameDuration / 1000;\n\n// Get PCM data from somewhere and encode it into opus\nvar pcmData = new Buffer(pcmSource);\nvar encodedPacket = encoder.encode(pcmData, frameSize);\n\n// Decode the opus packet back into PCM\nvar decodedPacket = encoder.decode(encodedPacket);\n\n// Delete the encoder when finished with it (Emscripten does not automatically call C++ object destructors)\nencoder.delete();\n```\n\n## Note: WASM\n\nIf your environment doesn't support WASM, you can try the JS-only module. Do note that the JS-only version barely has optimizations due to compiler/toolchain limitations, and should only be used as a last resort.\n\n```js\nvar encoder = new OpusScript(samplingRate, channels, OpusScript.Application.AUDIO, {\n  wasm: false\n});\n```\n\n## Note: TypeScript\n\nSince this module wasn't written for TypeScript, you need to use `import = require` syntax.\n\n```ts\n// Import using:\nimport OpusScript = require(\"opusscript\");\n\n// and NOT:\nimport OpusScript from \"opusscript\";\n```\n", "maintainers": [{"name": "a<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-10-10T05:26:23.196Z", "created": "2016-08-28T06:55:08.478Z", "0.0.1": "2016-08-28T06:55:08.478Z", "0.0.2": "2017-01-26T01:25:53.086Z", "0.0.3": "2017-03-12T05:38:49.545Z", "0.0.4": "2017-10-03T23:36:55.362Z", "0.0.5": "2017-12-13T20:10:05.312Z", "0.0.6": "2017-12-13T20:21:49.507Z", "0.0.7": "2019-08-08T22:07:26.494Z", "0.0.8": "2021-02-24T12:26:37.515Z", "0.1.0": "2023-04-24T15:18:49.882Z", "0.1.1": "2023-10-10T05:26:23.022Z"}, "homepage": "https://github.com/abalabahaha/opusscript#readme", "keywords": ["libopus", "encoder", "emscripten"], "repository": {"type": "git", "url": "git+https://github.com/abalabahaha/opusscript.git"}, "author": {"name": "a<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/abalabahaha/opusscript/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"legacygaming": true}}